{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-switch/u-switch.vue?5a7f", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-switch/u-switch.vue?b0a8", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-switch/u-switch.vue?2659", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-switch/u-switch.vue?48de", "uni-app:///components/uview-ui/components/u-switch/u-switch.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-switch/u-switch.vue?80b8", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-switch/u-switch.vue?b6d8"], "names": ["name", "props", "loading", "type", "default", "disabled", "size", "activeColor", "inactiveColor", "value", "vibrateShort", "activeValue", "inactiveValue", "data", "computed", "switchStyle", "style", "loadingColor", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACapxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACA,QAEA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA+7C,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAn9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-switch/u-switch.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-switch.vue?vue&type=template&id=71c1259a&scoped=true&\"\nvar renderjs\nimport script from \"./u-switch.vue?vue&type=script&lang=js&\"\nexport * from \"./u-switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-switch.vue?vue&type=style&index=0&id=71c1259a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"71c1259a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-switch/u-switch.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=template&id=71c1259a&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.switchStyle])\n  var g0 = _vm.$u.addUnit(this.size)\n  var g1 = _vm.$u.addUnit(this.size)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-switch\" :class=\"[value == true ? 'u-switch--on' : '', disabled ? 'u-switch--disabled' : '']\" @tap=\"onClick\"\r\n\t :style=\"[switchStyle]\">\r\n\t\t<view class=\"u-switch__node node-class\" :style=\"{\r\n\t\t\twidth: $u.addUnit(this.size),\r\n\t\t\theight: $u.addUnit(this.size)\r\n\t\t}\">\r\n\t\t\t<u-loading :show=\"loading\" class=\"u-switch__loading\" :size=\"size * 0.6\" :color=\"loadingColor\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * switch 开关选择器\r\n\t * @description 选择开关一般用于只有两个选择，且只能选其一的场景。\r\n\t * @tutorial https://www.uviewui.com/components/switch.html\r\n\t * @property {Boolean} loading 是否处于加载中（默认false）\r\n\t * @property {Boolean} disabled 是否禁用（默认false）\r\n\t * @property {String Number} size 开关尺寸，单位rpx（默认50）\r\n\t * @property {String} active-color 打开时的背景色（默认#2979ff）\r\n\t * @property {Boolean} inactive-color 关闭时的背景色（默认#ffffff）\r\n\t * @property {Boolean | Number | String} active-value 打开选择器时通过change事件发出的值（默认true）\r\n\t * @property {Boolean | Number | String} inactive-value 关闭选择器时通过change事件发出的值（默认false）\r\n\t * @event {Function} change 在switch打开或关闭时触发\r\n\t * @example <u-switch v-model=\"checked\" active-color=\"red\" inactive-color=\"#eee\"></u-switch>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-switch\",\r\n\t\tprops: {\r\n\t\t\t// 是否为加载中状态\r\n\t\t\tloading: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否为禁用装填\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 开关尺寸，单位rpx\r\n\t\t\tsize: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 50\r\n\t\t\t},\r\n\t\t\t// 打开时的背景颜色\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#2979ff'\r\n\t\t\t},\r\n\t\t\t// 关闭时的背景颜色\r\n\t\t\tinactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// 通过v-model双向绑定的值\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否使手机发生短促震动，目前只在iOS的微信小程序有效(2020-05-06)\r\n\t\t\tvibrateShort: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 打开选择器时的值\r\n\t\t\tactiveValue: {\r\n\t\t\t\ttype: [Number, String, Boolean],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 关闭选择器时的值\r\n\t\t\tinactiveValue: {\r\n\t\t\t\ttype: [Number, String, Boolean],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tswitchStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tstyle.fontSize = this.size + 'rpx';\r\n\t\t\t\tstyle.backgroundColor = this.value ? this.activeColor : this.inactiveColor;\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\tloadingColor() {\r\n\t\t\t\treturn this.value ? this.activeColor : null;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonClick() {\r\n\t\t\t\tif (!this.disabled && !this.loading) {\r\n\t\t\t\t\t// 使手机产生短促震动，微信小程序有效，APP(HX 2.6.8)和H5无效\r\n\t\t\t\t\tif(this.vibrateShort) uni.vibrateShort();\r\n\t\t\t\t\tthis.$emit('input', !this.value);\r\n\t\t\t\t\t// 放到下一个生命周期，因为双向绑定的value修改父组件状态需要时间，且是异步的\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.$emit('change', this.value ? this.activeValue : this.inactiveValue);\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-switch {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-block;\r\n\t\t/* #endif */\r\n\t\tbox-sizing: initial;\r\n\t\twidth: 2em;\r\n\t\theight: 1em;\r\n\t\tbackground-color: #fff;\r\n\t\tborder: 1px solid rgba(0, 0, 0, 0.1);\r\n\t\tborder-radius: 1em;\r\n\t\ttransition: background-color 0.3s;\r\n\t\tfont-size: 50rpx;\r\n\t}\r\n\r\n\t.u-switch__node {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tborder-radius: 100%;\r\n\t\tz-index: 1;\r\n\t\tbackground-color: #fff;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.1), 0 3px 3px 0 rgba(0, 0, 0, 0.05);\r\n\t\tbox-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.1), 0 3px 3px 0 rgba(0, 0, 0, 0.05);\r\n\t\ttransition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);\r\n\t\ttransition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05), -webkit-transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);\r\n\t\ttransition: transform cubic-bezier(0.3, 1.05, 0.4, 1.05);\r\n\t\ttransition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05)\r\n\t}\r\n\r\n\t.u-switch__loading {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.u-switch--on {\r\n\t\tbackground-color: #1989fa;\r\n\t}\r\n\r\n\t.u-switch--on .u-switch__node {\r\n\t\ttransform: translateX(100%);\r\n\t}\r\n\r\n\t.u-switch--disabled {\r\n\t\topacity: 0.4;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=style&index=0&id=71c1259a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=style&index=0&id=71c1259a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760724547\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}