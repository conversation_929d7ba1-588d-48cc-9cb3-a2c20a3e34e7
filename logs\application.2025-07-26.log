2025-07-26 17:23:38.700 [main] INFO  c.y.springbootinit.MainApplication - Starting MainApplication v0.0.1-SNAPSHOT using Java 17 on 小伍 with PID 32396 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\springboot-init-0.0.1-SNAPSHOT.jar started by 16935 in D:\求职之路\Fox\用户端\用户端)
2025-07-26 17:23:38.706 [main] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "dev"
2025-07-26 17:23:41.263 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-26 17:23:41.304 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. <PERSON> already defined with the same name!
2025-07-26 17:23:41.308 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.309 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.309 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.309 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.309 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.310 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.310 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.310 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.311 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.311 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.311 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.311 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.312 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.312 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.312 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.313 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.313 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.314 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.314 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.314 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.314 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.315 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.315 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.315 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.315 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.316 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.316 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.316 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:23:41.317 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-26 17:23:43.187 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-07-26 17:23:43.218 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-26 17:23:43.220 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 17:23:43.221 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-26 17:23:43.413 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-26 17:23:43.418 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4579 ms
2025-07-26 17:23:44.594 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-26 17:23:45.713 [main] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-26 17:23:45.913 [main] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-26 17:23:45.930 [main] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-26 17:23:46.068 [main] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-26 17:23:46.105 [main] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-26 17:23:47.081 [main] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-26 17:23:48.889 [main] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-26 17:23:49.014 [main] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-26 17:23:49.639 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 17:23:50.006 [main] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-26 17:23:50.181 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-26 17:23:50.241 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api'
2025-07-26 17:23:50.242 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-26 17:23:50.248 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-26 17:23:50.385 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-26 17:23:50.774 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-26 17:23:50.998 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-26 17:23:51.109 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-26 17:23:51.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-26 17:23:51.115 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-26 17:23:51.118 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-26 17:23:51.120 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-26 17:23:51.470 [main] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-26 17:23:51.490 [main] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 14.323 seconds (JVM running for 16.359)
2025-07-26 17:36:44.316 [main] INFO  c.y.springbootinit.MainApplication - Starting MainApplication v0.0.1-SNAPSHOT using Java 17 on 小伍 with PID 29992 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\springboot-init-0.0.1-SNAPSHOT.jar started by 16935 in D:\求职之路\Fox\用户端\用户端)
2025-07-26 17:36:44.322 [main] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "dev"
2025-07-26 17:36:46.945 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-26 17:36:46.983 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.983 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.983 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.983 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.983 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.984 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.984 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.984 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.984 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.985 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.985 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.985 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.985 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.985 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.985 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.986 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.986 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.986 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.987 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.987 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.987 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.988 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.988 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.988 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.988 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.989 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.989 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.989 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.989 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:36:46.990 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-26 17:36:48.787 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-07-26 17:36:48.817 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-26 17:36:48.819 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 17:36:48.819 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-26 17:36:48.987 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-26 17:36:48.987 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4517 ms
2025-07-26 17:36:50.083 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-26 17:36:51.254 [main] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-26 17:36:51.418 [main] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-26 17:36:51.433 [main] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-26 17:36:51.577 [main] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-26 17:36:51.614 [main] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-26 17:36:52.654 [main] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-26 17:36:54.358 [main] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-26 17:36:54.467 [main] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-26 17:36:55.073 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 17:36:55.452 [main] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-26 17:36:55.657 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-26 17:36:55.713 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api'
2025-07-26 17:36:55.715 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-26 17:36:55.720 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-26 17:36:55.849 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-26 17:36:56.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-26 17:36:56.529 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-26 17:36:56.676 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-26 17:36:56.679 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-26 17:36:56.683 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-26 17:36:56.686 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-26 17:36:56.689 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-26 17:36:57.109 [main] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-26 17:36:57.128 [main] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 14.267 seconds (JVM running for 15.582)
2025-07-26 17:58:33.299 [main] INFO  c.y.springbootinit.MainApplication - Starting MainApplication v0.0.1-SNAPSHOT using Java 17 on 小伍 with PID 23864 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\springboot-init-0.0.1-SNAPSHOT.jar started by 16935 in D:\求职之路\Fox\用户端\用户端)
2025-07-26 17:58:33.306 [main] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "dev"
2025-07-26 17:58:35.595 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-26 17:58:35.643 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.644 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.645 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.645 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.645 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.647 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.647 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.647 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.648 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.648 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.648 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.649 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.649 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.650 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.650 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.650 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.651 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.651 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.651 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.652 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.652 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.652 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.653 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.653 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.653 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.653 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.654 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.654 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.654 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-26 17:58:35.656 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-26 17:58:37.358 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-07-26 17:58:37.384 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-26 17:58:37.386 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-26 17:58:37.386 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-26 17:58:37.598 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-26 17:58:37.598 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4155 ms
2025-07-26 17:58:38.624 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-26 17:58:39.627 [main] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-26 17:58:39.789 [main] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-26 17:58:39.803 [main] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-26 17:58:39.940 [main] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-26 17:58:39.974 [main] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-26 17:58:40.860 [main] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-26 17:58:42.072 [main] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-26 17:58:42.141 [main] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-26 17:58:42.598 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 17:58:42.858 [main] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-26 17:58:42.966 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-26 17:58:43.013 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api'
2025-07-26 17:58:43.015 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-26 17:58:43.021 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-26 17:58:43.114 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-26 17:58:43.476 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-26 17:58:43.638 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-26 17:58:43.711 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-26 17:58:43.713 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-26 17:58:43.715 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-26 17:58:43.717 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-26 17:58:43.718 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-26 17:58:44.090 [main] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-26 17:58:44.116 [main] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 11.761 seconds (JVM running for 13.001)
2025-07-26 23:59:59.914 [pool-1-thread-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
