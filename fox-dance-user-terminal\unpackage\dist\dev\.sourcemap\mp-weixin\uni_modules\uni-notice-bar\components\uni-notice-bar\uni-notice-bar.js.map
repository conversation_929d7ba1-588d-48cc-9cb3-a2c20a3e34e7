{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue?f82c", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue?b8e3", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue?3830", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue?6566", "uni-app:///uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue?26a2", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue?8944"], "names": ["name", "emits", "props", "text", "type", "default", "moreText", "backgroundColor", "speed", "color", "fontSize", "moreColor", "single", "scrollable", "showIcon", "showGetMore", "showClose", "data", "textWidth", "boxWidth", "wrapWidth", "webviewHide", "elId", "elIdBox", "show", "animationDuration", "animationPlayState", "animationDelay", "watch", "computed", "isShowGetMore", "isShowClose", "mounted", "methods", "initSize", "uni", "in", "select", "boundingClientRect", "exec", "resolve", "query", "Promise", "setTimeout", "loopAnimation", "clickMore", "close", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAswB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsD1xB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAoBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACA;MACAZ;MACAC;IACA;EACA;EACAY;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MAIAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAzB;MACA;IACA;EACA;EACA0B;IACAC;MACA;IACA;IACAC;MACA,kEACA;IACA;EACA;EACAC;IAAA;IAYA;MACA;IACA;EACA;EAMAC;IACAC;MAAA;MACA;QAEA;UACAf;UACAD;QACA;UACAiB,0BAEAC,WAEAC,gCACAC,qBACAC;YACA;YACAC;UACA;QACA;QACA;UACAL,0BAEAC,WAEAC,mCACAC,qBACAC;YACA;YACAC;UACA;QACA;QACAC;QACAA;QACAC;UACA;UACA;UACAC;YACA;UACA;QACA;MA+BA;IAQA;IACAC,yCAwBA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9SA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-notice-bar.vue?vue&type=template&id=a1596656&scoped=true&\"\nvar renderjs\nimport script from \"./uni-notice-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-notice-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-notice-bar.vue?vue&type=style&index=0&id=a1596656&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a1596656\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-notice-bar.vue?vue&type=template&id=a1596656&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.show && _vm.isShowGetMore ? _vm.moreText.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-notice-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-notice-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"show\" class=\"uni-noticebar\" :style=\"{ backgroundColor }\" @click=\"onClick\">\r\n\t\t<uni-icons v-if=\"showIcon === true || showIcon === 'true'\" class=\"uni-noticebar-icon\" type=\"sound\"\r\n\t\t\t:color=\"color\" :size=\"fontSize * 1.5\" />\r\n\t\t<view ref=\"textBox\" class=\"uni-noticebar__content-wrapper\"\r\n\t\t\t:class=\"{\r\n\t\t\t\t'uni-noticebar__content-wrapper--scrollable': scrollable,\r\n\t\t\t\t'uni-noticebar__content-wrapper--single': !scrollable && (single || moreText)\r\n\t\t\t}\"\r\n\t\t\t:style=\"{ height: scrollable ? fontSize * 1.5 + 'px' : 'auto' }\"\r\n\t\t>\r\n\t\t\t<view :id=\"elIdBox\" class=\"uni-noticebar__content\"\r\n\t\t\t\t:class=\"{\r\n\t\t\t\t\t'uni-noticebar__content--scrollable': scrollable,\r\n\t\t\t\t\t'uni-noticebar__content--single': !scrollable && (single || moreText)\r\n\t\t\t\t}\"\r\n\t\t\t>\r\n\t\t\t\t<text :id=\"elId\" ref=\"animationEle\" class=\"uni-noticebar__content-text\" \r\n\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t'uni-noticebar__content-text--scrollable': scrollable,\r\n\t\t\t\t\t\t'uni-noticebar__content-text--single': !scrollable && (single || showGetMore)\r\n\t\t\t\t\t}\" \r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\tcolor: color,\r\n\t\t\t\t\t\tfontSize: fontSize + 'px',\r\n\t\t\t\t\t\tlineHeight: fontSize * 1.5 + 'px',\r\n\t\t\t\t\t\twidth: wrapWidth + 'px',\r\n\t\t\t\t\t\t'animationDuration': animationDuration,\r\n\t\t\t\t\t\t'-webkit-animationDuration': animationDuration,\r\n\t\t\t\t\t\tanimationPlayState: webviewHide ? 'paused' : animationPlayState,\r\n\t\t\t\t\t\t'-webkit-animationPlayState': webviewHide ? 'paused' : animationPlayState,\r\n\t\t\t\t\t\tanimationDelay: animationDelay,\r\n\t\t\t\t\t\t'-webkit-animationDelay': animationDelay\r\n\t\t\t\t\t}\"\r\n\t\t\t\t>{{text}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"isShowGetMore\" class=\"uni-noticebar__more uni-cursor-point\"\r\n\t\t\t@click=\"clickMore\">\r\n\t\t\t<text v-if=\"moreText.length > 0\" :style=\"{ color: moreColor, fontSize: fontSize + 'px' }\">{{ moreText }}</text>\r\n\t\t\t<uni-icons v-else type=\"right\" :color=\"moreColor\" :size=\"fontSize * 1.1\" />\r\n\t\t</view>\r\n\t\t<view class=\"uni-noticebar-close uni-cursor-point\" v-if=\"isShowClose\">\r\n\t\t\t<uni-icons type=\"closeempty\" :color=\"color\" :size=\"fontSize * 1.1\" @click=\"close\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = weex.requireModule('dom');\r\n\tconst animation = weex.requireModule('animation');\r\n\t// #endif\r\n\r\n\t/**\r\n\t * NoticeBar 自定义导航栏\r\n\t * @description 通告栏组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=30\r\n\t * @property {Number} speed 文字滚动的速度，默认100px/秒\r\n\t * @property {String} text 显示文字\r\n\t * @property {String} backgroundColor 背景颜色\r\n\t * @property {String} color 文字颜色\r\n\t * @property {String} moreColor 查看更多文字的颜色\r\n\t * @property {String} moreText 设置“查看更多”的文本\r\n\t * @property {Boolean} single = [true|false] 是否单行\r\n\t * @property {Boolean} scrollable = [true|false] 是否滚动，为true时，NoticeBar为单行\r\n\t * @property {Boolean} showIcon = [true|false] 是否显示左侧喇叭图标\r\n\t * @property {Boolean} showClose = [true|false] 是否显示左侧关闭按钮\r\n\t * @property {Boolean} showGetMore = [true|false] 是否显示右侧查看更多图标，为true时，NoticeBar为单行\r\n\t * @event {Function} click 点击 NoticeBar 触发事件\r\n\t * @event {Function} close 关闭 NoticeBar 触发事件\r\n\t * @event {Function} getmore 点击”查看更多“时触发事件\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'UniNoticeBar',\r\n\t\temits: ['click', 'getmore', 'close'],\r\n\t\tprops: {\r\n\t\t\ttext: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmoreText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#FFF9EA'\r\n\t\t\t},\r\n\t\t\tspeed: {\r\n\t\t\t\t// 默认1s滚动100px\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#FF9A43'\r\n\t\t\t},\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 14\r\n\t\t\t},\r\n\t\t\tmoreColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#FF9A43'\r\n\t\t\t},\r\n\t\t\tsingle: {\r\n\t\t\t\t// 是否单行\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tscrollable: {\r\n\t\t\t\t// 是否滚动，添加后控制单行效果取消\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshowIcon: {\r\n\t\t\t\t// 是否显示左侧icon\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshowGetMore: {\r\n\t\t\t\t// 是否显示右侧查看更多\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshowClose: {\r\n\t\t\t\t// 是否显示左侧关闭按钮\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\tconst elId = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`\r\n\t\t\tconst elIdBox = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`\r\n\t\t\treturn {\r\n\t\t\t\ttextWidth: 0,\r\n\t\t\t\tboxWidth: 0,\r\n\t\t\t\twrapWidth: '',\r\n\t\t\t\twebviewHide: false,\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tstopAnimation: false,\r\n\t\t\t\t// #endif\r\n\t\t\t\telId: elId,\r\n\t\t\t\telIdBox: elIdBox,\r\n\t\t\t\tshow: true,\r\n\t\t\t\tanimationDuration: 'none',\r\n\t\t\t\tanimationPlayState: 'paused',\r\n\t\t\t\tanimationDelay: '0s'\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\ttext:function(newValue,oldValue){\r\n\t\t\t\tthis.initSize();\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisShowGetMore() {\r\n\t\t\t\treturn this.showGetMore === true || this.showGetMore === 'true'\r\n\t\t\t},\r\n\t\t\tisShowClose() {\r\n\t\t\t\treturn (this.showClose === true || this.showClose === 'true') \r\n\t\t\t\t\t&& (this.showGetMore === false || this.showGetMore === 'false')\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tvar page = pages[pages.length - 1];\r\n\t\t\tvar currentWebview = page.$getAppWebview();\r\n\t\t\tcurrentWebview.addEventListener('hide', () => {\r\n\t\t\t\tthis.webviewHide = true\r\n\t\t\t})\r\n\t\t\tcurrentWebview.addEventListener('show', () => {\r\n\t\t\t\tthis.webviewHide = false\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.initSize()\r\n\t\t\t})\r\n\t\t},\r\n\t\t// #ifdef APP-NVUE\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.stopAnimation = true\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\tinitSize() {\r\n\t\t\t\tif (this.scrollable) {\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\tlet query = [],\r\n\t\t\t\t\t\tboxWidth = 0,\r\n\t\t\t\t\t\ttextWidth = 0;\r\n\t\t\t\t\tlet textQuery = new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t.select(`#${this.elId}`)\r\n\t\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t\t.exec(ret => {\r\n\t\t\t\t\t\t\t\tthis.textWidth = ret[0].width\r\n\t\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet boxQuery = new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t.select(`#${this.elIdBox}`)\r\n\t\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t\t.exec(ret => {\r\n\t\t\t\t\t\t\t\tthis.boxWidth = ret[0].width\r\n\t\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\tquery.push(textQuery)\r\n\t\t\t\t\tquery.push(boxQuery)\r\n\t\t\t\t\tPromise.all(query).then(() => {\r\n\t\t\t\t\t\tthis.animationDuration = `${this.textWidth / this.speed}s`\r\n\t\t\t\t\t\tthis.animationDelay = `-${this.boxWidth / this.speed}s`\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.animationPlayState = 'running'\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tdom.getComponentRect(this.$refs['animationEle'], (res) => {\r\n\t\t\t\t\t\tlet winWidth = uni.getSystemInfoSync().windowWidth\r\n\t\t\t\t\t\tthis.textWidth = res.size.width\r\n\t\t\t\t\t\tanimation.transition(this.$refs['animationEle'], {\r\n\t\t\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t\t\ttransform: `translateX(-${winWidth}px)`\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tduration: 0,\r\n\t\t\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\t\t\tdelay: 0\r\n\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\tif (!this.stopAnimation) {\r\n\t\t\t\t\t\t\t\tanimation.transition(this.$refs['animationEle'], {\r\n\t\t\t\t\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t\t\t\t\ttransform: `translateX(-${this.textWidth}px)`\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\t\t\t\t\tduration: (this.textWidth - winWidth) / this.speed * 1000,\r\n\t\t\t\t\t\t\t\t\tdelay: 1000\r\n\t\t\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\t\t\tif (!this.stopAnimation) {\r\n\t\t\t\t\t\t\t\t\t\tthis.loopAnimation()\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif (!this.scrollable && (this.single || this.moreText)) {\r\n\t\t\t\t\tdom.getComponentRect(this.$refs['textBox'], (res) => {\r\n\t\t\t\t\t\tthis.wrapWidth = res.size.width\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tloopAnimation() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tanimation.transition(this.$refs['animationEle'], {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\ttransform: `translateX(0px)`\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration: 0\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\tif (!this.stopAnimation) {\r\n\t\t\t\t\t\tanimation.transition(this.$refs['animationEle'], {\r\n\t\t\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t\t\ttransform: `translateX(-${this.textWidth}px)`\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tduration: this.textWidth / this.speed * 1000,\r\n\t\t\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\t\t\tdelay: 0\r\n\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\tif (!this.stopAnimation) {\r\n\t\t\t\t\t\t\t\tthis.loopAnimation()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tclickMore() {\r\n\t\t\t\tthis.$emit('getmore')\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.show = false;\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t},\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.uni-noticebar {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tpadding: 10px 12px;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.uni-cursor-point {\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-noticebar-close {\r\n\t\tmargin-left: 8px;\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.uni-noticebar-icon {\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.uni-noticebar__content-wrapper {\r\n\t\tflex: 1;\r\n\t\tflex-direction: column;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-noticebar__content-wrapper--single {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tline-height: 18px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-noticebar__content-wrapper--single,\r\n\t.uni-noticebar__content-wrapper--scrollable {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-noticebar__content-wrapper--scrollable {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t.uni-noticebar__content--scrollable {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tflex: 0;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tflex: 1;\r\n\t\tdisplay: block;\r\n\t\toverflow: hidden;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-noticebar__content--single {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tflex: none;\r\n\t\twidth: 100%;\r\n\t\tjustify-content: center;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-noticebar__content-text {\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 18px;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tword-break: break-all;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-noticebar__content-text--single {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 1;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\twhite-space: nowrap;\r\n\t\t/* #endif */\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t}\r\n\r\n\t.uni-noticebar__content-text--scrollable {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 1;\r\n\t\tpadding-left: 750rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\theight: 18px;\r\n\t\tline-height: 18px;\r\n\t\twhite-space: nowrap;\r\n\t\tpadding-left: 100%;\r\n\t\tanimation: notice 10s 0s linear infinite both;\r\n\t\tanimation-play-state: paused;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-noticebar__more {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: nowrap;\r\n\t\talign-items: center;\r\n\t\tpadding-left: 5px;\r\n\t}\r\n\r\n\t@keyframes notice {\r\n\t\t100% {\r\n\t\t\ttransform: translate3d(-100%, 0, 0);\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-notice-bar.vue?vue&type=style&index=0&id=a1596656&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-notice-bar.vue?vue&type=style&index=0&id=a1596656&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760725045\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}