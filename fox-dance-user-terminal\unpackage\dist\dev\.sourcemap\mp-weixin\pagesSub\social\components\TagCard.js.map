{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagCard.vue?28ac", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagCard.vue?dc5f", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagCard.vue?db9e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagCard.vue?5d92", "uni-app:///pagesSub/social/components/TagCard.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagCard.vue?8a80", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagCard.vue?9e50"], "names": ["name", "components", "TagFollowButton", "props", "tag", "type", "required", "data", "imageError", "methods", "goTagDetail", "uni", "url", "onFollowChange", "onImageError", "formatNumber"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAgvB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC8DpwB;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAm6C,CAAgB,wwCAAG,EAAC,C;;;;;;;;;;;ACAv7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/components/TagCard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./TagCard.vue?vue&type=template&id=093619a4&scoped=true&\"\nvar renderjs\nimport script from \"./TagCard.vue?vue&type=script&lang=js&\"\nexport * from \"./TagCard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TagCard.vue?vue&type=style&index=0&id=093619a4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"093619a4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/components/TagCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagCard.vue?vue&type=template&id=093619a4&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatNumber(_vm.tag.postCount || 0)\n  var m1 = _vm.formatNumber(_vm.tag.viewCount || 0)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagCard.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"tag-card\" @click=\"goTagDetail\">\n    <!-- 话题图标 -->\n    <view class=\"tag-icon\">\n      <view v-if=\"tag.coverImage\" class=\"tag-cover\">\n        <image \n          :src=\"tag.coverImage\" \n          class=\"cover-image\" \n          mode=\"aspectFill\"\n          @error=\"onImageError\"\n        />\n      </view>\n      <view v-else class=\"default-icon\">\n        <u-icon name=\"hash\" color=\"#ff6b87\" size=\"32\"></u-icon>\n      </view>\n    </view>\n\n    <!-- 话题信息 -->\n    <view class=\"tag-info\">\n      <view class=\"tag-name\">\n        <text class=\"name-text\">#{{ tag.name || tag.tagName }}</text>\n        \n        <!-- 热门标识 -->\n        <view v-if=\"tag.isHot\" class=\"hot-badge\">\n          <u-icon name=\"fire\" color=\"#ff6b87\" size=\"14\"></u-icon>\n          <text class=\"hot-text\">热门</text>\n        </view>\n      </view>\n      \n      <view v-if=\"tag.description\" class=\"tag-description\">\n        <text class=\"desc-text\">{{ tag.description }}</text>\n      </view>\n      \n      <!-- 话题统计 -->\n      <view class=\"tag-stats\">\n        <view class=\"stat-item\">\n          <u-icon name=\"edit-pen\" color=\"#909399\" size=\"14\"></u-icon>\n          <text class=\"stat-text\">{{ formatNumber(tag.postCount || 0) }}篇帖子</text>\n        </view>\n        <view class=\"stat-item\">\n          <u-icon name=\"eye\" color=\"#909399\" size=\"14\"></u-icon>\n          <text class=\"stat-text\">{{ formatNumber(tag.viewCount || 0) }}次浏览</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 关注按钮 -->\n    <view class=\"action-container\">\n      <TagFollowButton\n        :tag=\"tag\"\n        :followed=\"tag.isFollowed\"\n        size=\"mini\"\n        @click.stop\n        @follow-change=\"onFollowChange\"\n      />\n    </view>\n  </view>\n</template>\n\n<script>\nimport TagFollowButton from './TagFollowButton.vue';\n\nexport default {\n  name: \"TagCard\",\n  components: {\n    TagFollowButton\n  },\n  props: {\n    tag: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      imageError: false\n    };\n  },\n  methods: {\n    // 跳转到话题详情页\n    goTagDetail() {\n      uni.navigateTo({\n        url: `/pagesSub/social/tag/detail?tagId=${this.tag.id || this.tag.tagId}&tagName=${encodeURIComponent(this.tag.name || this.tag.tagName)}`\n      });\n    },\n\n    // 关注状态变化回调\n    onFollowChange(isFollowed) {\n      this.tag.isFollowed = isFollowed;\n      // 可以在这里更新话题的关注数等统计信息\n      if (isFollowed) {\n        this.tag.followCount = (this.tag.followCount || 0) + 1;\n      } else {\n        this.tag.followCount = Math.max((this.tag.followCount || 0) - 1, 0);\n      }\n    },\n\n    // 图片加载失败\n    onImageError() {\n      this.imageError = true;\n    },\n\n    // 格式化数字\n    formatNumber(num) {\n      if (num >= 10000) {\n        return (num / 10000).toFixed(1) + 'w';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'k';\n      }\n      return num.toString();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.tag-card {\n  display: flex;\n  align-items: center;\n  padding: 24rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  \n  &:active {\n    transform: scale(0.98);\n    background-color: #f8f9fa;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.tag-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.tag-cover {\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n}\n\n.default-icon {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #ff6b87, #ff8fa3);\n  border-radius: 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tag-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.tag-name {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.name-text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-right: 12rpx;\n}\n\n.hot-badge {\n  display: flex;\n  align-items: center;\n  background-color: #fff2f0;\n  border: 1rpx solid #ffccc7;\n  border-radius: 12rpx;\n  padding: 4rpx 8rpx;\n}\n\n.hot-text {\n  font-size: 20rpx;\n  color: #ff6b87;\n  margin-left: 4rpx;\n}\n\n.tag-description {\n  margin-bottom: 12rpx;\n}\n\n.desc-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.tag-stats {\n  display: flex;\n  gap: 24rpx;\n}\n\n.stat-item {\n  display: flex;\n  align-items: center;\n}\n\n.stat-text {\n  font-size: 24rpx;\n  color: #909399;\n  margin-left: 6rpx;\n}\n\n.action-container {\n  margin-left: 16rpx;\n  flex-shrink: 0;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagCard.vue?vue&type=style&index=0&id=093619a4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagCard.vue?vue&type=style&index=0&id=093619a4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751620995\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}