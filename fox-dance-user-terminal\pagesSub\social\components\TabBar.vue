<template>
  <view class="tabbar-container">
    <u-tabbar
      :list="tabbarList"
      :value="currentTab"
      active-color="#2979ff"
      inactive-color="#909399"
      height="100"
      bg-color="#fff"
      border-top
      safe-area-inset-bottom
      @change="tabbarChange"
    ></u-tabbar>
  </view>
</template>

<script>
export default {
  name: "TabBar",
  props: {
    // 当前激活的选项卡索引
    currentTab: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      messageCount: 0, // 消息数量
      tabbarList: [
        {
          pagePath: "/pagesSub/social/home/<USER>",
          text: "首页",
          iconPath: "home",
          selectedIconPath: "home-fill"
        },
        {
          pagePath: "/pagesSub/social/discover/index",
          text: "发现",
          iconPath: "search",
          selectedIconPath: "search"
        },
        {
          pagePath: "/pagesSub/social/publish/index",
          text: "",
          iconPath: "/static/icon/publish.png",
          selectedIconPath: "/static/icon/publish.png",
          customIcon: false
        },
        {
          pagePath: "/pagesSub/social/message/index",
          text: "消息",
          iconPath: "chat",
          selectedIconPath: "chat-fill",
          badge: 0 // 消息徽标数量
        },
        {
          pagePath: "/pagesSub/social/profile/index",
          text: "我的",
          iconPath: "account",
          selectedIconPath: "account-fill"
        }
      ]
    };
  },
  mounted() {
    // 组件挂载时开始监控消息
    this.startMessageMonitoring();

    // 监听清除消息徽标事件
    uni.$on("clearMessageBadge", this.clearMessageBadge);
  },
  beforeDestroy() {
    // 组件销毁时清除监控
    this.stopMessageMonitoring();

    // 移除事件监听
    uni.$off("clearMessageBadge", this.clearMessageBadge);
  },
  methods: {
    // 发布按钮点击
    handlePublish() {
      uni.navigateTo({
        url: "/pagesSub/social/publish/index"
      });
    },

    // 发布按钮点击 - 直接选择图片
    handlePublishWithImageSelect() {
      console.log("🔥 点击发布按钮，开始选择图片");

      uni.chooseImage({
        count: 9, // 最多选择9张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: res => {
          console.log("🔥 用户选择了图片:", res.tempFilePaths);

          // 用户选择了图片，跳转到发布页面并传递图片数据
          const tempFilePaths = res.tempFilePaths;

          // 将图片路径存储到全局数据中
          getApp().globalData.selectedImages = tempFilePaths;

          // 跳转到发布页面
          uni.navigateTo({
            url: "/pagesSub/social/publish/index?fromImageSelect=true"
          });
        },
        fail: error => {
          console.log("🔥 用户取消选择图片或选择失败:", error);
          // 用户取消选择图片，不做任何操作
        }
      });
    },
    tabbarChange(index) {
      const item = this.tabbarList[index];

      // 如果是发布按钮（index = 2），直接触发图片选择
      if (index === 2) {
        this.handlePublishWithImageSelect();
        return;
      }

      // 发送切换事件给父组件，而不是直接跳转页面
      this.$emit("tab-change", {
        index: index,
        item: item
      });
    },

    // 开始监控消息
    startMessageMonitoring() {
      // 模拟获取消息数量
      this.checkMessageCount();

      // 每30秒检查一次消息
      this.messageTimer = setInterval(() => {
        this.checkMessageCount();
      }, 30000);
    },

    // 停止监控消息
    stopMessageMonitoring() {
      if (this.messageTimer) {
        clearInterval(this.messageTimer);
        this.messageTimer = null;
      }
    },

    // 检查消息数量
    checkMessageCount() {
      // 这里应该调用实际的API获取消息数量
      // 现在使用模拟数据
      this.getUnreadMessageCount().then(count => {
        this.updateMessageBadge(count);
      });
    },

    // 模拟获取未读消息数量的API
    getUnreadMessageCount() {
      return new Promise(resolve => {
        // 模拟API调用
        setTimeout(() => {
          // 随机生成0-5的消息数量用于演示
          const count = Math.floor(Math.random() * 6);
          resolve(count);
        }, 100);
      });
    },

    // 更新消息徽标
    updateMessageBadge(count) {
      this.messageCount = count;

      // 找到消息Tab并更新badge
      const messageTabIndex = this.tabbarList.findIndex(
        item => item.pagePath === "/pagesSub/social/message/index"
      );

      if (messageTabIndex !== -1) {
        this.tabbarList[messageTabIndex].badge = count > 0 ? count : 0;
      }
    },

    // 清除消息徽标（当用户查看消息页面时调用）
    clearMessageBadge() {
      this.updateMessageBadge(0);
    }
  }
};
</script>

<style lang="scss" scoped>
.tabbar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
}



</style>
