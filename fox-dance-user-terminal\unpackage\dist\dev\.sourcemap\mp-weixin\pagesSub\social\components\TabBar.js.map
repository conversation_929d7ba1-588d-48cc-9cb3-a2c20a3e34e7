{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TabBar.vue?538a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TabBar.vue?1f65", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TabBar.vue?b8b9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TabBar.vue?e3d7", "uni-app:///pagesSub/social/components/TabBar.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TabBar.vue?77c2", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TabBar.vue?c627"], "names": ["name", "props", "currentTab", "type", "default", "data", "messageCount", "tabbarList", "pagePath", "text", "iconPath", "selected<PERSON><PERSON><PERSON><PERSON>", "customIcon", "badge", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "handlePublish", "url", "handlePublishWithImageSelect", "console", "count", "sizeType", "sourceType", "success", "getApp", "fail", "tabbarChange", "index", "item", "startMessageMonitoring", "stopMessageMonitoring", "clearInterval", "checkMessageCount", "getUnreadMessageCount", "setTimeout", "resolve", "updateMessageBadge", "clearMessageBadge"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+uB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiBnwB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC,aACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAE;MACA,GACA;QACAL;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAG;IACA;IACA;;IAEA;IACAC;EACA;EACAC;IACA;IACA;;IAEA;IACAD;EACA;EACAE;IACA;IACAC;MACAH;QACAI;MACA;IACA;IAEA;IACAC;MACAC;MAEAN;QACAO;QAAA;QACAC;QACAC;QACAC;UACAJ;;UAEA;UACA;;UAEA;UACAK;;UAEA;UACAX;YACAI;UACA;QACA;QACAQ;UACAN;UACA;QACA;MACA;IACA;IACAO;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACAC;UACA;UACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA,gDACA;QAAA;MAAA,EACA;MAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAAk6C,CAAgB,uwCAAG,EAAC,C;;;;;;;;;;;ACAt7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/components/TabBar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./TabBar.vue?vue&type=template&id=4fd9768a&scoped=true&\"\nvar renderjs\nimport script from \"./TabBar.vue?vue&type=script&lang=js&\"\nexport * from \"./TabBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TabBar.vue?vue&type=style&index=0&id=4fd9768a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4fd9768a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/components/TabBar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TabBar.vue?vue&type=template&id=4fd9768a&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabbar/u-tabbar\" */ \"@/components/uview-ui/components/u-tabbar/u-tabbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TabBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TabBar.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"tabbar-container\">\n    <u-tabbar\n      :list=\"tabbarList\"\n      :value=\"currentTab\"\n      active-color=\"#2979ff\"\n      inactive-color=\"#909399\"\n      height=\"100\"\n      bg-color=\"#fff\"\n      border-top\n      safe-area-inset-bottom\n      @change=\"tabbarChange\"\n    ></u-tabbar>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: \"TabBar\",\n  props: {\n    // 当前激活的选项卡索引\n    currentTab: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      messageCount: 0, // 消息数量\n      tabbarList: [\n        {\n          pagePath: \"/pagesSub/social/home/<USER>\",\n          text: \"首页\",\n          iconPath: \"home\",\n          selectedIconPath: \"home-fill\"\n        },\n        {\n          pagePath: \"/pagesSub/social/discover/index\",\n          text: \"发现\",\n          iconPath: \"search\",\n          selectedIconPath: \"search\"\n        },\n        {\n          pagePath: \"/pagesSub/social/publish/index\",\n          text: \"\",\n          iconPath: \"/static/icon/publish.png\",\n          selectedIconPath: \"/static/icon/publish.png\",\n          customIcon: false\n        },\n        {\n          pagePath: \"/pagesSub/social/message/index\",\n          text: \"消息\",\n          iconPath: \"chat\",\n          selectedIconPath: \"chat-fill\",\n          badge: 0 // 消息徽标数量\n        },\n        {\n          pagePath: \"/pagesSub/social/profile/index\",\n          text: \"我的\",\n          iconPath: \"account\",\n          selectedIconPath: \"account-fill\"\n        }\n      ]\n    };\n  },\n  mounted() {\n    // 组件挂载时开始监控消息\n    this.startMessageMonitoring();\n\n    // 监听清除消息徽标事件\n    uni.$on(\"clearMessageBadge\", this.clearMessageBadge);\n  },\n  beforeDestroy() {\n    // 组件销毁时清除监控\n    this.stopMessageMonitoring();\n\n    // 移除事件监听\n    uni.$off(\"clearMessageBadge\", this.clearMessageBadge);\n  },\n  methods: {\n    // 发布按钮点击\n    handlePublish() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/publish/index\"\n      });\n    },\n\n    // 发布按钮点击 - 直接选择图片\n    handlePublishWithImageSelect() {\n      console.log(\"🔥 点击发布按钮，开始选择图片\");\n\n      uni.chooseImage({\n        count: 9, // 最多选择9张图片\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: res => {\n          console.log(\"🔥 用户选择了图片:\", res.tempFilePaths);\n\n          // 用户选择了图片，跳转到发布页面并传递图片数据\n          const tempFilePaths = res.tempFilePaths;\n\n          // 将图片路径存储到全局数据中\n          getApp().globalData.selectedImages = tempFilePaths;\n\n          // 跳转到发布页面\n          uni.navigateTo({\n            url: \"/pagesSub/social/publish/index?fromImageSelect=true\"\n          });\n        },\n        fail: error => {\n          console.log(\"🔥 用户取消选择图片或选择失败:\", error);\n          // 用户取消选择图片，不做任何操作\n        }\n      });\n    },\n    tabbarChange(index) {\n      const item = this.tabbarList[index];\n\n      // 如果是发布按钮（index = 2），直接触发图片选择\n      if (index === 2) {\n        this.handlePublishWithImageSelect();\n        return;\n      }\n\n      // 发送切换事件给父组件，而不是直接跳转页面\n      this.$emit(\"tab-change\", {\n        index: index,\n        item: item\n      });\n    },\n\n    // 开始监控消息\n    startMessageMonitoring() {\n      // 模拟获取消息数量\n      this.checkMessageCount();\n\n      // 每30秒检查一次消息\n      this.messageTimer = setInterval(() => {\n        this.checkMessageCount();\n      }, 30000);\n    },\n\n    // 停止监控消息\n    stopMessageMonitoring() {\n      if (this.messageTimer) {\n        clearInterval(this.messageTimer);\n        this.messageTimer = null;\n      }\n    },\n\n    // 检查消息数量\n    checkMessageCount() {\n      // 这里应该调用实际的API获取消息数量\n      // 现在使用模拟数据\n      this.getUnreadMessageCount().then(count => {\n        this.updateMessageBadge(count);\n      });\n    },\n\n    // 模拟获取未读消息数量的API\n    getUnreadMessageCount() {\n      return new Promise(resolve => {\n        // 模拟API调用\n        setTimeout(() => {\n          // 随机生成0-5的消息数量用于演示\n          const count = Math.floor(Math.random() * 6);\n          resolve(count);\n        }, 100);\n      });\n    },\n\n    // 更新消息徽标\n    updateMessageBadge(count) {\n      this.messageCount = count;\n\n      // 找到消息Tab并更新badge\n      const messageTabIndex = this.tabbarList.findIndex(\n        item => item.pagePath === \"/pagesSub/social/message/index\"\n      );\n\n      if (messageTabIndex !== -1) {\n        this.tabbarList[messageTabIndex].badge = count > 0 ? count : 0;\n      }\n    },\n\n    // 清除消息徽标（当用户查看消息页面时调用）\n    clearMessageBadge() {\n      this.updateMessageBadge(0);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.tabbar-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 999;\n  background: #fff;\n  border-top: 2rpx solid #e4e7ed;\n}\n\n\n\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TabBar.vue?vue&type=style&index=0&id=4fd9768a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TabBar.vue?vue&type=style&index=0&id=4fd9768a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760723528\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}