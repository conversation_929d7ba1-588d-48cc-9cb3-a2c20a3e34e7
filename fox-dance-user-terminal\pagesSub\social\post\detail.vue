<template>
  <view class="detail-container">
    <!-- 页面加载动画 -->
    <view v-if="loading.page" class="page-loading">
      <view class="loading-container">
        <view class="loading-spinner">
          <u-loading mode="circle" size="40" color="#ff6b87"></u-loading>
        </view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view v-else class="content" scroll-y>
      <!-- 帖子内容 -->
      <view class="post-detail">
        <!-- 帖子内容加载状态 -->
        <view v-if="loading.post" class="post-loading">
          <view class="skeleton-user">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-user-info">
              <view class="skeleton-line skeleton-username"></view>
              <view class="skeleton-line skeleton-time"></view>
            </view>
          </view>
          <view class="skeleton-content">
            <view class="skeleton-line skeleton-title"></view>
            <view class="skeleton-line skeleton-text"></view>
            <view class="skeleton-line skeleton-text short"></view>
          </view>
        </view>

        <!-- 帖子实际内容 -->
        <template v-else>
          <!-- 用户信息 -->
          <view class="user-info">
            <u-avatar :src="postData.userAvatar" size="50" @click="goUserProfile"></u-avatar>
            <view class="user-details">
              <text class="username" @click="goUserProfile">{{ postData.username }}</text>
              <text class="time">{{ formatTime(postData.createTime) }}</text>
            </view>
            <!-- 如果是本人帖子，显示操作按钮；否则显示关注按钮 -->
            <view v-if="isOwnPost" class="post-actions">
              <view class="action-btn" @click="showPostActions">
                <u-icon name="more-dot-fill" color="#666" size="20"></u-icon>
              </view>
            </view>
            <FollowButton
              v-else
              :user="{ id: postData.userId, nickname: postData.username }"
              :followed="postData.isFollowed"
              size="mini"
              @follow="onUserFollow"
              @change="onFollowChange"
            />
          </view>

          <!-- 帖子标题 -->
          <view class="post-title" v-if="postData.title">
            <text class="title-text">{{ postData.title }}</text>
          </view>

          <!-- 帖子文字内容 -->
          <view class="post-content">
            <text class="content-text">{{ postData.content }}</text>
          </view>
        </template>

        <!-- 话题标签 -->
        <view class="topic-tags" v-if="postData.topics && postData.topics.length">
          <text
            v-for="topic in postData.topics"
            :key="topic"
            class="topic-tag"
            @click="goTopic(topic)"
          >
            #{{ topic }}
          </text>
        </view>

        <!-- 帖子图片轮播 -->
        <view class="post-images" v-if="postData.images && postData.images.length">
          <swiper
            class="image-swiper"
            :indicator-dots="postData.images.length > 1"
            :autoplay="false"
            :circular="true"
            indicator-color="rgba(255, 255, 255, 0.5)"
            indicator-active-color="#fff"
          >
            <swiper-item
              v-for="(img, index) in postData.images"
              :key="index"
            >
              <image
                :src="imgUrl(img)"
                class="swiper-image"
                mode="aspectFill"
                @click="previewImage(index)"
              />
            </swiper-item>
          </swiper>
        </view>

        <!-- 位置信息 -->
        <view class="location-info" v-if="postData.location" @click="openLocation">
          <u-icon name="map" color="#999" size="16"></u-icon>
          <text class="location-text">{{ postData.location }}</text>
        </view>

        <!-- 互动数据 -->
        <view class="post-stats">
          <text class="stat-item">{{ postData.likeCount }}人点赞</text>
          <text class="stat-item">{{ postData.commentCount }}条评论</text>
          <text class="stat-item">{{ postData.shareCount }}次分享</text>
        </view>

        <!-- 互动按钮 -->
        <view class="action-bar">
          <view class="action-item" @click="toggleLike">
            <u-icon
              :name="postData.isLiked ? 'heart-fill' : 'heart'"
              :color="postData.isLiked ? '#ff4757' : '#666'"
              size="24"
            ></u-icon>
            <text class="action-text">点赞 ({{ postData.likeCount || 0 }})</text>
          </view>
          <view class="action-item" @click="focusComment">
            <u-icon name="chat" color="#666" size="24"></u-icon>
            <text class="action-text">评论</text>
          </view>
          <view class="action-item" @click="sharePost">
            <u-icon name="share" color="#666" size="24"></u-icon>
            <text class="action-text">分享</text>
          </view>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 {{ loading.comments ? '...' : commentList.length }}</text>

            <u-tabs
              :list="tabList"
              :current="currentTab"
              @change="changeSortType"
              :scrollable="false"
              activeColor="#2979ff"
              inactiveColor="#999"
              fontSize="28"
              lineColor="#2979ff"
              lineWidth="20"
              lineHeight="3"
              height="40"
            ></u-tabs>
        </view>

        <!-- 评论加载状态 -->
        <view v-if="loading.comments" class="comments-loading">
          <view v-for="n in 3" :key="n" class="skeleton-comment">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-comment-content">
              <view class="skeleton-line skeleton-comment-user"></view>
              <view class="skeleton-line skeleton-comment-text"></view>
              <view class="skeleton-line skeleton-comment-text short"></view>
            </view>
          </view>
        </view>

        <!-- 评论实际内容 -->
        <view v-else class="comment-list">
          <!-- 评论空状态 -->
          <view v-if="commentList.length === 0" class="comment-empty">
            <view class="empty-icon">
              <u-icon name="chat" size="60" color="#d0d0d0"></u-icon>
            </view>
            <view class="empty-text">暂无评论</view>
            <view class="empty-desc">快来发表第一条评论吧~</view>
          </view>

          <!-- 评论列表 -->
          <view
            v-else
            v-for="comment in commentList"
            :key="comment.id"
            class="comment-item"
          >
            <view class="user-avatar" @click.stop="goCommentUserProfile(comment)">
              <u-avatar :src="comment.userAvatar" size="40"></u-avatar>
            </view>
            <view class="comment-content">
              <view class="user-info-row">
                <view class="user-info">
                  <view class="user-name" @click.stop="goCommentUserProfile(comment)">
                    {{ comment.username }}
                    <view v-if="comment.level >= 0" class="user-level"
                      :style="{ backgroundColor: getLevelColor(comment.level) }">Lv.{{ comment.level }}</view>
                  </view>
                  <view class="time">{{ formatTime(comment.createTime) }}</view>
                </view>
                <view class="like-btn" :class="{ liked: comment.isLiked }" @click.stop="toggleCommentLike(comment)">
                  <u-icon
                    :name="comment.isLiked ? 'heart-fill' : 'heart'"
                    :color="comment.isLiked ? '#ff4757' : '#666'"
                    size="20"
                  ></u-icon>
                  <text>{{ comment.likeCount || 0 }}</text>
                </view>
              </view>

              <view class="text">
                <text>{{ comment.showFullContent ? comment.content : (comment.content.length > 100 ? comment.content.slice(0, 100) + '...' : comment.content) }}</text>
                <view v-if="comment.content.length > 100" class="expand-btn" @click.stop="toggleContent(comment)">
                  {{ comment.showFullContent ? '收起' : '展开' }}
                </view>
              </view>

              <view class="actions">
                <view class="reply-btn" @click.stop="replyComment(comment)">
                  <u-icon name="chat" color="#666" size="18"></u-icon>
                  <text>回复</text>
                </view>
                <view class="more-btn" @click.stop="showMoreOptions(comment)">
                  <u-icon name="more-dot-fill" color="#999" size="18"></u-icon>
                </view>
              </view>

              <!-- 回复预览 -->
              <view v-if="comment.replies && comment.replies.length > 0" class="reply-preview">
                <view class="reply-item" v-for="(reply, rIndex) in comment.replies.slice(0, 2)" :key="rIndex">
                  <text class="reply-nickname" @click.stop="goReplyUserProfile(reply)">{{ reply.username }}</text>
                  <text v-if="reply.replyTo" class="reply-to" @click.stop="goReplyToUserProfile(reply.replyTo)">@{{ reply.replyTo.username }}</text>
                  <text class="reply-content">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>
                </view>
                <view v-if="comment.replyCount > 2" class="view-more" @click.stop="viewAllReplies(comment)">
                  查看全部{{ comment.replyCount }}条回复 >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 评论输入框 -->
    <view class="comment-input-bar">
      <!-- 回复状态指示器 -->
      <view v-if="isReplyMode" class="reply-indicator">
        <view class="reply-info">
          <text class="reply-text">回复 @{{ currentReply && currentReply.username ? currentReply.username : '用户' }}</text>
          <view class="cancel-reply-btn" @click="cancelReplyMode">
            <text>✕</text>
          </view>
        </view>
      </view>

      <view class="input-container">
        <u-avatar :src="currentUser.avatar" size="32"></u-avatar>
        <input
          ref="commentInput"
          v-model="commentText"
          class="comment-input"
          :placeholder="inputPlaceholder"
          @focus="onInputFocus"
          @blur="onInputBlur"
        />
        <text
          class="send-btn"
          :class="{ active: commentText.trim() }"
          @click="sendComment"
        >
          发送
        </text>
      </view>
    </view>

    <!-- 评论更多操作弹窗 -->
    <u-popup v-model="showMorePopup" mode="bottom" border-radius="30">
      <view class="action-popup">
        <view class="action-item reply" @click="replyFromMore">
          <view class="action-icon">
            <u-icon name="chat" color="#ff6b87" size="24"></u-icon>
          </view>
          <text>回复</text>
        </view>
        <view class="action-item copy" @click="copyComment">
          <view class="action-icon">
            <u-icon name="file-text" color="#666" size="24"></u-icon>
          </view>
          <text>复制</text>
        </view>
        <view v-if="isCommentOwner(currentMoreComment)" class="action-item delete" @click="deleteComment">
          <view class="action-icon">
            <u-icon name="trash" color="#f56c6c" size="24"></u-icon>
          </view>
          <text>删除</text>
        </view>
        <view class="action-item report" @click="reportComment">
          <view class="action-icon">
            <u-icon name="info-circle" color="#999" size="24"></u-icon>
          </view>
          <text>举报</text>
        </view>
      </view>
    </u-popup>

    <!-- 帖子操作弹窗 -->
    <u-popup v-model="showPostActionsPopup" mode="bottom" border-radius="30">
      <view class="action-popup">
        <view class="popup-header">
          <text class="popup-title">帖子操作</text>
        </view>
        <view class="action-item edit" @click="editPost">
          <view class="action-icon">
            <u-icon name="edit-pen" color="#2979ff" size="24"></u-icon>
          </view>
          <text>编辑帖子</text>
        </view>
        <view class="action-item permission" @click="setPostPermission">
          <view class="action-icon">
            <u-icon name="lock" color="#67C23A" size="24"></u-icon>
          </view>
          <text>权限设置</text>
        </view>
        <view class="action-item delete" @click="deletePost">
          <view class="action-icon">
            <u-icon name="trash" color="#f56c6c" size="24"></u-icon>
          </view>
          <text>删除帖子</text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import FollowButton from '../components/FollowButton.vue'
import { getPostDetail, getPostComments, createPostComment, replyComment, likePost, unlikePost, likeComment, getUserProfile } from '@/utils/socialApi.js'

export default {
  name: 'PostDetail',
  components: {
    FollowButton
  },
  data() {
    return {
      postId: '',
      postData: {},
      commentList: [],
      commentText: '',
      sortType: 'time', // time, hot
      currentTab: 0,
      tabList: [
        { name: '最新' },
        { name: '最热' }
      ],
      currentUser: {
        id: null,
        avatar: '/static/images/toux.png', // 默认头像
        nickname: '加载中...'
      },
      // 加载状态
      loading: {
        page: true,        // 页面整体加载
        post: true,        // 帖子详情加载
        comments: true,    // 评论列表加载
        user: true         // 用户信息加载
      },
      replyingTo: null,
      // 回复相关状态
      isReplyMode: false,
      currentReply: null,
      inputPlaceholder: '写评论...',
      // 更多操作弹窗
      showMorePopup: false,
      currentMoreComment: null,
      // 帖子操作弹窗
      showPostActionsPopup: false
    }
  },
  onLoad(options) {
    console.log('详情页接收到的参数:', options)

    // 获取帖子ID，支持多种参数名
    this.postId = options.id || options.postId || '7' // 默认使用ID 7进行测试

    console.log('最终使用的帖子ID:', this.postId)

    // 确保postId是字符串类型
    this.postId = String(this.postId)

    // 开始加载数据
    this.initPageData()
  },
  computed: {
    // 判断是否是当前用户的帖子
    isOwnPost() {
      return this.currentUser.id && this.postData.userId &&
             String(this.currentUser.id) === String(this.postData.userId)
    }
  },
  methods: {
    // 初始化页面数据
    async initPageData() {
      try {
        // 设置加载状态
        this.loading = {
          page: true,
          post: true,
          comments: true,
          user: true
        }

        // 并行加载数据
        await Promise.all([
          this.loadCurrentUser(),
          this.loadPostDetail(),
          this.loadComments()
        ])

        // 所有数据加载完成，关闭页面加载状态
        this.loading.page = false

      } catch (error) {
        console.error('页面数据初始化失败:', error)
        this.loading.page = false

        uni.showToast({
          title: '页面加载失败',
          icon: 'none'
        })
      }
    },

    async loadCurrentUser() {
      try {
        this.loading.user = true

        const userId = uni.getStorageSync('userid')
        if (!userId) {
          console.log('用户未登录，使用默认头像')
          this.loading.user = false
          return
        }

        console.log('加载当前用户信息，ID:', userId)
        const result = await getUserProfile(userId)
        console.log('当前用户信息API返回:', result)

        if (result && result.code === 0 && result.data) {
          const user = result.data
          this.currentUser = {
            id: user.userId,
            avatar: user.avatar ? 'https://file.foxdance.com.cn' + user.avatar + 'imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85': '/static/images/toux.png',
            nickname: user.nickname || '无名氏'
          }
          console.log('当前用户信息加载成功:', this.currentUser)
        } else {
          console.error('获取用户信息失败:', result)
        }

        this.loading.user = false
      } catch (error) {
        console.error('加载当前用户信息失败:', error)
        this.loading.user = false
      }
    },

    async loadPostDetail() {
      try {
        this.loading.post = true

        console.log('加载帖子详情，ID:', this.postId)
        console.log('postId类型:', typeof this.postId)

        if (!this.postId) {
          throw new Error('帖子ID为空')
        }

        const result = await getPostDetail(this.postId)
        console.log('帖子详情API返回:', result)

        if (result && result.code === 0 && result.data) {
          const post = result.data
          console.log('🔥 API返回的原始帖子数据:', post)
          console.log('🔥 API返回的isLiked值:', post.isLiked, '类型:', typeof post.isLiked)

          this.postData = {
            id: post.id,
            userId: post.userId,
            username: post.nickname || '无名氏',
            userAvatar: 'https://file.foxdance.com.cn' + post.avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85' || '/static/images/toux.png',
            title: post.title || '',
            content: post.content,
            images: post.images || [],
            topics: post.tags || [],
            location: post.locationName || '',
            locationAddress: post.locationAddress || '',
            locationLatitude: post.locationLatitude || null,
            locationLongitude: post.locationLongitude || null,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            shareCount: post.shareCount || 0,
            isLiked: Boolean(post.isLiked),
            isFollowed: post.isFollowed || false,
            createTime: new Date(post.createTime)
          }
          console.log('帖子数据加载成功:', this.postData)
        } else {
          console.error('API返回数据格式错误:', result)
          throw new Error('获取帖子详情失败 - API返回格式错误')
        }

        this.loading.post = false
      } catch (error) {
        console.error('加载帖子详情失败:', error)
        this.loading.post = false

        uni.showToast({
          title: '帖子加载失败',
          icon: 'none'
        })
        // 加载失败，返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },

    async loadComments() {
      try {
        this.loading.comments = true

        console.log('加载帖子评论列表，帖子ID:', this.postId)
        const result = await getPostComments(this.postId, {
          userId: 1, // 当前用户ID，实际应该从用户状态获取
          filter: this.sortType === 'time' ? 'latest' : 'hot',
          current: 1,
          pageSize: 50
        })
        console.log('帖子评论列表API返回:', result)

        if (result && result.code === 0 && result.data) {
          // 处理API返回格式
          let comments = []
          if (result.data.comments) {
            comments = result.data.comments
          } else if (Array.isArray(result.data)) {
            comments = result.data
          }

          this.commentList = comments.map(comment => ({
            id: comment.id,
            userId: comment.userId,
            username: comment.nickname || '无名氏',
            userAvatar: 'https://file.foxdance.com.cn' + comment.avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85' || '/static/images/toux.png',
            content: comment.content,
            likeCount: comment.likes || 0,
            isLiked: comment.isLiked || false,
            level: comment.level || 0,
            createTime: new Date(comment.createdAt || comment.createTime),
            // 后端返回的回复预览数据（来自comment_replies表）
            replies: (comment.replies || []).map(reply => ({
              id: reply.id,
              userId: reply.userId,
              username: reply.nickname || '无名氏',
              userAvatar: 'https://file.foxdance.com.cn' + reply.avatar || '/static/images/toux.png',
              content: reply.content,
              likeCount: reply.likes || 0,
              isLiked: reply.isLiked || false,
              replyTo: reply.replyTo ? {
                userId: reply.replyTo.id,
                username: reply.replyTo.nickname || '用户'
              } : null,
              createTime: new Date(reply.createdAt || reply.createTime)
            })),
            replyCount: comment.replyCount || 0
          }))

          console.log('评论列表加载成功:', this.commentList.length)
        } else {
          console.log('评论API返回格式不正确或无数据')
          this.commentList = []
        }

        this.loading.comments = false
      } catch (error) {
        console.error('加载评论失败:', error)
        this.loading.comments = false

        uni.showToast({
          title: '评论加载失败',
          icon: 'none'
        })
        this.commentList = []
      }
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      return `${days}天前`
    },

    goBack() {
      uni.navigateBack()
    },

    goUserProfile() {
      if (!this.postData.userId) {
        uni.showToast({
          title: '用户信息错误',
          icon: 'none'
        })
        return
      }

      console.log('跳转到帖子作者主页，用户ID:', this.postData.userId)
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${this.postData.userId}`
      })
    },

    // 跳转到评论用户主页
    goCommentUserProfile(comment) {
      if (!comment.userId) {
        uni.showToast({
          title: '用户信息错误',
          icon: 'none'
        })
        return
      }

      console.log('跳转到评论用户主页，用户ID:', comment.userId, '用户名:', comment.username)
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${comment.userId}`
      })
    },

    // 跳转到回复用户主页
    goReplyUserProfile(reply) {
      if (!reply.userId) {
        uni.showToast({
          title: '用户信息错误',
          icon: 'none'
        })
        return
      }

      console.log('跳转到回复用户主页，用户ID:', reply.userId, '用户名:', reply.username)
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${reply.userId}`
      })
    },

    // 跳转到被回复用户主页
    goReplyToUserProfile(replyToUser) {
      if (!replyToUser.userId) {
        uni.showToast({
          title: '用户信息错误',
          icon: 'none'
        })
        return
      }

      console.log('跳转到被回复用户主页，用户ID:', replyToUser.userId, '用户名:', replyToUser.username)
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${replyToUser.userId}`
      })
    },

    onUserFollow(data) {
      console.log('关注操作:', data)
      // 这里可以调用API进行关注/取消关注操作
    },

    onFollowChange(data) {
      // 更新本地数据
      this.postData.isFollowed = data.isFollowed
      console.log('关注状态变化:', data)
    },
    imgUrl(img) {
      return img + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85'
    },

    async toggleLike() {
      console.log('=== 点赞功能开始 ===')
      console.log('点赞按钮被点击')
      console.log('当前帖子数据:', this.postData)

      // 先测试简单的UI更新
      uni.showToast({
        title: '点赞功能被触发',
        icon: 'none',
        duration: 2000
      })

      if (!this.postData || !this.postData.id) {
        console.error('帖子数据无效:', this.postData)
        uni.showToast({
          title: '帖子信息错误',
          icon: 'none'
        })
        return
      }

      const originalLiked = this.postData.isLiked
      const originalCount = this.postData.likeCount || 0

      try {
        // 先更新UI，提供即时反馈
        this.postData.isLiked = !this.postData.isLiked
        this.postData.likeCount = this.postData.isLiked ? originalCount + 1 : Math.max(originalCount - 1, 0)

        console.log('点赞操作 - 帖子ID:', this.postData.id, '类型:', typeof this.postData.id, '操作:', this.postData.isLiked ? '点赞' : '取消点赞')

        // 确保帖子ID是数字类型
        const postId = Number(this.postData.id)
        const userId = Number(uni.getStorageSync('userid')) // 从缓存获取当前用户ID

        console.log('🔥 点赞操作详情:')
        console.log('  - postId:', postId, 'userId:', userId)
        console.log('  - 原始状态 isLiked:', originalLiked, 'likeCount:', originalCount)
        console.log('  - 新状态 isLiked:', this.postData.isLiked, 'likeCount:', this.postData.likeCount)

        if (!userId) {
          console.error('用户未登录')
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
          // 回滚UI状态
          this.postData.isLiked = originalLiked
          this.postData.likeCount = originalCount
          return
        }

        // 调用对应的API
        let result
        if (this.postData.isLiked) {
          console.log('调用点赞API')
          result = await likePost(postId, userId)
        } else {
          console.log('调用取消点赞API')
          result = await unlikePost(postId, userId)
        }

        console.log('点赞API返回:', result)

        if (result && result.code === 0) {
          // API调用成功
          console.log('点赞操作成功')
          uni.showToast({
            title: this.postData.isLiked ? '点赞成功' : '取消点赞',
            icon: 'success',
            duration: 1000
          })
        } else {
          // API调用失败，回滚UI状态
          console.warn('点赞API调用失败:', result)
          this.postData.isLiked = originalLiked
          this.postData.likeCount = originalCount

          uni.showToast({
            title: result?.message || '操作失败，请重试',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('点赞操作失败:', error)

        // 回滚UI状态
        this.postData.isLiked = originalLiked
        this.postData.likeCount = originalCount

        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    },

    toggleCommentLike(comment) {
      comment.isLiked = !comment.isLiked
      comment.likeCount += comment.isLiked ? 1 : -1
    },

    previewImage(index) {
      uni.previewImage({
        urls: this.postData.images,
        current: index
      })
    },

    sharePost() {
      uni.showActionSheet({
        itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
        success: () => {
          this.$u.toast('分享成功')
          this.postData.shareCount++
        }
      })
    },

    goTopic(topic) {
      uni.navigateTo({
        url: `/pagesSub/social/topic/detail?name=${topic}`
      })
    },

    openLocation() {
      if (!this.postData.location) return

      console.log('打开位置信息:', this.postData.location)

      // 如果有经纬度信息，可以打开地图
      if (this.postData.locationLatitude && this.postData.locationLongitude) {
        uni.openLocation({
          latitude: Number(this.postData.locationLatitude),
          longitude: Number(this.postData.locationLongitude),
          name: this.postData.location,
          address: this.postData.locationAddress || this.postData.location,
          success: () => {
            console.log('打开地图成功')
          },
          fail: (err) => {
            console.error('打开地图失败:', err)
            // 如果打开地图失败，显示位置信息
            uni.showModal({
              title: '位置信息',
              content: this.postData.location,
              showCancel: false
            })
          }
        })
      } else {
        // 没有经纬度信息，只显示位置名称
        uni.showModal({
          title: '位置信息',
          content: this.postData.location,
          showCancel: false
        })
      }
    },

    changeSortType(...args) {
      console.log('tabs点击参数:', args)
      const index = typeof args[0] === 'number' ? args[0] : (args[0] && args[0].index !== undefined ? args[0].index : 0)
      this.currentTab = index
      this.sortType = index === 0 ? 'time' : 'hot'
      // 重新加载评论
      this.loadComments()
      this.loadComments()
    },

    focusComment() {
      this.$refs.commentInput.focus()
    },

    onInputFocus() {
      // 输入框获得焦点
    },

    onInputBlur() {
      // 输入框失去焦点
    },

    replyComment(comment) {
      // 设置回复状态
      this.isReplyMode = true
      this.currentReply = comment
      this.inputPlaceholder = `@${comment.username}`

      // 聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.commentInput) {
          this.$refs.commentInput.focus()
        }
      })
    },

    async sendComment() {
      if (!this.commentText.trim()) {
        uni.showToast({
          title: '请输入评论内容',
          icon: 'none'
        })
        return
      }

      try {
        console.log('发送评论，帖子ID:', this.postId, '内容:', this.commentText, '回复模式:', this.isReplyMode)

        // 根据是否是回复模式调用不同的API
        if (this.isReplyMode && this.currentReply) {
          // 回复评论
          await this.sendReplyComment()
        } else {
          // 普通评论
          await this.sendNormalComment()
        }
      } catch (error) {
        console.error('发送评论失败:', error)
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    },

    // 发送普通评论
    async sendNormalComment() {
      const commentData = {
        postId: String(this.postId),
        content: this.commentText.trim(),
        userId: 1 // 当前用户ID，实际应该从用户状态获取
      }

      console.log('发送普通评论数据:', commentData)

      try {
        const result = await createPostComment(commentData)
        console.log('普通评论API返回:', result)

        if (result && result.code === 0) {
          this.handleCommentSuccess()
          return
        }
      } catch (apiError) {
        console.warn('普通评论API调用失败，使用临时方案:', apiError)
      }

      // API失败时的临时处理：直接添加到本地列表
      console.log('使用临时评论方案')
      const newComment = {
        id: Date.now(),
        userId: this.currentUser.id || 1,
        username: this.currentUser.nickname || '当前用户',
        userAvatar: this.currentUser.avatar || '/static/images/toux.png',
        content: this.commentText.trim(),
        likeCount: 0,
        isLiked: false,
        level: 0,
        createTime: new Date(),
        replyCount: 0,
        replies: []
      }

      // 添加到评论列表顶部
      this.commentList.unshift(newComment)
      this.handleCommentSuccess()
    },

    // 发送回复评论
    async sendReplyComment() {
      const replyData = {
        content: this.commentText.trim(),
        userId: 1, // 当前用户ID，实际应该从用户状态获取
        replyToUserId: this.currentReply.userId,
        replyToUsername: this.currentReply.username
      }

      console.log('发送回复评论数据:', replyData, '目标评论ID:', this.currentReply.id)

      try {
        const result = await replyComment(this.currentReply.id, replyData)
        console.log('回复评论API返回:', result)

        if (result && result.code === 0) {
          this.handleCommentSuccess()
          return
        }
      } catch (apiError) {
        console.warn('回复评论API调用失败，使用临时方案:', apiError)
      }

      // API失败时的临时处理：重新加载评论列表
      console.log('回复API失败，重新加载评论列表')
      this.handleCommentSuccess()

      // 重新加载评论列表以获取最新的回复数据
      setTimeout(() => {
        this.loadComments()
      }, 500)
    },

    // 处理评论成功的通用逻辑
    handleCommentSuccess() {
      const wasReplyMode = this.isReplyMode

      // 清空输入框和回复状态
      this.commentText = ''
      this.replyingTo = null
      this.cancelReplyMode()

      // 更新帖子评论数（只有普通评论才增加评论数，回复不增加）
      if (!wasReplyMode) {
        this.postData.commentCount++
      }

      // 显示成功提示
      uni.showToast({
        title: wasReplyMode ? '回复成功' : '评论成功',
        icon: 'success'
      })

      // 重新加载评论列表以获取最新数据（包括新的回复）
      setTimeout(() => {
        this.loadComments()
      }, 500)
    },

    // 点赞评论
    async toggleCommentLike(comment) {
      if (!comment || !comment.id) {
        uni.showToast({
          title: '评论信息错误',
          icon: 'none'
        })
        return
      }

      const originalLiked = comment.isLiked
      const originalCount = comment.likeCount || 0

      try {
        // 先更新UI，提供即时反馈
        comment.isLiked = !comment.isLiked
        comment.likeCount = comment.isLiked ? originalCount + 1 : Math.max(originalCount - 1, 0)

        console.log('点赞评论 - 评论ID:', comment.id, '操作:', comment.isLiked ? '点赞' : '取消点赞')

        // 获取当前用户ID
        const userId = Number(uni.getStorageSync('userid'))
        if (!userId) {
          console.error('用户未登录')
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
          // 回滚UI状态
          comment.isLiked = originalLiked
          comment.likeCount = originalCount
          return
        }

        // 调用评论点赞API
        const result = await likeComment(comment.id, {
          userId: userId,
          action: comment.isLiked ? 'like' : 'unlike'
        })

        console.log('评论点赞API返回:', result)

        if (result && result.code === 0) {
          // API调用成功
          uni.showToast({
            title: comment.isLiked ? '点赞成功' : '取消点赞',
            icon: 'success',
            duration: 1000
          })
        } else {
          // API调用失败，回滚UI状态
          console.warn('评论点赞API调用失败:', result)
          comment.isLiked = originalLiked
          comment.likeCount = originalCount

          uni.showToast({
            title: '操作失败，请重试',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('评论点赞操作失败:', error)

        // 回滚UI状态
        comment.isLiked = originalLiked
        comment.likeCount = originalCount

        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    },

    // 展开/收起评论内容
    toggleContent(comment) {
      this.$set(comment, 'showFullContent', !comment.showFullContent)
    },

    // 获取用户等级颜色
    getLevelColor(level) {
      const colors = ['#999', '#2979ff', '#67C23A', '#E6A23C', '#F56C6C', '#9C27B0']
      return colors[Math.min(level, colors.length - 1)] || '#999'
    },

    // 显示更多操作
    showMoreOptions(comment) {
      this.currentMoreComment = comment
      this.showMorePopup = true
    },

    // 从更多操作中回复
    replyFromMore() {
      if (this.currentMoreComment) {
        this.showMorePopup = false
        setTimeout(() => {
          this.replyComment(this.currentMoreComment)
        }, 300)
      }
    },

    // 复制评论
    copyComment() {
      if (!this.currentMoreComment) return

      uni.setClipboardData({
        data: this.currentMoreComment.content,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          })
        }
      })
      this.showMorePopup = false
    },

    // 删除评论
    deleteComment() {
      if (!this.currentMoreComment) return

      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条评论吗？',
        success: (res) => {
          if (res.confirm) {
            // 这里应该调用删除API
            console.log('删除评论:', this.currentMoreComment.id)

            // 临时从列表中移除
            const index = this.commentList.findIndex(item => item.id === this.currentMoreComment.id)
            if (index > -1) {
              this.commentList.splice(index, 1)
              this.postData.commentCount--
            }

            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
      this.showMorePopup = false
    },

    // 举报评论
    reportComment() {
      uni.showToast({
        title: '举报成功',
        icon: 'success'
      })
      this.showMorePopup = false
    },

    // 判断是否是评论作者
    isCommentOwner(comment) {
      return comment && comment.userId === this.currentUser.id
    },

    // 取消回复模式
    cancelReplyMode() {
      this.isReplyMode = false
      this.currentReply = null
      this.inputPlaceholder = '写评论...'

      uni.showToast({
        title: '已取消回复',
        icon: 'none',
        duration: 1000
      })
    },

    // 查看全部回复
    async viewAllReplies(comment) {
      console.log('查看全部回复:', comment.id)

      try {
        // 调用获取回复列表的API
        const response = await uni.request({
          url: `${this.$http.vote_baseUrl}/comments/${comment.id}/replies`,
          method: 'GET',
          data: {
            userId: 1, // 当前用户ID
            sort: 'latest'
          }
        })

        if (response.data && response.data.code === 0) {
          const replies = response.data.data || []

          // 更新评论的回复列表
          const targetComment = this.commentList.find(c => c.id === comment.id)
          if (targetComment) {
            targetComment.replies = replies.map(reply => ({
              id: reply.id,
              userId: reply.userId,
              username: reply.nickname || '无名氏',
              userAvatar: reply.avatar ? (reply.avatar.startsWith('http') ? reply.avatar : 'https://file.foxdance.com.cn' + reply.avatar) : '/static/images/toux.png',
              content: reply.content,
              likeCount: reply.likes || 0,
              isLiked: reply.isLiked || false,
              replyTo: reply.replyTo ? {
                userId: reply.replyTo.id,
                username: reply.replyTo.nickname || '用户'
              } : null,
              createTime: new Date(reply.createdAt || reply.createTime)
            }))
          }

          uni.showToast({
            title: `加载了${replies.length}条回复`,
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('获取回复列表失败:', error)
        uni.showToast({
          title: '加载回复失败',
          icon: 'none'
        })
      }
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ['举报', '不感兴趣', '屏蔽用户'],
        success: (res) => {
          console.log('更多操作:', res.tapIndex)
        }
      })
    },

    // 显示帖子操作菜单
    showPostActions() {
      console.log('显示帖子操作菜单')
      this.showPostActionsPopup = true
    },

    // 编辑帖子
    editPost() {
      console.log('编辑帖子:', this.postData.id)
      this.showPostActionsPopup = false

      // 跳转到编辑页面，传递帖子数据
      uni.navigateTo({
        url: `/pagesSub/social/publish/index?mode=edit&postId=${this.postData.id}`
      })
    },

    // 权限设置
    setPostPermission() {
      console.log('设置帖子权限:', this.postData.id)
      this.showPostActionsPopup = false

      // 显示权限设置选项
      uni.showActionSheet({
        itemList: ['公开', '仅关注者可见', '私密'],
        success: (res) => {
          const permissions = ['public', 'followers', 'private']
          const selectedPermission = permissions[res.tapIndex]

          console.log('选择的权限:', selectedPermission)

          // 这里应该调用API更新帖子权限
          this.updatePostPermission(selectedPermission)
        }
      })
    },

    // 更新帖子权限
    async updatePostPermission(permission) {
      try {
        // 这里应该调用实际的API
        console.log('更新帖子权限:', this.postData.id, permission)

        // 临时模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        const permissionNames = {
          'public': '公开',
          'followers': '仅关注者可见',
          'private': '私密'
        }

        uni.showToast({
          title: `已设置为${permissionNames[permission]}`,
          icon: 'success'
        })

        // 更新本地数据
        this.postData.permission = permission

      } catch (error) {
        console.error('更新帖子权限失败:', error)
        uni.showToast({
          title: '设置失败，请重试',
          icon: 'none'
        })
      }
    },

    // 删除帖子
    deletePost() {
      console.log('删除帖子:', this.postData.id)
      this.showPostActionsPopup = false

      uni.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除这条帖子吗？',
        confirmColor: '#f56c6c',
        success: (res) => {
          if (res.confirm) {
            this.confirmDeletePost()
          }
        }
      })
    },

    // 确认删除帖子
    async confirmDeletePost() {
      try {
        console.log('确认删除帖子:', this.postData.id)

        // 显示加载提示
        uni.showLoading({
          title: '删除中...'
        })

        // 这里应该调用实际的删除API
        // const result = await deletePost(this.postData.id)

        // 临时模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.hideLoading()

        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 删除成功后返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('删除帖子失败:', error)
        uni.hideLoading()

        uni.showToast({
          title: '删除失败，请重试',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 60px;
}

/* 页面加载动画 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

/* 骨架屏样式 */
.post-loading, .comments-loading {
  padding: 32rpx;
}

.skeleton-user {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.skeleton-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-user-info {
  flex: 1;
  margin-left: 24rpx;
}

.skeleton-line {
  height: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
}

.skeleton-username {
  width: 160rpx;
}

.skeleton-time {
  width: 120rpx;
}

.skeleton-content {
  margin-bottom: 32rpx;
}

.skeleton-title {
  width: 80%;
  height: 40rpx;
  margin-bottom: 24rpx;
}

.skeleton-text {
  width: 100%;
  height: 32rpx;
  margin-bottom: 16rpx;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-comment {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.skeleton-comment-content {
  flex: 1;
  margin-left: 24rpx;
}

.skeleton-comment-user {
  width: 140rpx;
  height: 28rpx;
}

.skeleton-comment-text {
  width: 100%;
  height: 28rpx;
  margin-bottom: 12rpx;
}

.skeleton-comment-text.short {
  width: 70%;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 评论空状态样式 */
.comment-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 32rpx 80rpx;
  text-align: center;
}

.empty-icon {
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
  line-height: 1.4;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}


.post-detail {
  background: #fff;
  padding: 40rpx 32rpx;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-details {
  flex: 1;
  margin-left: 24rpx;
}

.post-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  border: 1rpx solid #e4e7ed;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: #e4e7ed;
  transform: scale(0.95);
}

.username {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  cursor: pointer;
  transition: color 0.2s ease;
}

.username:active {
  color: #2979ff;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.location {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.post-title {
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.4;
  color: #333;
  display: block;
}

.content-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.topic-tags {
  margin-bottom: 32rpx;
}

.topic-tag {
  color: #2979ff;
  font-size: 28rpx;
  margin-right: 16rpx;
}

/* 位置信息样式 */
.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #2979ff;
}

.location-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
  flex: 1;
}

.post-images {
  margin-bottom: 32rpx;
}

.image-swiper {
  width: 100%;
  height: 600rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.post-stats {
  padding: 24rpx 0;
  border-top: 2rpx solid #f0f0f0;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}

.stat-item {
  font-size: 26rpx;
  color: #666;
  margin-right: 32rpx;
}

.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.comments-section {
  background: #fff;
  padding: 32rpx;
}

.comments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.comment-item {
  display: flex;
  margin-bottom: 32rpx;
}

.comment-content {
  flex: 1;
  margin-left: 24rpx;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.replies {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}

.reply-item {
  margin-bottom: 8rpx;
}

.reply-user {
  font-size: 26rpx;
  color: #2979ff;
  margin-right: 8rpx;
}

.reply-text {
  font-size: 26rpx;
  color: #333;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.comment-action {
  display: flex;
  align-items: center;
}

.action-count, .action-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.comment-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 104rpx;
  position: relative;
}

.comment-input {
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 36rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  transition: none;
  position: absolute;
  left: 48rpx;
  top: 16rpx;
}

.comment-input:focus {
  background: #f5f5f5;
  border: none;
  outline: none;
  box-shadow: none;
  width: calc(100% - 128rpx);
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  left: 48rpx;
  top: 16rpx;
}

.send-btn {
  width: 80rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ccc;
  white-space: nowrap;
  position: absolute;
  right: 0;
  top: 16rpx;
}

.send-btn.active {
  color: #2979ff;
}

/* 重新设计的评论样式 - 符合帖子详情页风格 */
.comment-item {
  display: flex !important;
  padding: 24rpx 0 !important;
  margin-bottom: 0 !important;
  border-bottom: 1rpx solid #f0f0f0 !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
  transition: none !important;
}

.comment-item:last-child {
  border-bottom: none;
}

.user-avatar {
  margin-right: 16rpx;
  flex-shrink: 0;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.user-avatar:active {
  opacity: 0.8;
}

.user-info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
  cursor: pointer;
  transition: color 0.2s ease;
}

.user-name:active {
  color: #2979ff;
}

.user-level {
  font-size: 18rpx;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  margin-left: 12rpx;
  font-weight: 500;
  background: #2979ff;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.like-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e4e7ed;
  transition: all 0.2s ease;
  min-width: 60rpx;
  justify-content: center;
}

.like-btn:active {
  background: #e4e7ed;
}

.like-btn text {
  font-size: 24rpx;
  color: #666;
  margin-left: 6rpx;
  font-weight: 400;
}

.like-btn.liked {
  background: #fff0f0;
  border-color: #ff4757;
}

.like-btn.liked text {
  color: #ff4757;
}

.text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
  word-break: break-word;
}

.expand-btn {
  color: #2979ff;
  font-size: 26rpx;
  margin-top: 8rpx;
  padding: 4rpx 0;
  display: inline-block;
  transition: opacity 0.2s ease;
}

.expand-btn:active {
  opacity: 0.7;
}

.actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: 12rpx;
}

.reply-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e4e7ed;
  transition: all 0.2s ease;
}

.reply-btn:active {
  background: #e4e7ed;
}

.reply-btn text {
  font-size: 24rpx;
  color: #666;
  margin-left: 6rpx;
  font-weight: 400;
}

.more-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.more-btn:active {
  background: #e4e7ed;
}

.reply-preview {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.reply-item {
  font-size: 26rpx;
  margin-bottom: 8rpx;
  line-height: 1.5;
  color: #666;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-nickname {
  color: #2979ff !important;
  font-weight: 500;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.reply-nickname:active {
  opacity: 0.7;
}

.reply-to {
  color: #666 !important;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.2s ease;
}

.reply-to:active {
  color: #2979ff !important;
}

.reply-content {
  color: #666;
}

.view-more {
  color: #2979ff;
  font-size: 26rpx;
  font-weight: 400;
  margin-top: 8rpx;
  padding: 4rpx 0;
}

.view-more:active {
  opacity: 0.7;
}

/* 回复状态指示器 - 符合整体风格 */
.reply-indicator {
  background: #f8f9fa;
  border-bottom: 1rpx solid #e4e7ed;
  padding: 16rpx 32rpx;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.reply-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reply-text {
  font-size: 26rpx;
  color: #2979ff;
  font-weight: 400;
}

.cancel-reply-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e4e7ed;
  border-radius: 50%;
  font-size: 20rpx;
  color: #666;
  font-weight: normal;
}

.cancel-reply-btn:active {
  background: #d3d4d6;
  transform: scale(0.95);
}

/* 更多操作弹窗 - 简洁风格 */
.action-popup {
  padding: 32rpx 0 40rpx;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

.popup-header {
  padding: 0 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 400;
  transition: background-color 0.2s ease;
}

.action-item:active {
  background: #f8f9fa;
}

.action-item.delete {
  color: #ff4757;
}

.action-item.edit {
  color: #2979ff;
}

.action-item.permission {
  color: #67C23A;
}

.action-icon {
  margin-right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
