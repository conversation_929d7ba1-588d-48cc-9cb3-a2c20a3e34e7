<template>
  <view class="container">
    <view class="header">
      <text class="title">WebSocket连接测试</text>
    </view>

    <view class="test-section">
      <view class="info-item">
        <text class="label">当前环境：</text>
        <text class="value">{{ envInfo.platform }} - {{ envInfo.env }}</text>
      </view>

      <view class="info-item">
        <text class="label">WebSocket URL：</text>
        <text class="value">{{ wsUrl }}</text>
      </view>

      <view class="info-item">
        <text class="label">连接状态：</text>
        <text class="value" :class="statusClass">{{ connectionStatus }}</text>
      </view>
    </view>

    <view class="button-section">
      <button
        @click="testConnection"
        :disabled="isConnecting"
        class="test-btn"
      >{{ isConnecting ? '连接中...' : '测试连接' }}</button>

      <button @click="disconnect" :disabled="!isConnected" class="disconnect-btn">断开连接</button>

      <button @click="sendTestMessage" :disabled="!isConnected" class="send-btn">发送测试消息</button>
    </view>

    <view class="log-section">
      <text class="log-title">连接日志：</text>
      <scroll-view class="log-content" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-message" :class="log.type">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      socketTask: null,
      isConnected: false,
      isConnecting: false,
      logs: [],
      envInfo: {
        platform: "",
        env: ""
      },
      wsUrl: ""
    };
  },

  computed: {
    connectionStatus() {
      if (this.isConnecting) return "连接中...";
      if (this.isConnected) return "已连接";
      return "未连接";
    },

    statusClass() {
      if (this.isConnecting) return "status-connecting";
      if (this.isConnected) return "status-connected";
      return "status-disconnected";
    }
  },

  onLoad() {
    this.initEnvInfo();
    this.addLog("页面加载完成", "info");
  },

  onUnload() {
    this.disconnect();
  },

  methods: {
    initEnvInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.envInfo = {
        platform: systemInfo.platform,
        env: process.env.NODE_ENV
      };

      // 设置WebSocket URL
      if (
        process.env.NODE_ENV === "development" ||
        systemInfo.platform === "devtools"
      ) {
        this.wsUrl = "ws://localhost:8101/api/ws/chat";
      } else {
        this.wsUrl = "wss://admin.foxdance.com.cn/ws/chat";
      }

      this.addLog(
        `环境信息: ${this.envInfo.platform} - ${this.envInfo.env}`,
        "info"
      );
      this.addLog(`WebSocket URL: ${this.wsUrl}`, "info");
    },

    testConnection() {
      if (this.isConnecting || this.isConnected) {
        return;
      }

      this.isConnecting = true;
      this.addLog("开始测试WebSocket连接...", "info");

      // 获取测试用户信息
      const userId = uni.getStorageSync("userid") || "test_user";
      const token = uni.getStorageSync("token") || "test_token";

      const fullUrl = `${this.wsUrl}?userId=${userId}&token=${token}`;
      this.addLog(`连接URL: ${fullUrl}`, "info");

      this.socketTask = uni.connectSocket({
        url: fullUrl,
        success: () => {
          this.addLog("WebSocket连接请求发送成功", "success");
        },
        fail: error => {
          this.addLog(`WebSocket连接失败: ${JSON.stringify(error)}`, "error");
          this.isConnecting = false;
        }
      });

      // 监听连接打开
      this.socketTask.onOpen(() => {
        this.isConnected = true;
        this.isConnecting = false;
        this.addLog("WebSocket连接已建立！", "success");
      });

      // 监听消息
      this.socketTask.onMessage(res => {
        this.addLog(`收到消息: ${res.data}`, "success");
      });

      // 监听连接关闭
      this.socketTask.onClose(() => {
        this.isConnected = false;
        this.isConnecting = false;
        this.addLog("WebSocket连接已关闭", "warning");
      });

      // 监听错误
      this.socketTask.onError(error => {
        this.addLog(`WebSocket错误: ${JSON.stringify(error)}`, "error");
        this.isConnecting = false;
      });
    },

    disconnect() {
      if (this.socketTask) {
        this.socketTask.close();
        this.socketTask = null;
      }
      this.isConnected = false;
      this.isConnecting = false;
      this.addLog("主动断开连接", "info");
    },

    sendTestMessage() {
      if (!this.isConnected || !this.socketTask) {
        this.addLog("未连接，无法发送消息", "error");
        return;
      }

      const testMessage = {
        type: "message",
        data: {
          content: `测试消息 - ${new Date().toLocaleTimeString()}`,
          timestamp: Date.now()
        }
      };

      this.socketTask.send({
        data: JSON.stringify(testMessage),
        success: () => {
          this.addLog("测试消息发送成功", "success");
        },
        fail: error => {
          this.addLog(`消息发送失败: ${JSON.stringify(error)}`, "error");
        }
      });
    },

    addLog(message, type = "info") {
      const now = new Date();
      const time = `${now
        .getHours()
        .toString()
        .padStart(2, "0")}:${now
        .getMinutes()
        .toString()
        .padStart(2, "0")}:${now
        .getSeconds()
        .toString()
        .padStart(2, "0")}`;

      this.logs.unshift({
        time,
        message,
        type
      });

      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.status-connected {
  color: #4caf50;
}

.status-connecting {
  color: #ff9800;
}

.status-disconnected {
  color: #f44336;
}

.button-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.test-btn,
.disconnect-btn,
.send-btn {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
}

.test-btn {
  background: #2979ff;
  color: white;
}

.disconnect-btn {
  background: #f44336;
  color: white;
}

.send-btn {
  background: #4caf50;
  color: white;
}

.log-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  height: 600rpx;
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.log-content {
  height: 500rpx;
}

.log-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 10rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.log-time {
  font-size: 24rpx;
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}

.log-message {
  font-size: 26rpx;
  flex: 1;
}

.log-message.success {
  color: #4caf50;
}

.log-message.error {
  color: #f44336;
}

.log-message.warning {
  color: #ff9800;
}

.log-message.info {
  color: #333;
}
</style>
