{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-avatar/u-avatar.vue?ae3a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-avatar/u-avatar.vue?d907", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-avatar/u-avatar.vue?682d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-avatar/u-avatar.vue?ac27", "uni-app:///components/uview-ui/components/u-avatar/u-avatar.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-avatar/u-avatar.vue?9fd0", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-avatar/u-avatar.vue?e06c"], "names": ["name", "props", "bgColor", "type", "default", "src", "size", "mode", "text", "imgMode", "index", "sexIcon", "levelIcon", "levelBgColor", "sexBgColor", "showSex", "showLevel", "data", "error", "avatar", "watch", "computed", "wrapStyle", "style", "imgStyle", "uText", "uSexStyle", "uLevelStyle", "methods", "loadError", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwBpxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAmBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAf;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAgB;IACAC;MACA;MACAC,0EACA;MACAA;MACAA;MACAA;MACAA;MACA;MACA;IACA;IACAC;MACA;MACAD;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClLA;AAAA;AAAA;AAAA;AAA+7C,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAn9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-avatar/u-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-avatar.vue?vue&type=template&id=22e236d3&scoped=true&\"\nvar renderjs\nimport script from \"./u-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-avatar.vue?vue&type=style&index=0&id=22e236d3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"22e236d3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-avatar/u-avatar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=template&id=22e236d3&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapStyle])\n  var s1 = !_vm.uText && _vm.avatar ? _vm.__get_style([_vm.imgStyle]) : null\n  var s2 = _vm.showSex ? _vm.__get_style([_vm.uSexStyle]) : null\n  var s3 = _vm.showLevel ? _vm.__get_style([_vm.uLevelStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-avatar\" :style=\"[wrapStyle]\" @tap=\"click\">\r\n\t\t<image\r\n\t\t\t@error=\"loadError\"\r\n\t\t\t:style=\"[imgStyle]\"\r\n\t\t\tclass=\"u-avatar__img\"\r\n\t\t\tv-if=\"!uText && avatar\"\r\n\t\t\t:src=\"avatar\" \r\n\t\t\t:mode=\"imgMode\"\r\n\t\t></image>\r\n\t\t<text class=\"u-line-1\" v-else-if=\"uText\" :style=\"{\r\n\t\t\tfontSize: '38rpx'\r\n\t\t}\">{{uText}}</text>\r\n\t\t<slot v-else></slot>\r\n\t\t<view class=\"u-avatar__sex\" v-if=\"showSex\" :class=\"['u-avatar__sex--' + sexIcon]\" :style=\"[uSexStyle]\">\r\n\t\t\t<u-icon :name=\"sexIcon\" size=\"20\"></u-icon>\r\n\t\t</view>\r\n\t\t<view class=\"u-avatar__level\" v-if=\"showLevel\" :style=\"[uLevelStyle]\">\r\n\t\t\t<u-icon :name=\"levelIcon\" size=\"20\"></u-icon>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet base64Avatar = \"data:image/jpg;base64,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\";\r\n\t/**\r\n\t * avatar 头像\r\n\t * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\r\n\t * @tutorial https://www.uviewui.com/components/avatar.html\r\n\t * @property {String} bg-color 背景颜色，一般显示文字时用（默认#ffffff）\r\n\t * @property {String} src 头像路径，如加载失败，将会显示默认头像\r\n\t * @property {String Number} size 头像尺寸，可以为指定字符串(large, default, mini)，或者数值，单位rpx（默认default）\r\n\t * @property {String} mode 显示类型，见上方说明（默认circle）\r\n\t * @property {String} sex-icon 性别图标，man-男，woman-女（默认man）\r\n\t * @property {String} level-icon 等级图标（默认level）\r\n\t * @property {String} sex-bg-color 性别图标背景颜色\r\n\t * @property {String} level-bg-color 等级图标背景颜色\r\n\t * @property {String} show-sex 是否显示性别图标（默认false）\r\n\t * @property {String} show-level 是否显示等级图标（默认false）\r\n\t * @property {String} img-mode 头像图片的裁剪类型，与uni的image组件的mode参数一致，如效果达不到需求，可尝试传widthFix值（默认aspectFill）\r\n\t * @property {String} index 用户传递的标识符值，如果是列表循环，可穿v-for的index值\r\n\t * @event {Function} click 头像被点击\r\n\t * @example <u-avatar :src=\"src\"></u-avatar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-avatar',\r\n\t\tprops: {\r\n\t\t\t// 背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'transparent'\r\n\t\t\t},\r\n\t\t\t// 头像路径\r\n\t\t\tsrc: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 尺寸，large-大，default-中等，mini-小，如果为数值，则单位为rpx\r\n\t\t\t// 宽度等于高度\r\n\t\t\tsize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\t// 头像模型，square-带圆角方形，circle-圆形\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'circle'\r\n\t\t\t},\r\n\t\t\t// 文字内容\r\n\t\t\ttext: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 图片的裁剪模型\r\n\t\t\timgMode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'aspectFill'\r\n\t\t\t},\r\n\t\t\t// 标识符\r\n\t\t\tindex: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 右上角性别角标，man-男，woman-女\r\n\t\t\tsexIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'man'\r\n\t\t\t},\r\n\t\t\t// 右下角的等级图标\r\n\t\t\tlevelIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'level'\r\n\t\t\t},\r\n\t\t\t// 右下角等级图标背景颜色\r\n\t\t\tlevelBgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 右上角性别图标的背景颜色\r\n\t\t\tsexBgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否显示性别图标\r\n\t\t\tshowSex: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否显示等级图标\r\n\t\t\tshowLevel: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\terror: false,\r\n\t\t\t\t// 头像的地址，因为如果加载错误，需要赋值为默认图片，props值无法修改，所以需要一个中间值\r\n                avatar: this.src ? this.src : base64Avatar, \r\n\t\t\t}\r\n\t\t},\r\n        watch: {\r\n            src(n) {\r\n                // 用户可能会在头像加载失败时，再次修改头像值，所以需要重新赋值\r\n                if(!n) {\r\n\t\t\t\t\t// 如果传入null或者''，或者undefined，显示默认头像\r\n\t\t\t\t\tthis.avatar = base64Avatar;\r\n\t\t\t\t\tthis.error = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.avatar = n;\r\n\t\t\t\t\tthis.error = false;\r\n\t\t\t\t}\r\n            }\r\n        },\r\n\t\tcomputed: {\r\n\t\t\twrapStyle() {\r\n\t\t\t\tlet style = {};  \r\n\t\t\t\tstyle.height = this.size == 'large' ? '120rpx' : this.size == 'default' ?\r\n\t\t\t\t'90rpx' : this.size == 'mini' ? '70rpx' : this.size + 'rpx';\r\n\t\t\t\tstyle.width = style.height;\r\n\t\t\t\tstyle.flex = `0 0 ${style.height}`;\r\n\t\t\t\tstyle.backgroundColor = this.bgColor;\r\n\t\t\t\tstyle.borderRadius = this.mode == 'circle' ? '500px' : '5px';\r\n\t\t\t\tif(this.text) style.padding = `0 6rpx`;\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\timgStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tstyle.borderRadius = this.mode == 'circle' ? '500px' : '5px';\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 取字符串的第一个字符\r\n\t\t\tuText() {\r\n\t\t\t\treturn String(this.text)[0];\r\n\t\t\t},\r\n\t\t\t// 性别图标的自定义样式\r\n\t\t\tuSexStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tif(this.sexBgColor) style.backgroundColor = this.sexBgColor;\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 等级图标的自定义样式\r\n\t\t\tuLevelStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tif(this.levelBgColor) style.backgroundColor = this.levelBgColor;\r\n\t\t\t\treturn style;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 图片加载错误时，显示默认头像\r\n\t\t\tloadError() {\r\n\t\t\t\tthis.error = true;\r\n                this.avatar = base64Avatar;\r\n\t\t\t},\r\n\t\t\tclick() {\r\n\t\t\t\tthis.$emit('click', this.index);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\t.u-avatar {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-flex;\t\t\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: $u-content-color;\r\n\t\tborder-radius: 10px;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t&__img {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t&__sex {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 32rpx;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\theight: 32rpx;\r\n\t\t\t@include vue-flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tborder-radius: 100rpx;\r\n\t\t\ttop: 5%;\r\n\t\t\tz-index: 1;\r\n\t\t\tright: -7%;\r\n\t\t\tborder: 1px #ffffff solid;\r\n\t\t\t\r\n\t\t\t&--man {\r\n\t\t\t\tbackground-color: $u-type-primary;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&--woman {\r\n\t\t\t\tbackground-color: $u-type-error;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&--none {\r\n\t\t\t\tbackground-color: $u-type-warning;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&__level {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 32rpx;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\theight: 32rpx;\r\n\t\t\t@include vue-flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tborder-radius: 100rpx;\r\n\t\t\tbottom: 5%; \r\n\t\t\tz-index: 1;\r\n\t\t\tright: -7%;\r\n\t\t\tborder: 1px #ffffff solid;\r\n\t\t\tbackground-color: $u-type-warning;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=style&index=0&id=22e236d3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-avatar.vue?vue&type=style&index=0&id=22e236d3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760724645\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}