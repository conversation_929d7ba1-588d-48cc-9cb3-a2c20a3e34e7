{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue?b20f", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue?8028", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue?0f57", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue?1c1a", "uni-app:///uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue?70f9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue?3f9e"], "names": ["props", "popup", "type", "default", "label", "value", "required", "placeholder", "readonly", "openSmooth", "boundingBox", "data", "showPopup", "isHeight", "height1", "width", "height", "showPicker", "methods", "moveHandle", "toImg", "undo", "toPop", "toDeleteImg", "toclear", "close", "deleteImg", "toDataURL", "Tomagnify", "isEmpty", "console", "quality", "success", "uni", "title", "icon", "fail", "complete", "beforeCreate", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACsC;;;AAGvG;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA0wB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2D9xB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC,mCAEA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAEA;IAKA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAEA;MAKA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;MACA;QACAC;QACAC;UACAF;UACA;YACA;cAEA;cAKA;cACA;cACA;YACA;cACAA;cACAG;gBACAC;gBACAC;cACA;YACA;UACA;YAEA;YAKA;YACA;YACA;UACA;QAEA;QACAC;UACAN;UACAG;YACAC;YACAC;UACA;QACA;QACAE;UACAP;QACA;MACA;IACA;EACA;EACAQ;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC5NA;AAAA;AAAA;AAAA;AAAy8C,CAAgB,mxCAAG,EAAC,C;;;;;;;;;;;ACA79C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./jp-signature-popup.vue?vue&type=template&id=1756e4df&scoped=true&\"\nvar renderjs\nimport script from \"./jp-signature-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./jp-signature-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jp-signature-popup.vue?vue&type=style&index=0&id=1756e4df&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1756e4df\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature-popup.vue?vue&type=template&id=1756e4df&scoped=true&\"", "var components\ntry {\n  components = {\n    jpSignature: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/jp-signature/components/jp-signature/jp-signature\" */ \"@/uni_modules/jp-signature/components/jp-signature/jp-signature.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature-popup.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div class=\"signature\">\r\n\t\t<div class=\"inputs\" v-if=\"!popup\">\r\n\t\t\t<div class=\"label\" :class=\"required?'labelqr':''\">{{label}}</div>\r\n\t\t\t<div>\r\n\t\t\t\t<div v-if=\"value\" class=\"images\">\r\n\t\t\t\t\t<image @tap=\"toImg\"  class=\"images\" mode=\"aspectFit\" :src=\"value\"></image>\r\n\t\t\t\t\t<view v-if=\"!readonly\" @click=\"toDeleteImg\" class=\"icons\">\r\n\t\t\t\t\t\t<view class=\"Deletes\">×</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-if=\"!value && !readonly\" class=\"explain\" @click=\"toPop\">\r\n\t\t\t\t\t{{placeholder?placeholder:'点击签名'}}\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<view class=\"bottomPopup\" v-if=\"showPopup\" @touchmove.stop.prevent=\"moveHandle\">\r\n\t\t\t\t<transition name=\"slide-up\" appear>\r\n\t\t\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t\t\t<view class=\"popup\">\r\n\t\t\t\t\t\t\t<div class=\"hader\" v-if=\"!isHeight\">\r\n\t\t\t\t\t\t\t\t<div @click=\"toclear\" style=\"opacity:0;position:relative;z-index:-1;\">取消</div>\r\n\t\t\t\t\t\t\t\t<div class=\"text\">{{label}}</div>\r\n\t\t\t\t\t\t\t\t<!-- <div @click=\"isEmpty\">确定</div> -->\r\n\t\t\t\t\t\t\t\t<div @click=\"toclear\">取消</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div :class=\"isHeight?'wgSignatureq':'wgSignature'\">\r\n\t\t\t\t\t\t\t\t<div v-if=\"isHeight\" key=\"999\" style=\"width: 750rpx ;height: 100vh;\">\r\n\t\t\t\t\t\t\t\t\t<jp-signature  :beforeDelay=\"200\" landscape disableScroll ref=\"signatureRef\" :openSmooth=\"openSmooth\" :penSize=\"6\" :bounding-box=\"boundingBox\"></jp-signature>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div v-else key=\"888\" style=\"width: 750rpx ;height: 35vh;\">\r\n\t\t\t\t\t\t\t\t\t<jp-signature :beforeDelay=\"200\" disableScroll ref=\"signatureRef\" :openSmooth=\"openSmooth\" :bounding-box=\"boundingBox\"  :penSize=\"3\"></jp-signature>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t  <div v-if=\"!isHeight\" class=\"appBut\" >\r\n\t\t\t\t\t\t\t\t\t  <!-- <div class=\"buts\" @click=\"undo\" >撤销</div> -->\r\n\t\t\t\t\t\t\t\t\t  <div class=\"buts\" @click=\"deleteImg\" >清除</div>\r\n\t\t\t\t\t\t\t\t\t  <div class=\"buts\" @click=\"Tomagnify\" >全屏</div>\r\n\t\t\t\t\t\t\t\t\t  <div class=\"buts\" style=\"background-color: #55aaff;color: #fff;\" @click=\"isEmpty\" >确定</div>\r\n\t\t\t\t\t\t\t\t  </div>\r\n\t\t\t\t\t\t\t\t  <div v-else class=\"appBut\" style=\"height: 80px;\">\r\n\t\t\t\t\t\t\t\t\t  <div class=\"butx\" @click=\"undo\" >撤销</div>\r\n\t\t\t\t\t\t\t\t\t  <div class=\"butx\" @click=\"deleteImg\">清除</div>\r\n\t\t\t\t\t\t\t\t\t  <div class=\"butx\" style=\"background-color: #55aaff;color: #fff;\" @click=\"Tomagnify\" >小屏</div>\r\n\t\t\t\t\t\t\t\t\t  <div class=\"butx\" @click=\"toclear\">取消</div>\r\n\t\t\t\t\t\t\t\t\t  <div class=\"butx\" style=\"background-color: #E59C36;color: #fff;\"  @click=\"isEmpty\">完成</div>\r\n\t\t\t\t\t\t\t\t  </div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<!-- ios底部安全距离 go -->\r\n\t\t\t\t\t\t\t<view class=\"aqjlViw\"></view>\r\n\t\t\t\t\t\t\t<!-- ios底部安全距离 end -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</transition>\r\n\t\t\t</view>\r\n\t\t\r\n\t</div>\r\n</template>\r\n<!-- 有项目需要开发的请联系 扣 - 371524845 -->\r\n<script>\r\n\t/**\r\n\t * 手写签名组件\r\n\t * 用于手写签名（弹框签名支持小屏和全屏）\r\n\t *\r\n\t *********参数********\r\n\t * label        选项名称\r\n\t * value        初始值String（支持bas64，url 等图片显示）\r\n\t * required     是否显示必填\r\n\t * placeholder  默认值\r\n\t * readonly     是否只读\r\n\t *\r\n\t * *********回调********\r\n\t * @input(e)   点击确认   e生成的图片数据(bas64)\r\n\t *\r\n\t *********方法********\r\n\t * isEmpty()     生成图片\r\n\t * deleteImg()   删除图片\r\n\t */\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tpopup: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\tlabel: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '手写签名',\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\trequired: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\tplaceholder: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '点击签名',\r\n\t\t\t},\r\n\t\t\treadonly: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\topenSmooth: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\tboundingBox: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tisHeight: false,\r\n\t\t\t\theight1: uni.getSystemInfoSync().windowWidth / 2,\r\n\t\t\t\twidth: uni.getSystemInfoSync().windowWidth, //实时屏幕宽度\r\n\t\t\t\theight: uni.getSystemInfoSync().windowHeight, //实时屏幕高度\r\n\t\t\t\tshowPicker: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmoveHandle(){\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\ttoImg(){\r\n\t\t\t\tthis.$emit('toImg',this.value)\r\n\t\t\t},\r\n\t\t\tundo() {\r\n\t\t\t\tthis.$refs.signatureRef.undo()\r\n\t\t\t},\r\n\t\t\ttoPop() {\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t},\r\n\t\t\ttoDeleteImg() {\r\n\t\t\t\t// #ifndef VUE3\r\n\t\t\t\tthis.$emit('input','')\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.$emit('update:value','')\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\ttoclear() {\r\n\t\t\t\tthis.isHeight = false\r\n\t\t\t\tthis.showPopup = false\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.isHeight = false\r\n\t\t\t\tthis.showPopup = false\r\n\t\t\t\tthis.$refs.signatureRef.clear()\r\n\t\t\t},\r\n\t\t\tdeleteImg() {\r\n\t\t\t\tthis.$refs.signatureRef.clear()\r\n\t\t\t},\r\n\t\t\ttoDataURL(url) {\r\n\t\t\t\t// #ifndef VUE3\r\n\t\t\t\tthis.$emit('input',url)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.$emit('update:value',url)\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.showPicker = false\r\n\t\t\t},\r\n\t\t\tTomagnify() {\r\n\t\t\t\tthis.isHeight = !this.isHeight\r\n\t\t\t\tthis.$refs.signatureRef.clear()\r\n\t\t\t},\r\n\t\t\tisEmpty() {\r\n\t\t\t\tconsole.log('搜索1')\r\n\t\t\t\tthis.$refs.signatureRef.canvasToTempFilePath({\r\n\t\t\t\t\tquality: 0.8,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(this.required,'res',res)\r\n\t\t\t\t\t\tif (this.required) {\r\n\t\t\t\t\t\t\tif (!res.isEmpty) {\r\n\t\t\t\t\t\t\t\t// #ifndef VUE3\r\n\t\t\t\t\t\t\t\tthis.$emit('input', res.tempFilePath)\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t\t\t\t\tthis.$emit('update:value',res.tempFilePath)\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\tthis.$emit('change', res.tempFilePath)\r\n\t\t\t\t\t\t\t\tthis.isHeight = false\r\n\t\t\t\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.log('请先签名hah')\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '请先签名',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// #ifndef VUE3\r\n\t\t\t\t\t\t\tthis.$emit('input', res.tempFilePath)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t\t\t\tthis.$emit('update:value',res.tempFilePath)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tthis.$emit('change', res.tempFilePath)\r\n\t\t\t\t\t\t\tthis.isHeight = false\r\n\t\t\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (fail) => {\r\n\t\t\t\t\t\tconsole.log(fail,'fail')\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请先签名',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: (err) => {\r\n\t\t\t\t\t\tconsole.log(err,'err')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t\tbeforeCreate() {},\r\n\t\tcreated() {}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.wgSignatureq{\r\n\t\t\r\n\t}\r\n\t.appBut{\r\n\t\tdisplay: flex;justify-content: flex-start;align-items: center;text-align: center;height: 50px;line-height: 35px;\r\n\t .buts{\r\n\t\t color: #333;flex: 1;margin: 0 15px;background-color: #ccc;border-radius: 5px;height: 35px;\r\n\t  }\r\n\t  .butx{\r\n\t\t  color: #333;flex: 1;margin: 0 5px;background-color: #ccc;border-radius: 5px;height: 35px;\r\n\t\t  transform: rotate(90deg);\r\n\t  }\r\n\t}\r\n\t\r\n\t.bottomPopup {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 999;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\r\n\t\t.popup-content {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\t// top: 0;\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t}\r\n\r\n\t\t.slide-up-enter-active,\r\n\t\t.slide-up-leave-active {\r\n\t\t\ttransition: all .3s ease;\r\n\t\t}\r\n\r\n\t\t.slide-up-enter,\r\n\t\t.slide-up-leave-to {\r\n\t\t\ttransform: translateY(100%);\r\n\t\t}\r\n\t}\r\n\r\n\t.signature {\r\n\t\t.inputs {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 10px 16px;\r\n\r\n\t\t\t.label {\r\n\t\t\t\tline-height: 35px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\r\n\t\t\t.labelqr:before {\r\n\t\t\t\tcontent: \"*\";\r\n\t\t\t\tcolor: #f00;\r\n\t\t\t}\r\n\r\n\t\t\t.explain {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbackground-color: #f1f1f1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 40px;\r\n\t\t\t\tborder: 1px dotted #ccc;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\r\n\t\t\t.Deletes {\r\n\t\t\t\tborder: 1px solid #f00;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tcolor: #f00;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tline-height: 30rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.images {\r\n\t\t\twidth: 300rpx;\r\n\t\t\theight: 150rpx;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.icons {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.popup {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.hader {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\ttext-align: center;\r\n\t\theight: 45px;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t\talign-items: center;\r\n\r\n\t\tdiv {\r\n\t\t\ttext-align: center;\r\n\t\t\twidth: 80px;\r\n\t\t\tcolor: #E59C36;\r\n\t\t}\r\n\r\n\t\t.text {\r\n\t\t\tcolor: #333;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\t}\r\n  \r\n  \r\n</style>", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature-popup.vue?vue&type=style&index=0&id=1756e4df&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature-popup.vue?vue&type=style&index=0&id=1756e4df&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751622803\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}