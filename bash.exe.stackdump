Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA8F0, 0007FFFF97F0) msys-2.0.dll+0x1FE8E
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (000210286019, 0007FFFFA7A8, 0007FFFFA8F0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA8F0  000210068E24 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABD0  00021006A225 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF91CD70000 ntdll.dll
7FF91BF90000 KERNEL32.DLL
7FF919E60000 KERNELBASE.dll
7FF91B7A0000 USER32.dll
7FF91A9D0000 win32u.dll
7FF91C330000 GDI32.dll
7FF91A400000 gdi32full.dll
7FF91A240000 msvcp_win.dll
7FF91A2E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF91C360000 advapi32.dll
7FF91C0E0000 msvcrt.dll
7FF91CC10000 sechost.dll
7FF91A860000 bcrypt.dll
7FF91AAA0000 RPCRT4.dll
7FF9195C0000 CRYPTBASE.DLL
7FF91A7E0000 bcryptPrimitives.dll
7FF91CBD0000 IMM32.DLL
