<template>
  <view class="publish-container">
    <scroll-view class="content" scroll-y>
      <!-- 用户信息 -->
      <view class="user-section">
        <u-avatar :src="userInfo.avatar" size="40"></u-avatar>
        <text class="username">{{ userInfo.nickname }}</text>
      </view>

      <!-- 标题输入 -->
      <view class="title-section">
        <input v-model="postTitle" class="title-input" placeholder="标题（可选）" :maxlength="50" />
        <view class="title-char-count">{{ postTitle.length }}/50</view>
      </view>

      <!-- 文字输入 -->
      <view class="text-section">
        <textarea
          v-model="postContent"
          class="content-input"
          placeholder="分享你的生活..."
          :maxlength="500"
          auto-height
          :show-confirm-bar="false"
        />
        <view class="char-count">{{ postContent.length }}/500</view>
      </view>

      <!-- 图片上传 -->
      <view class="image-section">
        <view class="image-grid">
          <view v-for="(image, index) in selectedImages" :key="index" class="image-item">
            <image :src="image" class="uploaded-image" mode="aspectFill" />
            <view class="delete-btn" @click="removeImage(index)">
              <u-icon name="close" color="#fff" size="16"></u-icon>
            </view>
          </view>
          <view v-if="selectedImages.length < 9" class="add-image-btn" @click="chooseImage">
            <u-icon name="camera" color="#999" size="32"></u-icon>
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>

      <!-- 功能选项 -->
      <view class="options-section">
        <!-- 话题选择 -->
        <view class="option-item" @click="selectTopic">
          <view class="option-left">
            <u-icon name="tags" color="#2979ff" size="20"></u-icon>
            <text class="option-text">添加话题</text>
          </view>
          <view class="option-right">
            <text
              v-if="selectedTopics.length"
              class="selected-topics"
            >{{ selectedTopics.map(t => '#' + t).join(' ') }}</text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>

        <!-- 位置定位 -->
        <view class="option-item" @click="selectLocation">
          <view class="option-left">
            <u-icon name="map" color="#2979ff" size="20"></u-icon>
            <text class="option-text">添加位置</text>
          </view>
          <view class="option-right">
            <view v-if="selectedLocation" class="location-selected">
              <view class="location-info-inline">
                <text class="selected-location">{{ selectedLocation.name }}</text>
                <text class="selected-address">{{ selectedLocation.address }}</text>
              </view>
              <u-icon name="close-circle-fill" color="#999" size="18" @click.stop="clearLocation"></u-icon>
            </view>
            <u-icon v-else name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>

        <!-- 可见性设置 -->
        <view class="option-item" @click="setVisibility">
          <view class="option-left">
            <u-icon name="eye" color="#2979ff" size="20"></u-icon>
            <text class="option-text">可见性</text>
          </view>
          <view class="option-right">
            <text class="visibility-text">{{ visibilityText }}</text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 提醒文字 -->
      <view class="tips-section">
        <text class="tips-text">发布即表示同意《社区公约》，请文明发言，共建和谐社区</text>
      </view>

      <!-- 发布按钮 -->
      <view class="publish-section">
        <text
          class="publish-btn"
          :class="{ disabled: !canPublish }"
          @click="publishPost"
        >{{ publishing ? '发布中...' : '发布' }}</text>
      </view>
    </scroll-view>

    <!-- 话题选择弹窗 -->
    <u-popup v-model="showTopicModal" mode="bottom" border-radius="20">
      <view class="topic-modal">
        <view class="modal-header">
          <text class="modal-title">选择话题</text>
          <u-icon name="close" @click="showTopicModal = false"></u-icon>
        </view>
        <view class="topic-search">
          <u-input
            v-model="topicKeyword"
            placeholder="搜索话题"
            prefix-icon="search"
            @input="searchTopics"
          />
        </view>
        <view class="topic-list">
          <view
            v-for="topic in filteredTopics"
            :key="topic.id"
            class="topic-option"
            :class="{ selected: selectedTopics.includes(topic.name) }"
            @click="toggleTopic(topic)"
          >
            <text class="topic-name">#{{ topic.name }}</text>
            <text class="topic-count">{{ topic.postCount }}条帖子</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 位置选择弹窗 -->
    <u-popup v-model="showLocationModal" mode="bottom" border-radius="20">
      <view class="location-modal">
        <view class="modal-header">
          <text class="modal-title">选择位置</text>
          <u-icon name="close" @click="showLocationModal = false"></u-icon>
        </view>
        <view class="location-list">
          <view
            v-for="location in nearbyLocations"
            :key="location.id"
            class="location-option"
            @click="selectLocationItem(location)"
          >
            <u-icon name="map-pin" color="#2979ff" size="16"></u-icon>
            <view class="location-info">
              <text class="location-name">{{ location.name }}</text>
              <text class="location-address">{{ location.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import {
  createPost,
  getHotTags,
  getUserProfile,
  uploadPostImage,
  uploadPostImages
} from "@/utils/socialApi.js";

export default {
  name: "SocialPublish",
  data() {
    return {
      userInfo: {
        avatar: "/static/images/toux.png",
        nickname: "加载中..."
      },
      postTitle: "",
      postContent: "",
      selectedImages: [],
      coverImageUrl: "", // 封面图片URL
      selectedTopics: [],
      selectedLocation: null,
      visibility: "public", // public, friends, private
      publishing: false, // 发布状态

      showTopicModal: false,
      showLocationModal: false,

      topicKeyword: "",
      allTopics: [
        { id: 1, name: "街舞", postCount: 1234 },
        { id: 2, name: "现代舞", postCount: 856 },
        { id: 3, name: "芭蕾", postCount: 642 },
        { id: 4, name: "拉丁舞", postCount: 789 },
        { id: 5, name: "爵士舞", postCount: 456 },
        { id: 6, name: "民族舞", postCount: 321 },
        { id: 7, name: "古典舞", postCount: 298 },
        { id: 8, name: "舞蹈教学", postCount: 567 },
        { id: 9, name: "舞蹈比赛", postCount: 234 },
        { id: 10, name: "舞蹈培训", postCount: 189 }
      ],

      nearbyLocations: [
        {
          id: 1,
          name: "星巴克咖啡",
          address: "北京市朝阳区三里屯太古里"
        },
        {
          id: 2,
          name: "三里屯太古里",
          address: "北京市朝阳区三里屯路19号"
        },
        {
          id: 3,
          name: "朝阳公园",
          address: "北京市朝阳区朝阳公园南路1号"
        }
      ]
    };
  },
  computed: {
    canPublish() {
      return (
        this.postTitle.trim().length > 0 ||
        this.postContent.trim().length > 0 ||
        this.selectedImages.length > 0
      );
    },

    visibilityText() {
      const map = {
        public: "公开",
        friends: "仅朋友可见",
        private: "仅自己可见"
      };
      return map[this.visibility];
    },

    filteredTopics() {
      if (!this.topicKeyword) return this.allTopics;
      return this.allTopics.filter(topic =>
        topic.name.includes(this.topicKeyword)
      );
    }
  },

  onLoad(options) {
    console.log("=== 发布页面加载 ===");
    console.log("onLoad方法被调用了", options);

    // 首先检查用户登录状态
    if (!this.checkUserLogin()) {
      return; // 如果未登录，直接返回，不执行后续操作
    }

    // 显示一个提示确认方法被调用
    uni.showToast({
      title: "onLoad被调用",
      icon: "none",
      duration: 2000
    });

    this.testApiConnection();
    this.loadUserInfo();
    this.loadHotTopics();

    // 检查是否从图片选择跳转过来
    if (options && options.fromImageSelect === "true") {
      this.handleImageSelectFromTabBar();
    }
  },

  mounted() {
    console.log("=== mounted生命周期被调用 ===");

    // 如果onLoad没有被调用，在这里也执行一次
    if (this.userInfo.nickname === "加载中...") {
      console.log("onLoad可能没有执行，在mounted中重新执行");
      this.testApiConnection();
      this.loadUserInfo();
      this.loadHotTopics();
    }
  },
  methods: {
    // 检查用户登录状态
    checkUserLogin() {
      const token = uni.getStorageSync("token");
      const userId = uni.getStorageSync("userid");

      if (!token || !userId) {
        console.log("用户未登录，跳转到登录页");
        uni.showToast({
          title: "请先登录",
          icon: "none",
          duration: 2000
        });
        setTimeout(() => {
          uni.navigateTo({
            url: "/pages/login/login"
          });
        }, 1500);
        return false;
      }

      return true;
    },

    // 测试API连接
    async testApiConnection() {
      console.log("测试API连接...");
      try {
        const response = await uni.request({
          url: "http://localhost:8101/api/health",
          method: "GET",
          timeout: 5000
        });
        console.log("API连接测试结果:", response);

        if (response.statusCode === 200) {
          console.log("✅ 后端服务连接正常");
        } else {
          console.log("❌ 后端服务响应异常:", response.statusCode);
        }
      } catch (error) {
        console.error("❌ 后端服务连接失败:", error);
        uni.showToast({
          title: "后端服务连接失败",
          icon: "none",
          duration: 3000
        });
      }
    },

    // 加载用户信息
    async loadUserInfo() {
      console.log("开始加载用户信息...");
      try {
        // 从缓存中获取当前用户ID
        const userId = uni.getStorageSync("userid");
        console.log("调用getUserProfile，用户ID:", userId);

        const result = await getUserProfile(userId);

        console.log("获取用户信息结果:", result);

        if (result && result.code === 0 && result.data) {
          this.userInfo = {
            avatar:
              "https://file.foxdance.com.cn" +
              result.data.avatar +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
            nickname: result.data.nickname || "无名氏"
          };
        } else {
          console.warn("获取用户信息失败:", result);
          this.userInfo = {
            avatar: "/static/images/toux.png",
            nickname: "用户"
          };
        }
      } catch (error) {
        console.error("加载用户信息失败:", error);
        console.error("错误详情:", error.message || error);

        uni.showToast({
          title: "用户信息加载失败",
          icon: "none",
          duration: 2000
        });

        this.userInfo = {
          avatar: "/static/images/toux.png",
          nickname: "用户"
        };
      }
    },

    // 加载热门话题
    async loadHotTopics() {
      console.log("开始加载热门话题...");
      try {
        const hotTags = await getHotTags(20);
        console.log("获取热门话题结果:", hotTags);

        if (
          hotTags &&
          hotTags.code === 0 &&
          hotTags.data &&
          hotTags.data.length > 0
        ) {
          this.allTopics = hotTags.data.map(tag => ({
            id: tag.id,
            name: tag.name,
            postCount: tag.useCount || 0
          }));
          console.log("话题加载成功，数量:", this.allTopics.length);
        } else if (hotTags && hotTags.length > 0) {
          // 兼容直接返回数组的情况
          this.allTopics = hotTags.map(tag => ({
            id: tag.id,
            name: tag.name,
            postCount: tag.useCount || 0
          }));
          console.log("话题加载成功（数组格式），数量:", this.allTopics.length);
        } else {
          console.log("没有获取到话题数据，hotTags:", hotTags);
        }
      } catch (error) {
        console.error("加载热门话题失败:", error);
        console.error("错误详情:", error.message || error);

        uni.showToast({
          title: "话题加载失败",
          icon: "none",
          duration: 2000
        });

        // 使用默认话题列表
        this.allTopics = [
          { id: 1, name: "生活", postCount: 0 },
          { id: 2, name: "美食", postCount: 0 },
          { id: 3, name: "旅行", postCount: 0 },
          { id: 4, name: "摄影", postCount: 0 },
          { id: 5, name: "时尚", postCount: 0 }
        ];
        console.log("使用默认话题列表");
      }
    },
    goBack() {
      if (this.postTitle || this.postContent || this.selectedImages.length) {
        uni.showModal({
          title: "提示",
          content: "确定要放弃编辑吗？",
          success: res => {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    },

    async chooseImage() {
      const maxCount = 9 - this.selectedImages.length;

      uni.chooseImage({
        count: maxCount,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: async res => {
          const tempFilePaths = res.tempFilePaths;
          console.log("🔥 选择图片成功，开始上传到COS:", tempFilePaths);

          // 显示上传进度
          uni.showLoading({
            title: "上传图片中...",
            mask: true
          });

          try {
            // 使用批量上传API
            console.log("🔥 开始批量上传图片:", tempFilePaths);

            const result = await uploadPostImages(tempFilePaths);
            console.log("🔥 批量上传结果:", result);

            // 隐藏加载提示
            uni.hideLoading();

            if (result.code === 0 && result.data) {
              // 获取上传结果
              const { images, coverImage } = result.data;

              // 更新选中的图片列表
              this.selectedImages = images;

              // 保存封面图片URL（用于发布时传给后端）
              this.coverImageUrl = coverImage;

              console.log("✅ 批量上传成功:", {
                images: images.length,
                coverImage
              });

              this.$u.toast(`成功上传${images.length}张图片`);
            } else {
              console.error("❌ 批量上传失败:", result);
              this.$u.toast("图片上传失败");
            }
          } catch (error) {
            uni.hideLoading();
            console.error("❌ 图片上传过程异常:", error);
            this.$u.toast("图片上传失败");
          }
        },
        fail: error => {
          console.error("❌ 选择图片失败:", error);
          this.$u.toast("选择图片失败");
        }
      });
    },

    removeImage(index) {
      this.selectedImages.splice(index, 1);
    },

    // 处理从TabBar图片选择跳转过来的情况
    async handleImageSelectFromTabBar() {
      console.log("🔥 处理从TabBar选择的图片");

      try {
        // 从全局数据中获取选择的图片
        const app = getApp();
        const selectedImages = app.globalData.selectedImages;

        if (selectedImages && selectedImages.length > 0) {
          console.log("🔥 获取到选择的图片:", selectedImages);

          // 显示上传进度
          uni.showLoading({
            title: "上传图片中...",
            mask: true
          });

          try {
            // 使用批量上传API
            const result = await uploadPostImages(selectedImages);
            console.log("🔥 批量上传结果:", result);

            // 隐藏加载提示
            uni.hideLoading();

            if (result.code === 0 && result.data) {
              // 获取上传结果
              const { images, coverImage } = result.data;

              // 更新选中的图片列表
              this.selectedImages = images;

              // 保存封面图片URL
              this.coverImageUrl = coverImage;

              console.log("✅ 批量上传成功:", {
                images: images.length,
                coverImage
              });

              this.$u.toast(`成功上传${images.length}张图片`);
            } else {
              console.error("❌ 批量上传失败:", result);
              this.$u.toast("图片上传失败");
            }
          } catch (uploadError) {
            // 隐藏加载提示
            uni.hideLoading();

            console.error("❌ 批量上传异常:", uploadError);
            this.$u.toast(uploadError.message || "图片上传失败");
          }

          // 清除全局数据
          app.globalData.selectedImages = null;
        }
      } catch (error) {
        console.error("❌ 处理TabBar图片选择失败:", error);
        uni.hideLoading();
        this.$u.toast("处理图片失败");
      }
    },

    selectTopic() {
      this.showTopicModal = true;
    },

    toggleTopic(topic) {
      const index = this.selectedTopics.indexOf(topic.name);
      if (index > -1) {
        this.selectedTopics.splice(index, 1);
      } else {
        if (this.selectedTopics.length < 3) {
          this.selectedTopics.push(topic.name);
        } else {
          this.$u.toast("最多选择3个话题");
        }
      }
    },

    searchTopics() {
      // 搜索话题逻辑
    },

    selectLocation() {
      console.log("打开位置选择...");

      // 使用uni.chooseLocation打开地图选择位置
      uni.chooseLocation({
        success: res => {
          console.log("位置选择成功:", res);

          // 构建位置对象
          this.selectedLocation = {
            name: res.name || res.address,
            address: res.address,
            latitude: res.latitude,
            longitude: res.longitude
          };

          uni.showToast({
            title: "位置选择成功",
            icon: "success",
            duration: 1500
          });
        },
        fail: err => {
          console.error("位置选择失败:", err);

          if (err.errMsg && err.errMsg.includes("cancel")) {
            // 用户取消选择，不显示错误提示
            return;
          }

          uni.showToast({
            title: "位置选择失败",
            icon: "none",
            duration: 2000
          });

          // 如果地图选择失败，回退到弹窗选择
          this.showLocationModal = true;
        }
      });
    },

    selectLocationItem(location) {
      this.selectedLocation = location;
      this.showLocationModal = false;
    },

    clearLocation() {
      this.selectedLocation = null;
      uni.showToast({
        title: "已清除位置",
        icon: "success",
        duration: 1000
      });
    },

    setVisibility() {
      uni.showActionSheet({
        itemList: ["公开", "仅朋友可见", "仅自己可见"],
        success: res => {
          const visibilityMap = ["public", "friends", "private"];
          this.visibility = visibilityMap[res.tapIndex];
        }
      });
    },

    async publishPost() {
      if (!this.canPublish || this.publishing) return;

      // 检查用户登录状态
      const userId = uni.getStorageSync("userid");
      if (!userId) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
          duration: 2000
        });
        setTimeout(() => {
          uni.navigateTo({
            url: "/pages/login/login"
          });
        }, 1500);
        return;
      }

      this.publishing = true;

      try {
        // 构建发布数据 - 符合PostCreateDTO格式
        const postData = {
          userId: Number(userId), // 从缓存获取当前用户ID
          title: this.postTitle.trim() || null, // 单独发送标题字段
          content: this.postContent.trim(),
          images: this.selectedImages,
          coverImage: this.coverImageUrl, // 封面图片URL
          tags: this.selectedTopics.map(topic => topic.name || topic),
          locationName: this.selectedLocation?.name || "",
          locationLatitude: this.selectedLocation?.latitude || null,
          locationLongitude: this.selectedLocation?.longitude || null,
          locationAddress: this.selectedLocation?.address || "",
          isPublic: this.visibility === "public" ? 1 : 0,
          status: 1 // 1-已发布
        };

        console.log("发布帖子数据:", postData);

        // 调用发布API
        const result = await createPost(postData);

        console.log("发布API返回结果:", result);

        if (result && result.code === 0) {
          this.$u.toast("发布成功");
          // 返回主页面
          uni.navigateBack();
        } else {
          this.$u.toast(result?.message || "发布失败，请重试");
          this.publishing = false;
        }
      } catch (error) {
        console.error("发布帖子失败:", error);
        this.$u.toast("网络错误，请重试");
        this.publishing = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.publish-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.content {
  padding: 20px 16px;
  padding-top: calc(20px + var(--status-bar-height)); /* 状态栏高度 */
  padding-bottom: calc(20px + env(safe-area-inset-bottom)); /* 安全区域 */
  width: auto;
}

.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.username {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 标题输入区域 */
.title-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
}

.title-input {
  width: 100%;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
  line-height: 1.4;
}

.title-input::placeholder {
  color: #c0c4cc;
  font-weight: 400;
}

.title-char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.text-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
}

.content-input {
  width: 100%;
  min-height: 120px;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}

.char-count {
  position: absolute;
  bottom: 12px;
  right: 16px;
  font-size: 12px;
  color: #999;
}

.image-section {
  margin-bottom: 16px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  position: relative;
  width: calc(33.33% - 6px);
  height: 100px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-image-btn {
  width: calc(33.33% - 6px);
  height: 100px;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.options-section {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-left {
  display: flex;
  align-items: center;
}

.option-text {
  margin-left: 12px;
  font-size: 15px;
  color: #333;
}

.option-right {
  display: flex;
  align-items: center;
}

.selected-topics,
.selected-location,
.visibility-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

/* 位置选择相关样式 */
.location-selected {
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 200px;
}

.location-info-inline {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
}

.selected-location {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected-address {
  font-size: 12px;
  color: #999;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.tips-section {
  padding: 16px;
}

.tips-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  text-align: center;
}

/* 发布按钮区域 */
.publish-section {
  padding: 24px 16px;
  display: flex;
  justify-content: center;
}

.publish-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  padding: 14px 48px;
  border-radius: 28px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  min-width: 120px;
}

.publish-btn.disabled {
  background: #e4e7ed;
  color: #c0c4cc;
  box-shadow: none;
}

.publish-btn:not(.disabled):active {
  transform: scale(0.95);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.4);
}

.topic-modal,
.location-modal {
  background: #fff;
  border-radius: 20px 20px 0 0;
  max-height: 60vh;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.topic-search {
  padding: 16px 20px;
}

.topic-list,
.location-list {
  max-height: 300px;
  overflow-y: auto;
}

.topic-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.topic-option.selected {
  background: #f0f8ff;
}

.topic-name {
  font-size: 15px;
  color: #333;
}

.topic-count {
  font-size: 12px;
  color: #999;
}

.location-option {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.location-info {
  margin-left: 12px;
  flex: 1;
}

.location-name {
  font-size: 15px;
  color: #333;
  display: block;
}

.location-address {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}
</style>
