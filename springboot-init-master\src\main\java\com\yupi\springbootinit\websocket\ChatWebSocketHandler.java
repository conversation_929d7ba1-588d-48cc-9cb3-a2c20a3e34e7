package com.yupi.springbootinit.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.service.BaUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 聊天WebSocket处理器
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Component
@Slf4j
public class ChatWebSocketHandler implements WebSocketHandler {

    private final BaUserService baUserService;
    private final ObjectMapper objectMapper;

    // 存储用户连接会话
    private final Map<Long, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    // 存储聊天房间
    private final Map<String, Map<Long, WebSocketSession>> chatRooms = new ConcurrentHashMap<>();

    public ChatWebSocketHandler(BaUserService baUserService, ObjectMapper objectMapper) {
        this.baUserService = baUserService;
        this.objectMapper = objectMapper;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket连接建立: {}", session.getId());

        // 从URL参数中获取用户ID和token
        URI uri = session.getUri();
        if (uri != null) {
            String query = uri.getQuery();
            Map<String, String> params = parseQueryString(query);

            String userIdStr = params.get("userId");
            String token = params.get("token");

            if (userIdStr != null && token != null) {
                try {
                    Long userId = Long.parseLong(userIdStr);

                    // 验证用户身份
                    BaUser user = baUserService.getById(userId);
                    if (user != null) {
                        // 存储用户会话
                        userSessions.put(userId, session);
                        session.getAttributes().put("userId", userId);

                        log.info("用户 {} 连接成功", userId);

                        // 发送连接成功消息
                        sendMessage(session, Map.of(
                                "type", "connected",
                                "message", "连接成功",
                                "userId", userId));
                    } else {
                        log.warn("用户验证失败: {}", userId);
                        session.close(CloseStatus.NOT_ACCEPTABLE.withReason("用户验证失败"));
                    }
                } catch (NumberFormatException e) {
                    log.error("用户ID格式错误: {}", userIdStr);
                    session.close(CloseStatus.BAD_DATA.withReason("用户ID格式错误"));
                }
            } else {
                log.warn("缺少必要参数: userId={}, token={}", userIdStr, token);
                session.close(CloseStatus.BAD_DATA.withReason("缺少必要参数"));
            }
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String payload = message.getPayload().toString();
        log.info("收到WebSocket消息: {}", payload);

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String type = (String) messageData.get("type");

            Long userId = (Long) session.getAttributes().get("userId");
            if (userId == null) {
                log.warn("未认证的会话尝试发送消息");
                return;
            }

            switch (type) {
                case "join":
                    handleJoinRoom(session, messageData, userId);
                    break;
                case "message":
                    handleChatMessage(session, messageData, userId);
                    break;
                case "typing":
                    handleTypingStatus(session, messageData, userId);
                    break;
                case "heartbeat":
                    handleHeartbeat(session, userId);
                    break;
                default:
                    log.warn("未知消息类型: {}", type);
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        Long userId = (Long) session.getAttributes().get("userId");
        if (userId != null) {
            // 移除用户会话
            userSessions.remove(userId);

            // 从所有聊天房间中移除用户
            chatRooms.values().forEach(room -> room.remove(userId));

            log.info("用户 {} 断开连接: {}", userId, closeStatus);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理加入聊天房间
     */
    private void handleJoinRoom(WebSocketSession session, Map<String, Object> messageData, Long userId) {
        String chatId = (String) messageData.get("chatId");
        if (chatId != null) {
            chatRooms.computeIfAbsent(chatId, k -> new ConcurrentHashMap<>()).put(userId, session);
            session.getAttributes().put("chatId", chatId);

            log.info("用户 {} 加入聊天房间: {}", userId, chatId);

            // 通知房间内其他用户
            broadcastToRoom(chatId, Map.of(
                    "type", "online",
                    "data", Map.of("userId", userId, "status", "online")), userId);
        }
    }

    /**
     * 处理聊天消息
     */
    private void handleChatMessage(WebSocketSession session, Map<String, Object> messageData, Long userId) {
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) messageData.get("data");
        if (data != null) {
            String chatId = session.getAttributes().get("chatId").toString();

            // 广播消息到聊天房间（排除发送者，避免重复接收）
            broadcastToRoom(chatId, Map.of(
                    "type", "message",
                    "data", data), userId); // 排除发送者

            log.info("用户 {} 在房间 {} 发送消息，已广播给其他用户", userId, chatId);
        }
    }

    /**
     * 处理输入状态
     */
    private void handleTypingStatus(WebSocketSession session, Map<String, Object> messageData, Long userId) {
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) messageData.get("data");
        if (data != null) {
            String chatId = session.getAttributes().get("chatId").toString();

            // 广播输入状态到聊天房间（排除发送者）
            broadcastToRoom(chatId, Map.of(
                    "type", "typing",
                    "data", Map.of(
                            "userId", userId,
                            "isTyping", data.get("isTyping"))),
                    userId);
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(WebSocketSession session, Long userId) {
        sendMessage(session, Map.of(
                "type", "heartbeat",
                "timestamp", System.currentTimeMillis()));
    }

    /**
     * 向聊天房间广播消息
     */
    private void broadcastToRoom(String chatId, Map<String, Object> message, Long excludeUserId) {
        Map<Long, WebSocketSession> room = chatRooms.get(chatId);
        if (room != null) {
            room.forEach((userId, session) -> {
                if (excludeUserId == null || !excludeUserId.equals(userId)) {
                    sendMessage(session, message);
                }
            });
        }
    }

    /**
     * 发送消息到指定会话
     */
    private void sendMessage(WebSocketSession session, Object message) {
        try {
            if (session.isOpen()) {
                String jsonMessage = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(jsonMessage));
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }

    /**
     * 解析URL查询字符串
     */
    private Map<String, String> parseQueryString(String query) {
        Map<String, String> params = new ConcurrentHashMap<>();
        if (query != null) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return params;
    }

    /**
     * 向指定用户发送消息
     */
    public void sendMessageToUser(Long userId, Object message) {
        WebSocketSession session = userSessions.get(userId);
        if (session != null) {
            sendMessage(session, message);
        }
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }
}
