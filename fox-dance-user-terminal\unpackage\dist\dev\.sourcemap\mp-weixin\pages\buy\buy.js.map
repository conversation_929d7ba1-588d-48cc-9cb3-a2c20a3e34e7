{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/buy.vue?e6cb", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/buy.vue?6fe7", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/buy.vue?67c9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/buy.vue?ef0b", "uni-app:///pages/buy/buy.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/buy.vue?5868", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/buy/buy.vue?b500"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "membershipCard", "coursePackage", "pointsMall", "data", "bigType", "bgColor", "qj<PERSON>ton", "onShow", "console", "uni", "onLoad", "onPageScroll", "onReachBottom", "methods", "bigTab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AAC4L;AAC5L,gBAAgB,6LAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiCjvB;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;;IAEA;IACA;MACA;QACA;MACA;IACA;IAEAC;IACAC;IAEA;MACA;QACA;MACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;MACAA;MACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAD;EACA;EACAE;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAJ;IACA;MACA;QACA;MACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAK;IACAC;MACA;MACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAA42C,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACAh4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/buy/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=63838ed8&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/buy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=63838ed8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"buy\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t\r\n\t\t\t<view class=\"buy_one\">\r\n\t\t\t\t<!-- <view :class=\"bigType == 0 ? 'buy_one_ac' : ''\" @click=\"bigTab(0)\">会员卡</view> -->\r\n\t\t\t\t<view :class=\"bigType == 2 ? 'buy_one_ac' : ''\" @click=\"bigTab(2)\">周边商城</view>\r\n\t\t\t\t<view :class=\"bigType == 1 ? 'buy_one_ac' : ''\" @click=\"bigTab(1)\">课包</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 会员卡 -->\r\n\t\t\t<membershipCard v-show=\"bigType == 0\" ref=\"membershipCardRef\" />\r\n\t\t\t<!-- 会员卡 -->\r\n\t\t\t\r\n\t\t\t<!-- 课包 -->\r\n\t\t\t<coursePackage v-show=\"bigType == 1\" ref=\"coursePackageRef\" />\r\n\t\t\t<!-- 课包 -->\r\n\t\t\t\r\n\t\t\t<!-- 积分商城 -->\r\n\t\t\t<pointsMall v-show=\"bigType == 2\" ref=\"pointsMallRef\" />\r\n\t\t\t<!-- 积分商城 -->\r\n\r\n\t\t\t<tabbar ref=\"tabbar\" :current=\"1\"></tabbar>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport tabbar from '@/components/tabbar.vue'\r\nimport membershipCard from './membershipCard'\r\nimport coursePackage from '../buy/coursePackage/coursePackage.vue'\r\nimport pointsMall from '../buy/pointsMall/pointsMall.vue'\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\ttabbar,\r\n\t\tmembershipCard,\r\n\t\tcoursePackage,\r\n\t\tpointsMall\r\n\t},                                                                          \r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tbigType:2,\r\n\t\t\tbgColor:'#fff',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t// 安全获取storeInfo，避免undefined错误\r\n\t\tconst storeInfo = uni.getStorageSync('storeInfo')\r\n\t\tthis.qjbutton = (storeInfo && storeInfo.button) ? storeInfo.button : '#131315'\r\n\r\n\t\t// 使用$nextTick确保组件已挂载，并添加安全检查\r\n\t\tthis.$nextTick(() => {\r\n\t\t\tif (this.$refs.tabbar && typeof this.$refs.tabbar.setColor === 'function') {\r\n\t\t\t\tthis.$refs.tabbar.setColor();\r\n\t\t\t}\r\n\t\t})\r\n\r\n\t\tconsole.log(this.qjbutton,'this.qjbutton')\r\n\t\tuni.hideTabBar()\r\n\r\n\t\tif(this.bigType == 0){\r\n\t\t\tif (this.$refs.membershipCardRef && typeof this.$refs.membershipCardRef.onLoadData === 'function') {\r\n\t\t\t\tthis.$refs.membershipCardRef.onLoadData();\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(this.bigType == 1){\r\n\t\t\tif (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onLoadData === 'function') {\r\n\t\t\t\tthis.$refs.coursePackageRef.onLoadData();\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(this.bigType == 2){\r\n\t\t\tif (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onLoadData === 'function') {\r\n\t\t\t\tthis.$refs.pointsMallRef.onLoadData();\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(uni.getStorageSync('qbtz')){\r\n\t\t\tuni.removeStorageSync('qbtz')\r\n\t\t\tthis.bigType = 1;\r\n\t\t\tif (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onLoadData === 'function') {\r\n\t\t\t\tthis.$refs.coursePackageRef.onLoadData();\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tuni.hideTabBar()\r\n\t},\r\n\tonPageScroll(e) {\r\n\t\tif(this.bigType == 2){\r\n\t\t\tif (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onPageScrollData === 'function') {\r\n\t\t\t\tthis.$refs.pointsMallRef.onPageScrollData(e.scrollTop)\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonReachBottom() {\r\n\t\tconsole.log('到底了');\r\n\t\tif(this.bigType == 1){\r\n\t\t\tif (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onReachBottomData === 'function') {\r\n\t\t\t\tthis.$refs.coursePackageRef.onReachBottomData();\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(this.bigType == 2){\r\n\t\t\tif (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onReachBottomData === 'function') {\r\n\t\t\t\tthis.$refs.pointsMallRef.onReachBottomData();\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tbigTab(index){\r\n\t\t\tthis.bigType = index;\r\n\t\t\tif(this.bigType == 0){\r\n\t\t\t\tif (this.$refs.membershipCardRef && typeof this.$refs.membershipCardRef.onLoadData === 'function') {\r\n\t\t\t\t\tthis.$refs.membershipCardRef.onLoadData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(this.bigType == 1){\r\n\t\t\t\tif (this.$refs.coursePackageRef && typeof this.$refs.coursePackageRef.onLoadData === 'function') {\r\n\t\t\t\t\tthis.$refs.coursePackageRef.onLoadData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(this.bigType == 2){\r\n\t\t\t\tif (this.$refs.pointsMallRef && typeof this.$refs.pointsMallRef.onLoadData === 'function') {\r\n\t\t\t\t\tthis.$refs.pointsMallRef.onLoadData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.buy{overflow:hidden;}\r\n</style>", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751622779\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}