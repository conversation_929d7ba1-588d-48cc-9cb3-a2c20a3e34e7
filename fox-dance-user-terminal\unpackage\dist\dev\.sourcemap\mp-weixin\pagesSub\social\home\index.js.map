{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "uni-app:///pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/home/<USER>", "uni-app:///main.js"], "names": ["name", "components", "PostCard", "data", "postList", "loading", "refreshing", "page", "pageSize", "currentTopic", "currentTopicIndex", "topicList", "id", "hasMore", "loadError", "isInitialized", "onLoad", "onShow", "activated", "methods", "initializeData", "console", "loadHotTags", "result", "allOption", "loadPosts", "refresh", "params", "current", "size", "sortField", "sortOrder", "selectedTag", "posts", "userId", "title", "username", "userAvatar", "content", "coverImage", "images", "topics", "topicId", "likeCount", "commentCount", "isLiked", "createTime", "uni", "icon", "onRefresh", "loadMore", "retryLoad", "formatTime", "onPostLike", "post", "index", "goPostDetail", "url", "goUserProfile", "goSearch", "selectTopic", "dateString", "currentTopicName", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgFlwB;AAKA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YACA;QAAAX;QAAAY;MAAA,EACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;EACA;EAEA;EACAC;IACA;IACA;MACA;IACA;EACA;EAEA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAF;gBAEA,IACAE,UACAA,qBACAA,eACAA,wBACA;kBACA;kBACAC;kBACA,oBACAA,mDACAD;oBAAA;sBACAvB;sBACAY;oBACA;kBAAA,IACA;kBACAS;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAI;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACAL;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAIAA;gBACA;gBACA;gBAAA;gBAGAM;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACA;kBACAC,oCACA;oBAAA;kBAAA,EACA;kBACA;oBACAL;kBACA;gBACA;gBAEAN;gBAAA;gBAAA,OACA;cAAA;gBAAAE;gBACAF;gBAEA;kBACAY;oBAAA;sBACArB;sBACAsB;sBAAA;sBACAC;sBACAC;sBACAC,YACA,4IACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;kBACAzB;kBAEA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;;kBACAA;;kBAEA;kBACA;kBACAA;gBAEA;kBACA;kBACAA;kBACA;kBACA;oBACA0B;sBACAZ;sBACAa;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3B;gBACA;gBACA;kBACA0B;oBACAZ;oBACAa;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA5B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA6B;MACA7B;;MAEA;MACA;QACAA;QACA;MACA;MAEA;IACA;IAEA;IACA8B;MACA9B;MACA;MACA;IACA;IAEA+B;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAA;gBACAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;gBACA0B;kBACAZ;kBACAa;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAQ;MACAT;QACAU;MACA;IACA;IAEAC;MACA;QACAX;UACAZ;UACAa;QACA;QACA;MACA;MAEA3B,YACA,iBACAiC,aACA,QACAA,cACA;MACAP;QACAU;MACA;IACA;IAEAE;MACAZ;QACAU;MACA;IACA;IAEAG;MACA;MAEAvC;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IACA;EAAA,6EAGAwC;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;EACA,oFAEA;IAAA;MAAA;IACA,uBACA;MAAA;IAAA,yFACA;IACA,qCACA,+BACAC;EACA,oFAEA;IACA,qCACA,eACA;EACA,oFAGA;IACAzC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjbA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACA0C,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/home/<USER>", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"de45d8c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=de45d8c2&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loading && _vm.postList.length > 0\n  var g1 = !_vm.hasMore && _vm.postList.length > 0 && !_vm.loading\n  var g2 = _vm.loadError && _vm.postList.length > 0\n  var g3 = !_vm.postList.length && !_vm.loading\n  var m0 = g3 ? _vm.getEmptyText() : null\n  var m1 = g3 ? _vm.getEmptyDesc() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"home-container\" :style=\"{ height: '100vh', overflow: 'hidden' }\">\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"logo\">\n          <image src=\"https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\" class=\"logo-text\"></image>\n        </view>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#666\" @click=\"goSearch\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 话题标签栏 -->\n    <view class=\"topic-tabs-container\">\n      <u-tabs\n        :list=\"topicList\"\n        :current=\"currentTopicIndex\"\n        @change=\"selectTopic\"\n        :scrollable=\"true\"\n        activeColor=\"#2979ff\"\n        inactiveColor=\"#666\"\n        fontSize=\"28\"\n        lineColor=\"#2979ff\"\n        lineWidth=\"40\"\n        lineHeight=\"6\"\n        height=\"80\"\n        itemStyle=\"padding: 0 32rpx;\"\n      ></u-tabs>\n    </view>\n\n    <!-- 帖子网格列表 -->\n    <scroll-view\n      class=\"post-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"refreshing\"\n    >\n      <view class=\"post-grid\">\n        <PostCard\n          v-for=\"post in postList\"\n          :key=\"post.id\"\n          :post=\"post\"\n          class=\"post-card-item\"\n          @click=\"goPostDetail\"\n          @user-click=\"goUserProfile\"\n          @like=\"onPostLike\"\n        />\n      </view>\n\n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"loading && postList.length > 0\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n\n      <!-- 没有更多数据 -->\n      <view class=\"load-more\" v-if=\"!hasMore && postList.length > 0 && !loading\">\n        <text class=\"load-text no-more\">没有更多帖子了</text>\n      </view>\n\n      <!-- 加载失败重试 -->\n      <view class=\"load-more\" v-if=\"loadError && postList.length > 0\">\n        <text class=\"load-text error\" @click=\"retryLoad\">加载失败，点击重试</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!postList.length && !loading\" class=\"empty-state\">\n        <u-icon name=\"file-text\" color=\"#ccc\" size=\"120rpx\"></u-icon>\n        <text class=\"empty-text\">{{ getEmptyText() }}</text>\n        <text class=\"empty-desc\">{{ getEmptyDesc() }}</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from \"../components/PostCard.vue\";\nimport {\n  getPostList,\n  getHotTags,\n  likePost,\n  unlikePost\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialHome\",\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      postList: [],\n      loading: false,\n      refreshing: false,\n      page: 1,\n      pageSize: 10,\n      currentTopic: \"all\",\n      currentTopicIndex: 0,\n      topicList: [\n        { name: \"全部\", id: \"all\" }\n      ],\n      hasMore: true,\n      loadError: false, // 加载错误状态\n      isInitialized: false // 标记是否已初始化\n    };\n  },\n  onLoad() {\n    this.initializeData();\n  },\n\n  // 页面显示时重新加载数据\n  onShow() {\n    // 如果还没有初始化，或者数据为空，重新加载\n    if (!this.isInitialized || !this.postList || this.postList.length === 0) {\n      this.initializeData();\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    // 如果还没有初始化，或者数据为空，重新加载\n    if (!this.isInitialized || !this.postList || this.postList.length === 0) {\n      this.initializeData();\n    }\n  },\n  methods: {\n    // 初始化数据\n    async initializeData() {\n      console.log(\"初始化首页数据...\");\n      try {\n        await this.loadHotTags();\n        await this.loadPosts(true);\n        this.isInitialized = true;\n      } catch (error) {\n        console.error(\"初始化数据失败:\", error);\n      }\n    },\n\n    // 加载热门话题\n    async loadHotTags() {\n      try {\n        const result = await getHotTags(10);\n        console.log(\"热门标签API返回:\", result);\n\n        if (\n          result &&\n          result.code === 0 &&\n          result.data &&\n          result.data.length > 0\n        ) {\n          // 保留\"全部\"选项，添加热门话题\n          const allOption = this.topicList[0];\n          this.topicList = [\n            allOption,\n            ...result.data.map(tag => ({\n              name: tag.name,\n              id: tag.id\n            }))\n          ];\n          console.log(\"话题列表更新:\", this.topicList);\n        }\n      } catch (error) {\n        console.error(\"加载热门话题失败:\", error);\n        // 使用默认话题列表\n      }\n    },\n\n    async loadPosts(refresh = false) {\n      if (this.loading) {\n        console.log(\"正在加载中，跳过重复请求\");\n        return;\n      }\n\n      // 如果不是刷新且没有更多数据，直接返回\n      if (!refresh && !this.hasMore) {\n        console.log(\"没有更多数据，跳过加载\");\n        return;\n      }\n\n      console.log(\"开始加载帖子数据，refresh:\", refresh, \"page:\", refresh ? 1 : this.page);\n      this.loading = true;\n      this.loadError = false; // 重置错误状态\n\n      try {\n        const params = {\n          current: refresh ? 1 : this.page,\n          size: this.pageSize,\n          sortField: \"createTime\",\n          sortOrder: \"desc\"\n        };\n\n        // 如果选择了特定话题，添加标签筛选\n        if (this.currentTopic !== \"all\") {\n          // 根据tagId获取标签名称\n          const selectedTag = this.topicList.find(\n            topic => topic.id === this.currentTopic\n          );\n          if (selectedTag) {\n            params.tags = [selectedTag.name];\n          }\n        }\n\n        console.log(\"API请求参数:\", params);\n        const result = await getPostList(params);\n        console.log(\"API返回结果:\", result);\n\n        if (result.code == 0) {\n          const posts = result.data.records.map(post => ({\n            id: post.id,\n            userId: post.userId, // 添加用户ID字段\n            title: post.title || \"\",\n            username: post.nickname || \"无名氏\",\n            userAvatar:\n              \"https://file.foxdance.com.cn\" + post.avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85'||\n              \"/static/images/toux.png\",\n            content: post.content,\n            coverImage: post.coverImage,\n            images: post.images || [],\n            topics: post.tags || [],\n            topicId: this.currentTopic,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            createTime: new Date(post.createTime)\n          }));\n          console.log(\"posts:\", posts);\n\n          if (refresh) {\n            this.postList = posts;\n            this.page = 2; // 下次加载第2页\n          } else {\n            this.postList = [...this.postList, ...posts];\n            this.page++; // 页码递增\n          }\n          console.log(\"this.postList:\", this.postList, \"下次加载页码:\", this.page);\n\n          // 检查是否还有更多数据\n          this.hasMore = posts.length >= this.pageSize;\n          console.log(\"是否还有更多数据:\", this.hasMore, \"本次加载数量:\", posts.length);\n\n        } else {\n          // API调用失败\n          console.warn(\"获取帖子列表失败:\", result);\n          this.loadError = true;\n          if (!refresh) {\n            uni.showToast({\n              title: result.message || \"获取数据失败\",\n              icon: \"none\"\n            });\n          }\n        }\n      } catch (error) {\n        console.error(\"加载帖子失败:\", error);\n        this.loadError = true;\n        if (!refresh) {\n          uni.showToast({\n            title: \"网络错误，请重试\",\n            icon: \"none\"\n          });\n        }\n      } finally {\n        this.loading = false;\n        this.refreshing = false;\n      }\n    },\n\n    onRefresh() {\n      console.log(\"刷新首页数据...\");\n      this.refreshing = true;\n      this.page = 1;\n      this.postList = [];\n      this.hasMore = true;\n      this.loadError = false;\n      // 重新加载热门话题和帖子\n      this.loadHotTags();\n      this.loadPosts(true);\n    },\n\n    loadMore() {\n      console.log(\"触发加载更多，当前状态 - loading:\", this.loading, \"hasMore:\", this.hasMore, \"page:\", this.page);\n\n      // 防止重复加载和无数据时加载\n      if (this.loading || !this.hasMore || this.loadError) {\n        console.log(\"跳过加载更多 - loading:\", this.loading, \"hasMore:\", this.hasMore, \"loadError:\", this.loadError);\n        return;\n      }\n\n      this.loadPosts(false);\n    },\n\n    // 重试加载\n    retryLoad() {\n      console.log(\"重试加载\");\n      this.loadError = false;\n      this.loadPosts(false);\n    },\n\n    formatTime(time) {\n      const now = new Date();\n      const diff = now - new Date(time);\n      const minutes = Math.floor(diff / 60000);\n      const hours = Math.floor(diff / 3600000);\n      const days = Math.floor(diff / 86400000);\n\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      return `${days}天前`;\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id);\n          post.isLiked = false;\n          post.likeCount = Math.max(0, post.likeCount - 1);\n        } else {\n          // 点赞\n          await likePost(post.id);\n          post.isLiked = true;\n          post.likeCount += 1;\n        }\n\n        // 更新帖子列表中的数据\n        const index = this.postList.findIndex(p => p.id === post.id);\n        if (index !== -1) {\n          this.$set(this.postList, index, { ...post });\n        }\n      } catch (error) {\n        console.error(\"点赞操作失败:\", error);\n        uni.showToast({\n          title: \"操作失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goUserProfile(post) {\n      if (!post.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\n        \"跳转到用户主页，用户ID:\",\n        post.userId,\n        \"用户名:\",\n        post.username\n      );\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId}`\n      });\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/search/index\"\n      });\n    },\n\n    selectTopic(index) {\n      if (this.currentTopicIndex === index) return;\n\n      console.log(\"切换话题:\", this.topicList[index].name);\n      this.currentTopicIndex = index;\n      this.currentTopic = this.topicList[index].id;\n      this.page = 1;\n      this.postList = [];\n      this.hasMore = true;\n      this.loadError = false;\n\n      // 重新加载帖子\n      this.loadPosts(true);\n    },\n\n    // 格式化时间\n    formatTime(dateString) {\n      if (!dateString) return \"\";\n\n      const date = new Date(dateString);\n      const now = new Date();\n      const diff = now - date;\n\n      const minutes = Math.floor(diff / (1000 * 60));\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n\n      if (minutes < 1) return \"刚刚\";\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      if (days < 7) return `${days}天前`;\n\n      return date.toLocaleDateString();\n    },\n\n    getEmptyText() {\n      const currentTopicName =\n        this.topicList.find(topic => topic.id === this.currentTopic)?.name ||\n        \"全部\";\n      return this.currentTopic === \"all\"\n        ? \"暂无帖子\"\n        : `暂无${currentTopicName}相关帖子`;\n    },\n\n    getEmptyDesc() {\n      return this.currentTopic === \"all\"\n        ? \"快来发布第一条帖子吧\"\n        : \"换个话题看看其他内容吧\";\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新首页数据...\");\n      this.isInitialized = false;\n      this.postList = [];\n      this.page = 1;\n      this.hasMore = true;\n      this.loadError = false;\n      this.initializeData();\n    }\n  }\n};\n</script>\n\n\n\n<style lang=\"scss\" scoped>\n.home-container {\n  height: 100vh;\n  background: #f8f9fa;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n\n.header {\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n  flex-shrink: 0;\n}\n\n.header-content {\n  height: 88rpx;\n\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.logo-text {\n  width: 1328 * 0.11rpx;\n  height: 625 * 0.11rpx;\n  margin-left: -10rpx;\n}\n\n.topic-tabs-container {\n  background: #fff;\n  border-bottom: 2rpx solid #f0f0f0;\n  flex-shrink: 0;\n}\n\n/* uview tabs组件样式优化 */\n.topic-tabs-container ::v-deep .u-tabs {\n  background: #fff;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item {\n  padding: 0 32rpx !important;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item__text {\n  font-size: 28rpx !important;\n  font-weight: 500;\n}\n\n.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__line {\n  border-radius: 6rpx;\n}\n\n.post-list {\n  flex: 1;\n  height: 0; /* 重要：让flex子元素正确计算高度 */\n  overflow: hidden;\n}\n\n/* scroll-view内部内容的样式 */\n.post-list ::v-deep .uni-scroll-view {\n  height: 100% !important;\n  overflow-x: hidden !important;\n}\n\n.post-list ::v-deep .uni-scroll-view-content {\n  min-height: 100%;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 14rpx;\n  padding: 26rpx;\n  padding-bottom: 100rpx; /* 底部留出更多空间 */\n}\n\n.post-card-item {\n  width: calc(50% - 8rpx);\n  margin-bottom: 16rpx;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 160rpx 40rpx;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  margin: 32rpx 0 16rpx;\n}\n\n.empty-desc {\n  font-size: 28rpx;\n  color: #ccc;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-text {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n\n  &.no-more {\n    color: #ccc;\n    font-size: 26rpx;\n    margin-left: 0;\n  }\n\n  &.error {\n    color: #ff6b6b;\n    margin-left: 0;\n    padding: 16rpx 32rpx;\n    border: 2rpx solid #ff6b6b;\n    border-radius: 32rpx;\n    background: rgba(255, 107, 107, 0.1);\n\n    &:active {\n      background: rgba(255, 107, 107, 0.2);\n    }\n  }\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 375px) {\n  .post-list {\n    padding: 12rpx;\n  }\n\n  .post-grid {\n    gap: 12rpx;\n  }\n\n  .post-card-item {\n    width: calc(50% - 6rpx);\n  }\n}\n\n@media screen and (min-width: 768px) {\n  .post-grid {\n    gap: 24rpx;\n  }\n\n  .post-card-item {\n    width: calc(33.33% - 16rpx);\n  }\n}\n\n@media screen and (min-width: 1024px) {\n  .post-list {\n    padding: 32rpx 64rpx;\n  }\n\n  .post-card-item {\n    width: calc(25% - 18rpx);\n  }\n}\n\n// 修改u-tab-itme默认样式\n/deep/ .u-tab-item {\n  padding: 0 25rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=de45d8c2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760719341\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/home/<USER>'\ncreatePage(Page)"], "sourceRoot": ""}