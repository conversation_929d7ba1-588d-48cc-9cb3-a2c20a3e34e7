{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/websocket-test.vue?93a9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/websocket-test.vue?7a11", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/websocket-test.vue?f32e", "uni-app:///pagesSub/social/chat/websocket-test.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/websocket-test.vue?6635", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/websocket-test.vue?db45"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "socketTask", "isConnected", "isConnecting", "logs", "envInfo", "platform", "env", "wsUrl", "computed", "connectionStatus", "statusClass", "onLoad", "onUnload", "methods", "initEnvInfo", "process", "systemInfo", "testConnection", "url", "success", "fail", "disconnect", "sendTestMessage", "type", "content", "timestamp", "addLog", "getHours", "toString", "padStart", "getMinutes", "getSeconds", "time", "message"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuvB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgD3wB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;QACAT;QACAC;MACA;;MAEA;MACA,IACAS,IACAC,EACA;QACA;MACA,SAEA;MAEA,gDACA,wDACA,OACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA;MACA;MAEA;QACAC;QACAC;UACA;QACA;QACAC;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;QACA;QACA;MACA;MAEA;QACAC;QACAxB;UACAyB;UACAC;QACA;MACA;MAEA;QACA1B;QACAoB;UACA;QACA;QACAC;UACA;QACA;MACA;IACA;IAEAM;MAAA;MACA;MACA,yBACAC,WACAC,WACAC,kCACAC,aACAF,WACAC,kCACAE,aACAH,WACAC;MAEA;QACAG;QACAC;QACAV;MACA;;MAEA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/NA;AAAA;AAAA;AAAA;AAA06C,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACA97C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/chat/websocket-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/chat/websocket-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./websocket-test.vue?vue&type=template&id=19a3a4c2&scoped=true&\"\nvar renderjs\nimport script from \"./websocket-test.vue?vue&type=script&lang=js&\"\nexport * from \"./websocket-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./websocket-test.vue?vue&type=style&index=0&id=19a3a4c2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"19a3a4c2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/chat/websocket-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./websocket-test.vue?vue&type=template&id=19a3a4c2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./websocket-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./websocket-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">WebSocket连接测试</text>\n    </view>\n\n    <view class=\"test-section\">\n      <view class=\"info-item\">\n        <text class=\"label\">当前环境：</text>\n        <text class=\"value\">{{ envInfo.platform }} - {{ envInfo.env }}</text>\n      </view>\n\n      <view class=\"info-item\">\n        <text class=\"label\">WebSocket URL：</text>\n        <text class=\"value\">{{ wsUrl }}</text>\n      </view>\n\n      <view class=\"info-item\">\n        <text class=\"label\">连接状态：</text>\n        <text class=\"value\" :class=\"statusClass\">{{ connectionStatus }}</text>\n      </view>\n    </view>\n\n    <view class=\"button-section\">\n      <button\n        @click=\"testConnection\"\n        :disabled=\"isConnecting\"\n        class=\"test-btn\"\n      >{{ isConnecting ? '连接中...' : '测试连接' }}</button>\n\n      <button @click=\"disconnect\" :disabled=\"!isConnected\" class=\"disconnect-btn\">断开连接</button>\n\n      <button @click=\"sendTestMessage\" :disabled=\"!isConnected\" class=\"send-btn\">发送测试消息</button>\n    </view>\n\n    <view class=\"log-section\">\n      <text class=\"log-title\">连接日志：</text>\n      <scroll-view class=\"log-content\" scroll-y>\n        <view v-for=\"(log, index) in logs\" :key=\"index\" class=\"log-item\">\n          <text class=\"log-time\">{{ log.time }}</text>\n          <text class=\"log-message\" :class=\"log.type\">{{ log.message }}</text>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      socketTask: null,\n      isConnected: false,\n      isConnecting: false,\n      logs: [],\n      envInfo: {\n        platform: \"\",\n        env: \"\"\n      },\n      wsUrl: \"\"\n    };\n  },\n\n  computed: {\n    connectionStatus() {\n      if (this.isConnecting) return \"连接中...\";\n      if (this.isConnected) return \"已连接\";\n      return \"未连接\";\n    },\n\n    statusClass() {\n      if (this.isConnecting) return \"status-connecting\";\n      if (this.isConnected) return \"status-connected\";\n      return \"status-disconnected\";\n    }\n  },\n\n  onLoad() {\n    this.initEnvInfo();\n    this.addLog(\"页面加载完成\", \"info\");\n  },\n\n  onUnload() {\n    this.disconnect();\n  },\n\n  methods: {\n    initEnvInfo() {\n      const systemInfo = uni.getSystemInfoSync();\n      this.envInfo = {\n        platform: systemInfo.platform,\n        env: process.env.NODE_ENV\n      };\n\n      // 设置WebSocket URL\n      if (\n        process.env.NODE_ENV === \"development\" ||\n        systemInfo.platform === \"devtools\"\n      ) {\n        this.wsUrl = \"ws://localhost:8101/api/ws/chat\";\n      } else {\n        this.wsUrl = \"wss://admin.foxdance.com.cn/ws/chat\";\n      }\n\n      this.addLog(\n        `环境信息: ${this.envInfo.platform} - ${this.envInfo.env}`,\n        \"info\"\n      );\n      this.addLog(`WebSocket URL: ${this.wsUrl}`, \"info\");\n    },\n\n    testConnection() {\n      if (this.isConnecting || this.isConnected) {\n        return;\n      }\n\n      this.isConnecting = true;\n      this.addLog(\"开始测试WebSocket连接...\", \"info\");\n\n      // 获取测试用户信息\n      const userId = uni.getStorageSync(\"userid\") || \"test_user\";\n      const token = uni.getStorageSync(\"token\") || \"test_token\";\n\n      const fullUrl = `${this.wsUrl}?userId=${userId}&token=${token}`;\n      this.addLog(`连接URL: ${fullUrl}`, \"info\");\n\n      this.socketTask = uni.connectSocket({\n        url: fullUrl,\n        success: () => {\n          this.addLog(\"WebSocket连接请求发送成功\", \"success\");\n        },\n        fail: error => {\n          this.addLog(`WebSocket连接失败: ${JSON.stringify(error)}`, \"error\");\n          this.isConnecting = false;\n        }\n      });\n\n      // 监听连接打开\n      this.socketTask.onOpen(() => {\n        this.isConnected = true;\n        this.isConnecting = false;\n        this.addLog(\"WebSocket连接已建立！\", \"success\");\n      });\n\n      // 监听消息\n      this.socketTask.onMessage(res => {\n        this.addLog(`收到消息: ${res.data}`, \"success\");\n      });\n\n      // 监听连接关闭\n      this.socketTask.onClose(() => {\n        this.isConnected = false;\n        this.isConnecting = false;\n        this.addLog(\"WebSocket连接已关闭\", \"warning\");\n      });\n\n      // 监听错误\n      this.socketTask.onError(error => {\n        this.addLog(`WebSocket错误: ${JSON.stringify(error)}`, \"error\");\n        this.isConnecting = false;\n      });\n    },\n\n    disconnect() {\n      if (this.socketTask) {\n        this.socketTask.close();\n        this.socketTask = null;\n      }\n      this.isConnected = false;\n      this.isConnecting = false;\n      this.addLog(\"主动断开连接\", \"info\");\n    },\n\n    sendTestMessage() {\n      if (!this.isConnected || !this.socketTask) {\n        this.addLog(\"未连接，无法发送消息\", \"error\");\n        return;\n      }\n\n      const testMessage = {\n        type: \"message\",\n        data: {\n          content: `测试消息 - ${new Date().toLocaleTimeString()}`,\n          timestamp: Date.now()\n        }\n      };\n\n      this.socketTask.send({\n        data: JSON.stringify(testMessage),\n        success: () => {\n          this.addLog(\"测试消息发送成功\", \"success\");\n        },\n        fail: error => {\n          this.addLog(`消息发送失败: ${JSON.stringify(error)}`, \"error\");\n        }\n      });\n    },\n\n    addLog(message, type = \"info\") {\n      const now = new Date();\n      const time = `${now\n        .getHours()\n        .toString()\n        .padStart(2, \"0\")}:${now\n        .getMinutes()\n        .toString()\n        .padStart(2, \"0\")}:${now\n        .getSeconds()\n        .toString()\n        .padStart(2, \"0\")}`;\n\n      this.logs.unshift({\n        time,\n        message,\n        type\n      });\n\n      // 限制日志数量\n      if (this.logs.length > 50) {\n        this.logs = this.logs.slice(0, 50);\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  padding: 20rpx;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30rpx;\n}\n\n.title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.test-section {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.info-item {\n  display: flex;\n  margin-bottom: 20rpx;\n  align-items: center;\n}\n\n.label {\n  font-size: 28rpx;\n  color: #666;\n  width: 200rpx;\n}\n\n.value {\n  font-size: 28rpx;\n  color: #333;\n  flex: 1;\n  word-break: break-all;\n}\n\n.status-connected {\n  color: #4caf50;\n}\n\n.status-connecting {\n  color: #ff9800;\n}\n\n.status-disconnected {\n  color: #f44336;\n}\n\n.button-section {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n  margin-bottom: 30rpx;\n}\n\n.test-btn,\n.disconnect-btn,\n.send-btn {\n  height: 80rpx;\n  border-radius: 12rpx;\n  font-size: 32rpx;\n  border: none;\n}\n\n.test-btn {\n  background: #2979ff;\n  color: white;\n}\n\n.disconnect-btn {\n  background: #f44336;\n  color: white;\n}\n\n.send-btn {\n  background: #4caf50;\n  color: white;\n}\n\n.log-section {\n  background: white;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  height: 600rpx;\n}\n\n.log-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.log-content {\n  height: 500rpx;\n}\n\n.log-item {\n  display: flex;\n  margin-bottom: 15rpx;\n  padding: 10rpx;\n  background: #f9f9f9;\n  border-radius: 8rpx;\n}\n\n.log-time {\n  font-size: 24rpx;\n  color: #999;\n  width: 120rpx;\n  flex-shrink: 0;\n}\n\n.log-message {\n  font-size: 26rpx;\n  flex: 1;\n}\n\n.log-message.success {\n  color: #4caf50;\n}\n\n.log-message.error {\n  color: #f44336;\n}\n\n.log-message.warning {\n  color: #ff9800;\n}\n\n.log-message.info {\n  color: #333;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./websocket-test.vue?vue&type=style&index=0&id=19a3a4c2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./websocket-test.vue?vue&type=style&index=0&id=19a3a4c2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753841926285\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}