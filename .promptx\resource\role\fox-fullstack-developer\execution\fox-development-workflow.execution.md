<execution>
  <constraint>
    ## FOX项目技术约束
    - **微信小程序兼容**：所有开发必须兼容微信小程序环境
    - **框架限制**：前端必须使用uni-app + uView，后端必须使用SpringBoot
    - **页面路径约束**：新页面必须放在fox-dance-user-terminal/pagesSub中
    - **样式单位约束**：必须使用rpx单位，禁止使用px
    - **实体类约束**：操作用户实体类是BaUser，不是User
    - **组件化要求**：能组件化就组件化，优先使用uView组件
  </constraint>

  <rule>
    ## 强制性开发规则
    - **uView组件优先**：优先使用uView组件库，参考https://uviewui.com/components
    - **组件化开发**：所有可复用功能必须组件化
    - **接口规范统一**：前后端接口必须遵循RESTful规范
    - **错误处理完备**：前后端都必须有完善的错误处理机制
    - **代码规范遵循**：严格遵循项目代码规范和命名约定
    - **话题映射规则**：前端"话题"概念对应后端tags字段
  </rule>

  <guideline>
    ## 开发指导原则
    - **用户体验优先**：始终以用户体验为核心进行开发
    - **性能优化重视**：重视小程序性能，优化加载速度
    - **代码复用最大化**：通过组件化提高代码复用率
    - **接口设计先行**：前后端开发前先确定接口规范
    - **渐进式开发**：先实现核心功能，再逐步完善细节
    - **测试驱动开发**：重要功能必须有对应的测试用例
  </guideline>

  <process>
    ## FOX全栈开发标准流程
    
    ### Step 1: 需求分析与技术方案设计 (20分钟)
    
    ```mermaid
    flowchart TD
        A[需求理解] --> B[功能拆解]
        B --> C[技术方案设计]
        C --> D[接口协议设计]
        D --> E[数据库设计]
        E --> F[前端页面规划]
    ```
    
    **执行要点**：
    - 深入理解业务需求和用户场景
    - 将复杂需求拆解为可实现的功能模块
    - 设计合理的技术实现方案
    - 定义清晰的前后端接口协议
    - 设计合理的数据库表结构
    - 规划前端页面结构和组件使用
    
    ### Step 2: 后端开发实施 (40分钟)
    
    ```mermaid
    graph LR
        A[数据库表设计] --> B[实体类创建]
        B --> C[Repository层]
        C --> D[Service层开发]
        D --> E[Controller层]
        E --> F[接口测试]
    ```
    
    **SpringBoot开发要点**：
    - 创建合理的数据库表结构和索引
    - 定义对应的Entity实体类（注意使用BaUser）
    - 实现Repository层的数据访问逻辑
    - 编写Service层的业务逻辑处理
    - 开发Controller层的API接口
    - 使用Postman或Swagger进行接口测试
    
    ### Step 3: 前端开发实施 (40分钟)
    
    ```mermaid
    flowchart TD
        A[页面结构设计] --> B[uView组件选择]
        B --> C[页面布局实现]
        C --> D[数据交互开发]
        D --> E[样式美化]
        E --> F[功能测试]
    ```
    
    **uni-app开发要点**：
    - 在pagesSub目录下创建新页面
    - 优先选择合适的uView组件
    - 使用rpx单位进行样式开发
    - 实现与后端API的数据交互
    - 进行样式美化和用户体验优化
    - 在微信开发者工具中测试功能
    
    ### Step 4: 前后端联调与优化 (20分钟)
    
    ```mermaid
    graph TD
        A[接口联调] --> B[数据格式验证]
        B --> C[错误处理测试]
        C --> D[性能优化]
        D --> E[用户体验优化]
        E --> F[代码审查]
    ```
    
    **联调要点**：
    - 验证前后端接口的数据交互
    - 测试各种异常情况的处理
    - 优化接口响应速度和前端渲染性能
    - 完善用户交互体验和错误提示
    - 进行代码质量检查和优化
  </process>

  <criteria>
    ## 开发质量评价标准
    
    ### 功能完整性
    - ✅ 所有需求功能正确实现
    - ✅ 前后端接口对接成功
    - ✅ 数据交互正常无误
    - ✅ 异常情况处理完善
    
    ### 代码质量
    - ✅ 代码结构清晰合理
    - ✅ 命名规范易于理解
    - ✅ 注释文档完善
    - ✅ 无明显性能问题
    
    ### 用户体验
    - ✅ 界面美观易用
    - ✅ 交互流畅自然
    - ✅ 加载速度快
    - ✅ 错误提示友好
    
    ### 技术规范
    - ✅ 遵循项目技术约束
    - ✅ 使用uView组件库
    - ✅ 页面路径正确
    - ✅ 样式单位规范
    - ✅ 组件化程度高
    
    ### 兼容性
    - ✅ 微信小程序兼容
    - ✅ 不同设备适配
    - ✅ 网络异常处理
    - ✅ 数据格式兼容
  </criteria>
</execution>
