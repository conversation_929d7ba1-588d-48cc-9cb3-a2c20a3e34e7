<template>
  <u-button
    :type="isFollowed ? 'default' : 'primary'"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :customStyle="buttonStyle"
    @click="handleFollow"
  >
    {{ isFollowed ? '已关注' : '关注' }}
  </u-button>
</template>

<script>
import { followTag, unfollowTag } from '@/utils/socialApi.js'

export default {
  name: 'TagFollowButton',
  props: {
    // 话题信息
    tag: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 关注状态
    followed: {
      type: Boolean,
      default: false
    },
    // 按钮尺寸
    size: {
      type: String,
      default: 'mini',
      validator: (value) => ['large', 'normal', 'mini'].includes(value)
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      default: true
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isFollowed: false,
      loading: false
    }
  },
  computed: {
    buttonStyle() {
      const baseStyle = {
        fontSize: this.getFontSize(),
        height: this.getHeight(),
        minWidth: this.getMinWidth(),
        borderRadius: this.getBorderRadius()
      }
      
      return {
        ...baseStyle,
        ...this.customStyle
      }
    }
  },
  watch: {
    followed: {
      immediate: true,
      handler(newVal) {
        this.isFollowed = newVal
      }
    }
  },
  methods: {
    getFontSize() {
      const sizeMap = {
        large: '32rpx',
        normal: '28rpx',
        mini: '24rpx'
      }
      return sizeMap[this.size] || sizeMap.mini
    },
    
    getHeight() {
      const heightMap = {
        large: '88rpx',
        normal: '72rpx',
        mini: '56rpx'
      }
      return heightMap[this.size] || heightMap.mini
    },
    
    getMinWidth() {
      const widthMap = {
        large: '160rpx',
        normal: '140rpx',
        mini: '120rpx'
      }
      return widthMap[this.size] || widthMap.mini
    },
    
    getBorderRadius() {
      return '28rpx'
    },

    async handleFollow() {
      if (this.loading || this.disabled) return

      const tagId = this.tag.id || this.tag.tagId
      if (!tagId) {
        console.error('TagFollowButton: 缺少话题ID')
        uni.showToast({
          title: '话题信息错误',
          icon: 'error'
        })
        return
      }

      try {
        if (this.showLoading) {
          this.loading = true
        }

        let result
        if (this.isFollowed) {
          // 取消关注
          result = await unfollowTag(tagId)
          if (result && result.code === 0) {
            this.isFollowed = false
            uni.showToast({
              title: '已取消关注',
              icon: 'success'
            })
            // 触发事件通知父组件
            this.$emit('unfollow', {
              tag: this.tag,
              isFollowed: false
            })
          } else {
            throw new Error(result?.message || '取消关注失败')
          }
        } else {
          // 关注
          result = await followTag(tagId)
          if (result && result.code === 0) {
            this.isFollowed = true
            uni.showToast({
              title: '关注成功',
              icon: 'success'
            })
            // 触发事件通知父组件
            this.$emit('follow', {
              tag: this.tag,
              isFollowed: true
            })
          } else {
            throw new Error(result?.message || '关注失败')
          }
        }

        // 触发状态变化事件
        this.$emit('follow-change', this.isFollowed)

      } catch (error) {
        console.error('话题关注操作失败:', error)

        uni.showToast({
          title: error.message || '操作失败',
          icon: 'error'
        })

        // 触发错误事件
        this.$emit('error', {
          tag: this.tag,
          error: error
        })
      } finally {
        if (this.showLoading) {
          setTimeout(() => {
            this.loading = false
          }, 300)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 组件样式继承uView的u-button样式，无需额外样式
</style>
