{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagFollowButton.vue?9c51", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagFollowButton.vue?4edd", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagFollowButton.vue?554d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagFollowButton.vue?95ab", "uni-app:///pagesSub/social/components/TagFollowButton.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagFollowButton.vue?7ee7", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/TagFollowButton.vue?5e47"], "names": ["name", "props", "tag", "type", "required", "default", "followed", "size", "validator", "disabled", "showLoading", "customStyle", "data", "isFollowed", "loading", "computed", "buttonStyle", "fontSize", "height", "min<PERSON><PERSON><PERSON>", "borderRadius", "baseStyle", "watch", "immediate", "handler", "methods", "getFontSize", "large", "normal", "mini", "getHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getBorderRadius", "handleFollow", "tagId", "console", "uni", "title", "icon", "result", "error", "setTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACc5wB;AAAA;AAAA;AAAA,gBAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACAH;MACAE;IACA;IACA;IACAE;MACAJ;MACAE;MACAG;QAAA;MAAA;IACA;IACA;IACAC;MACAN;MACAE;IACA;IACA;IACAK;MACAP;MACAE;IACA;IACA;IACAM;MACAR;MACAE;QAAA;MAAA;IACA;EACA;EACAO;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MAEA,uCACAC,YACA;IAEA;EACA;EACAC;IACAhB;MACAiB;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;QACAH;QACAC;QACAC;MACA;MACA;IACA;IAEAE;MACA;QACAJ;QACAC;QACAC;MACA;MACA;IACA;IAEAG;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;kBACA;gBACA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAH;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACApC;kBACAW;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA0B;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAH;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACApC;kBACAW;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAsB;gBAEAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACApC;kBACAsC;gBACA;cAAA;gBAAA;gBAEA;kBACAC;oBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAA26C,CAAgB,gxCAAG,EAAC,C;;;;;;;;;;;ACA/7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/components/TagFollowButton.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./TagFollowButton.vue?vue&type=template&id=cd90723e&scoped=true&\"\nvar renderjs\nimport script from \"./TagFollowButton.vue?vue&type=script&lang=js&\"\nexport * from \"./TagFollowButton.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TagFollowButton.vue?vue&type=style&index=0&id=cd90723e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cd90723e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/components/TagFollowButton.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagFollowButton.vue?vue&type=template&id=cd90723e&scoped=true&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagFollowButton.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagFollowButton.vue?vue&type=script&lang=js&\"", "<template>\n  <u-button\n    :type=\"isFollowed ? 'default' : 'primary'\"\n    :size=\"size\"\n    :loading=\"loading\"\n    :disabled=\"disabled\"\n    :customStyle=\"buttonStyle\"\n    @click=\"handleFollow\"\n  >\n    {{ isFollowed ? '已关注' : '关注' }}\n  </u-button>\n</template>\n\n<script>\nimport { followTag, unfollowTag } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'TagFollowButton',\n  props: {\n    // 话题信息\n    tag: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 关注状态\n    followed: {\n      type: Boolean,\n      default: false\n    },\n    // 按钮尺寸\n    size: {\n      type: String,\n      default: 'mini',\n      validator: (value) => ['large', 'normal', 'mini'].includes(value)\n    },\n    // 是否禁用\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    // 是否显示加载状态\n    showLoading: {\n      type: Boolean,\n      default: true\n    },\n    // 自定义样式\n    customStyle: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      isFollowed: false,\n      loading: false\n    }\n  },\n  computed: {\n    buttonStyle() {\n      const baseStyle = {\n        fontSize: this.getFontSize(),\n        height: this.getHeight(),\n        minWidth: this.getMinWidth(),\n        borderRadius: this.getBorderRadius()\n      }\n      \n      return {\n        ...baseStyle,\n        ...this.customStyle\n      }\n    }\n  },\n  watch: {\n    followed: {\n      immediate: true,\n      handler(newVal) {\n        this.isFollowed = newVal\n      }\n    }\n  },\n  methods: {\n    getFontSize() {\n      const sizeMap = {\n        large: '32rpx',\n        normal: '28rpx',\n        mini: '24rpx'\n      }\n      return sizeMap[this.size] || sizeMap.mini\n    },\n    \n    getHeight() {\n      const heightMap = {\n        large: '88rpx',\n        normal: '72rpx',\n        mini: '56rpx'\n      }\n      return heightMap[this.size] || heightMap.mini\n    },\n    \n    getMinWidth() {\n      const widthMap = {\n        large: '160rpx',\n        normal: '140rpx',\n        mini: '120rpx'\n      }\n      return widthMap[this.size] || widthMap.mini\n    },\n    \n    getBorderRadius() {\n      return '28rpx'\n    },\n\n    async handleFollow() {\n      if (this.loading || this.disabled) return\n\n      const tagId = this.tag.id || this.tag.tagId\n      if (!tagId) {\n        console.error('TagFollowButton: 缺少话题ID')\n        uni.showToast({\n          title: '话题信息错误',\n          icon: 'error'\n        })\n        return\n      }\n\n      try {\n        if (this.showLoading) {\n          this.loading = true\n        }\n\n        let result\n        if (this.isFollowed) {\n          // 取消关注\n          result = await unfollowTag(tagId)\n          if (result && result.code === 0) {\n            this.isFollowed = false\n            uni.showToast({\n              title: '已取消关注',\n              icon: 'success'\n            })\n            // 触发事件通知父组件\n            this.$emit('unfollow', {\n              tag: this.tag,\n              isFollowed: false\n            })\n          } else {\n            throw new Error(result?.message || '取消关注失败')\n          }\n        } else {\n          // 关注\n          result = await followTag(tagId)\n          if (result && result.code === 0) {\n            this.isFollowed = true\n            uni.showToast({\n              title: '关注成功',\n              icon: 'success'\n            })\n            // 触发事件通知父组件\n            this.$emit('follow', {\n              tag: this.tag,\n              isFollowed: true\n            })\n          } else {\n            throw new Error(result?.message || '关注失败')\n          }\n        }\n\n        // 触发状态变化事件\n        this.$emit('follow-change', this.isFollowed)\n\n      } catch (error) {\n        console.error('话题关注操作失败:', error)\n\n        uni.showToast({\n          title: error.message || '操作失败',\n          icon: 'error'\n        })\n\n        // 触发错误事件\n        this.$emit('error', {\n          tag: this.tag,\n          error: error\n        })\n      } finally {\n        if (this.showLoading) {\n          setTimeout(() => {\n            this.loading = false\n          }, 300)\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 组件样式继承uView的u-button样式，无需额外样式\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagFollowButton.vue?vue&type=style&index=0&id=cd90723e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TagFollowButton.vue?vue&type=style&index=0&id=cd90723e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751621814\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}