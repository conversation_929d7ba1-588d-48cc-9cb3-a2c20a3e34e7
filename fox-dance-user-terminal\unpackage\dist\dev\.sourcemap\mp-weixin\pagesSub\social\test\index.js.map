{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/test/index.vue?9799", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/test/index.vue?42f8", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/test/index.vue?606e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/test/index.vue?987f", "uni-app:///pagesSub/social/test/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/test/index.vue?2c35", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/test/index.vue?9b76"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "methods", "goPage", "uni", "url", "success", "console", "fail", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiHlwB;EACAC;EACAC;IACAC;MACAC;QACAC;QACAC;UACAC;QACA;QACAC;UACAD;UACAH;YACAK;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpIA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/test/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/test/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8496c0dc&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=8496c0dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8496c0dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/test/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=8496c0dc&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"test-container\">\n    <view class=\"header\">\n      <text class=\"title\">组件测试页面</text>\n    </view>\n    \n    <scroll-view class=\"content\" scroll-y>\n      <!-- 测试基础组件 -->\n      <view class=\"test-section\">\n        <text class=\"section-title\">基础组件测试</text>\n        \n        <view class=\"test-item\">\n          <text class=\"test-label\">头像组件:</text>\n          <u-avatar src=\"https://picsum.photos/100/100?random=1\" size=\"40\"></u-avatar>\n        </view>\n        \n        <view class=\"test-item\">\n          <text class=\"test-label\">图标组件:</text>\n          <u-icon name=\"heart\" color=\"#ff4757\" size=\"24\"></u-icon>\n          <u-icon name=\"chat\" color=\"#2979ff\" size=\"24\"></u-icon>\n          <u-icon name=\"share\" color=\"#52c41a\" size=\"24\"></u-icon>\n        </view>\n        \n        <view class=\"test-item\">\n          <text class=\"test-label\">按钮组件:</text>\n          <u-button type=\"primary\" text=\"主要按钮\" size=\"small\"></u-button>\n          <u-button type=\"default\" text=\"默认按钮\" size=\"small\"></u-button>\n        </view>\n        \n        <view class=\"test-item\">\n          <text class=\"test-label\">加载组件:</text>\n          <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        </view>\n      </view>\n      \n      <!-- 测试导航功能 -->\n      <view class=\"test-section\">\n        <text class=\"section-title\">页面导航测试</text>\n        \n        <view class=\"nav-buttons\">\n          <u-button \n            type=\"primary\" \n            text=\"首页\" \n            @click=\"goPage('/pagesSub/social/home/<USER>')\"\n          ></u-button>\n          \n          <u-button \n            type=\"success\" \n            text=\"发现\" \n            @click=\"goPage('/pagesSub/social/discover/index')\"\n          ></u-button>\n          \n          <u-button \n            type=\"warning\" \n            text=\"发布\" \n            @click=\"goPage('/pagesSub/social/publish/index')\"\n          ></u-button>\n          \n          <u-button \n            type=\"error\" \n            text=\"消息\" \n            @click=\"goPage('/pagesSub/social/message/index')\"\n          ></u-button>\n          \n          <u-button \n            type=\"info\" \n            text=\"我的\" \n            @click=\"goPage('/pagesSub/social/profile/index')\"\n          ></u-button>\n        </view>\n      </view>\n      \n      <!-- 测试状态 -->\n      <view class=\"test-section\">\n        <text class=\"section-title\">测试状态</text>\n        \n        <view class=\"status-list\">\n          <view class=\"status-item success\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"status-text\">uni.promisify.adaptor.js 已修复</text>\n          </view>\n          \n          <view class=\"status-item success\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"status-text\">u-loading 组件已替换</text>\n          </view>\n          \n          <view class=\"status-item success\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"status-text\">页面路由配置完成</text>\n          </view>\n          \n          <view class=\"status-item success\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"status-text\">uview-ui 组件正常</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 返回演示页面 -->\n      <view class=\"test-section\">\n        <u-button \n          type=\"primary\" \n          size=\"large\"\n          text=\"返回演示页面\" \n          @click=\"goPage('/pagesSub/social/demo/index')\"\n        ></u-button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'SocialTest',\n  methods: {\n    goPage(url) {\n      uni.navigateTo({\n        url: url,\n        success: () => {\n          console.log('页面跳转成功:', url)\n        },\n        fail: (err) => {\n          console.error('页面跳转失败:', err)\n          uni.showToast({\n            title: '页面跳转失败',\n            icon: 'none'\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.header {\n  background: #2979ff;\n  padding: var(--status-bar-height) 16px 16px;\n  text-align: center;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #fff;\n}\n\n.content {\n  padding: 20px 16px;\n}\n\n.test-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16px;\n  display: block;\n}\n\n.test-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n  gap: 12px;\n}\n\n.test-label {\n  font-size: 14px;\n  color: #666;\n  min-width: 80px;\n}\n\n.nav-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.status-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 6px;\n}\n\n.status-item.success {\n  background: rgba(82, 196, 26, 0.1);\n}\n\n.status-text {\n  font-size: 14px;\n  color: #333;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=8496c0dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=8496c0dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760722124\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}