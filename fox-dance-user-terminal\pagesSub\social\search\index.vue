<template>
  <view class="search-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <u-search
        v-model="keyword"
        placeholder="搜索帖子、用户或话题"
        :show-action="true"
        action-text="搜索"
        @search="onSearch"
        @custom="onSearch"
      ></u-search>
    </view>

    <!-- 搜索结果 -->
    <view v-if="searched" class="search-results">
      <!-- 搜索类型切换 -->
      <view class="search-tabs">
        <u-tag
          text="全部"
          :bg-color="searchType === 'all' ? '#ff6b87' : ''"
          :color="searchType === 'all' ? '#ffffff' : '#909399'"
          :border-color="searchType === 'all' ? '#ff6b87' : '#e4e7ed'"
          :mode="searchType === 'all' ? 'dark' : 'plain'"
          size="default"
          shape="circle"
          @click="changeSearchType('all')"
        ></u-tag>
        <u-tag
          text="帖子"
          :bg-color="searchType === 'post' ? '#ff6b87' : ''"
          :color="searchType === 'post' ? '#ffffff' : '#909399'"
          :border-color="searchType === 'post' ? '#ff6b87' : '#e4e7ed'"
          :mode="searchType === 'post' ? 'dark' : 'plain'"
          size="default"
          shape="circle"
          @click="changeSearchType('post')"
        ></u-tag>
        <u-tag
          text="用户"
          :bg-color="searchType === 'user' ? '#ff6b87' : ''"
          :color="searchType === 'user' ? '#ffffff' : '#909399'"
          :border-color="searchType === 'user' ? '#ff6b87' : '#e4e7ed'"
          :mode="searchType === 'user' ? 'dark' : 'plain'"
          size="default"
          shape="circle"
          @click="changeSearchType('user')"
        ></u-tag>
        <u-tag
          text="话题"
          :bg-color="searchType === 'tag' ? '#ff6b87' : ''"
          :color="searchType === 'tag' ? '#ffffff' : '#909399'"
          :border-color="searchType === 'tag' ? '#ff6b87' : '#e4e7ed'"
          :mode="searchType === 'tag' ? 'dark' : 'plain'"
          size="default"
          shape="circle"
          @click="changeSearchType('tag')"
        ></u-tag>
      </view>

      <!-- 搜索结果内容 -->
      <scroll-view
        class="results-content"
        scroll-y
        enable-flex
        @scrolltolower="loadMoreResults"
      >
        <!-- 加载状态 -->
        <view v-if="loading && results.length === 0" class="loading-container">
          <u-loading mode="circle" size="40" color="#ff6b87"></u-loading>
          <text class="loading-text">搜索中...</text>
        </view>

        <!-- 搜索结果列表 -->
        <view v-else-if="hasSearchResults" class="results-list">
          <!-- 调试信息 -->
          <view class="debug-info" v-if="false">
            <text>搜索类型: {{ searchType }}, 结果数量: {{ results.length }}</text>
            <text v-if="searchType === 'tag'">话题数据: {{ JSON.stringify(searchResults.tags) }}</text>
          </view>

          <!-- 全部搜索结果 - 混合显示 -->
          <template v-if="searchType === 'all'">
            <!-- 用户结果 -->
            <template v-if="searchResults.users && searchResults.users.length > 0">
              <view class="result-section">
                <view class="section-title">用户</view>
                <UserCard
                  v-for="(user, index) in convertUsersForDisplay(searchResults.users.slice(0, 3))"
                  :key="user.id || user.userId || index"
                  :user="user"
                  class="user-card-item"
                />
                <view v-if="searchResults.users.length > 3" class="view-more" @click="changeSearchType('user')">
                  查看更多用户 ({{ searchResults.users.length }})
                </view>
              </view>
            </template>

            <!-- 话题结果 -->
            <template v-if="searchResults.tags && searchResults.tags.length > 0">
              <view class="result-section">
                <view class="section-title">话题</view>
                <TagCard
                  v-for="(tag, index) in convertTagsForDisplay(searchResults.tags.slice(0, 3))"
                  :key="tag.id || tag.tagId || index"
                  :tag="tag"
                  class="tag-card-item"
                />
                <view v-if="searchResults.tags.length > 3" class="view-more" @click="changeSearchType('tag')">
                  查看更多话题 ({{ searchResults.tags.length }})
                </view>
              </view>
            </template>

            <!-- 帖子结果 -->
            <template v-if="searchResults.posts && searchResults.posts.length > 0">
              <view class="result-section">
                <view class="section-title">帖子</view>
                <PostCard
                  v-for="(post, index) in convertPostsForDisplay(searchResults.posts)"
                  :key="post.id || index"
                  :post="post"
                  class="post-card-item"
                />
              </view>
            </template>
          </template>

          <!-- 帖子结果 -->
          <template v-else-if="searchType === 'post'">
            <PostCard
              v-for="(post, index) in results"
              :key="post.id || index"
              :post="post"
              class="post-card-item"
            />
          </template>

          <!-- 用户结果 -->
          <template v-else-if="searchType === 'user'">
            <UserCard
              v-for="(user, index) in results"
              :key="user.id || user.userId || index"
              :user="user"
              class="user-card-item"
            />
          </template>

          <!-- 话题结果 -->
          <template v-else-if="searchType === 'tag'">
            <TagCard
              v-for="(tag, index) in results"
              :key="tag.id || tag.tagId || index"
              :tag="tag"
              class="tag-card-item"
            />
          </template>

          <!-- 加载更多状态 - 仅非全部搜索显示 -->
          <template v-if="searchType !== 'all'">
            <view v-if="pagination.hasMore" class="load-more">
              <view v-if="loading" class="loading-more">
                <u-loading mode="circle" size="24" color="#ff6b87"></u-loading>
                <text class="loading-text">加载更多...</text>
              </view>
              <view v-else class="load-more-tip">上拉加载更多</view>
            </view>
            <view v-else class="no-more">没有更多内容了</view>
          </template>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-container">
          <u-empty mode="search" text="没有找到相关内容">
            <template #icon>
              <u-icon name="search" size="80" color="#d0d0d0"></u-icon>
            </template>
          </u-empty>
          <view class="empty-tips">
            <text>试试其他关键词或</text>
            <text class="back-link" @click="backToHome">返回首页</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索历史和热门搜索 -->
    <view v-else class="discovery-section">
      <!-- 搜索历史 -->
      <view v-if="searchHistory.length > 0" class="history-section">
        <view class="section-header">
          <text class="section-title">搜索历史</text>
          <u-icon name="trash" color="#999" size="20" @click="clearHistory"></u-icon>
        </view>
        <view class="tags-container">
          <u-tag
            v-for="(item, index) in searchHistory"
            :key="index"
            :text="item"
            type="info"
            @click="onTagClick(item)"
          ></u-tag>
        </view>
      </view>

      <!-- 热门搜索 -->
      <view class="hot-searches-section">
        <view class="section-header">
          <text class="section-title">热门搜索</text>
        </view>
        <view class="tags-container">
          <u-tag
            v-for="(item, index) in hotSearches"
            :key="index"
            :text="item"
            type="warning"
            plain
            @click="onTagClick(item)"
          ></u-tag>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import PostCard from "../components/PostCard.vue";
import UserCard from "../components/UserCard.vue";
import TagCard from "../components/TagCard.vue";
import {
  comprehensiveSearch,
  getHotKeywords,
  getSearchHistory,
  getSearchSuggestions
} from "@/utils/socialApi.js";

export default {
  name: "SearchPage",
  components: {
    PostCard,
    UserCard,
    TagCard
  },
  data() {
    return {
      keyword: "",
      searched: false,
      searchHistory: [],
      hotSearches: [],
      results: [],
      loading: false,
      searchType: 'all', // all, post, user, tag
      pagination: {
        current: 1,
        size: 10,
        hasMore: true
      },
      searchResults: {
        posts: [],
        users: [],
        tags: [],
        totalCount: 0
      },
      currentUserId: 0
    };
  },

  computed: {
    // 判断是否有搜索结果
    hasSearchResults() {
      if (this.searchType === 'all') {
        return (this.searchResults.posts && this.searchResults.posts.length > 0) ||
               (this.searchResults.users && this.searchResults.users.length > 0) ||
               (this.searchResults.tags && this.searchResults.tags.length > 0);
      } else {
        return this.results.length > 0;
      }
    }
  },

  onLoad() {
    this.loadInitialData();
    this.loadCurrentUserId();
  },

  methods: {
    // 加载初始数据
    async loadInitialData() {
      try {
        // 并行加载热门搜索词和搜索历史
        const [hotKeywordsResult, historyResult] = await Promise.all([
          this.loadHotKeywords(),
          this.loadSearchHistory()
        ]);

        console.log('初始数据加载完成');
      } catch (error) {
        console.error('初始数据加载失败:', error);
      }
    },

    // 加载热门搜索词
    async loadHotKeywords() {
      try {
        const result = await getHotKeywords(10);
        if (result && result.code === 0 && result.data) {
          this.hotSearches = result.data;
          console.log('热门搜索词加载成功:', this.hotSearches);
        } else {
          // 使用默认热门搜索词
          this.hotSearches = ["街舞", "现代舞", "芭蕾", "拉丁舞", "爵士舞", "民族舞", "古典舞", "舞蹈教学"];
        }
      } catch (error) {
        console.error('加载热门搜索词失败:', error);
        // 使用默认热门搜索词
        this.hotSearches = ["街舞", "现代舞", "芭蕾", "拉丁舞", "爵士舞", "民族舞", "古典舞", "舞蹈教学"];
      }
    },

    // 加载搜索历史
    async loadSearchHistory() {
      try {
        const result = await getSearchHistory(20);
        if (result && result.code === 0 && result.data) {
          this.searchHistory = result.data;
          console.log('搜索历史加载成功:', this.searchHistory);
        } else {
          // 从本地存储获取搜索历史
          const localHistory = uni.getStorageSync('searchHistory') || [];
          this.searchHistory = localHistory;
        }
      } catch (error) {
        console.error('加载搜索历史失败:', error);
        // 从本地存储获取搜索历史
        const localHistory = uni.getStorageSync('searchHistory') || [];
        this.searchHistory = localHistory;
      }
    },
    // 加载当前用户userId
    async loadCurrentUserId() {
      try {
        this.currentUserId = uni.getStorageSync('userid') || 0;
      } catch (error) {
        console.error('加载当前用户ID失败:', error);
        this.currentUserId = 0;
      }
    },

    // 执行搜索
    async onSearch(value) {
      if (!value || !value.trim()) {
        uni.showToast({
          title: '请输入搜索关键词',
          icon: 'none'
        });
        return;
      }

      const keyword = value.trim();
      this.keyword = keyword;
      this.searched = true;
      this.loading = true;

      // 重置分页和结果
      this.pagination.current = 1;
      this.pagination.hasMore = true;
      this.searchResults = {
        posts: [],
        users: [],
        tags: [],
        totalCount: 0
      };

      try {
        // 更新搜索历史
        this.updateHistory(keyword);

        // 执行搜索
        await this.performSearch(keyword);

      } catch (error) {
        console.error('搜索失败:', error);
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 执行实际搜索
    async performSearch(keyword, loadMore = false) {
      try {
        const params = {
          keyword: keyword,
          current: this.pagination.current,
          size: this.pagination.size,
          type: this.searchType,
          userId: this.currentUserId
        };

        console.log('搜索参数:', params);
        const result = await comprehensiveSearch(params);
        console.log('搜索结果:', result);

        if (result && result.code === 0 && result.data) {
          const searchData = result.data;

          if (loadMore) {
            // 加载更多，追加结果
            if (searchData.posts) {
              this.searchResults.posts = [...this.searchResults.posts, ...searchData.posts];
            }
            if (searchData.users) {
              this.searchResults.users = [...this.searchResults.users, ...searchData.users];
            }
            if (searchData.tags) {
              this.searchResults.tags = [...this.searchResults.tags, ...searchData.tags];
            }
          } else {
            // 新搜索，替换结果
            this.searchResults = {
              posts: searchData.posts || [],
              users: searchData.users || [],
              tags: searchData.tags || [],
              totalCount: searchData.totalCount || 0
            };
          }

          // 更新分页信息
          this.pagination.hasMore = searchData.hasMore || false;

          // 根据搜索类型设置结果数据
          this.updateResultsByType();

          console.log('搜索完成，结果数量:', {
            posts: this.searchResults.posts.length,
            users: this.searchResults.users.length,
            tags: this.searchResults.tags.length,
            total: this.searchResults.totalCount
          });

        } else {
          console.error('搜索API返回格式不正确:', result);
          this.searchResults = {
            posts: [],
            users: [],
            tags: [],
            totalCount: 0
          };
          this.results = [];
        }

      } catch (error) {
        console.error('执行搜索失败:', error);
        throw error;
      }
    },

    // 根据搜索类型更新结果数据
    updateResultsByType() {
      switch (this.searchType) {
        case 'all':
          // 全部搜索直接使用searchResults，不需要设置results
          this.results = [];
          break;
        case 'post':
          this.results = this.convertPostsForDisplay(this.searchResults.posts);
          break;
        case 'user':
          this.results = this.convertUsersForDisplay(this.searchResults.users);
          break;
        case 'tag':
          this.results = this.convertTagsForDisplay(this.searchResults.tags);
          break;
        default:
          this.results = [];
      }
    },

    // 转换帖子数据格式
    convertPostsForDisplay(posts) {
      return posts.map(post => ({
        id: post.id,
        title: post.title || '',
        content: post.content || '',
        username: post.username || '无名氏',
        userAvatar: this.formatAvatarUrl(post.userAvatar),
        coverImage: post.coverImage,
        likeCount: post.likeCount || 0,
        commentCount: post.commentCount || 0,
        createTime: post.createTime,
        isLiked: post.isLiked || false,
        isFavorited: post.isFavorited || false
      }));
    },

    // 转换用户数据格式
    convertUsersForDisplay(users) {
      return users.map(user => ({
        id: user.id || user.userId,
        userId: user.id || user.userId,
        username: user.username,
        nickname: user.nickname,
        avatar: this.formatAvatarUrl(user.avatar),
        bio: user.bio || user.description,
        postCount: user.postCount || 0,
        followerCount: user.followerCount || 0,
        followingCount: user.followingCount || 0,
        isFollowed: user.isFollowed,
        isVerified: user.isVerified || false
      }));
    },

    // 转换话题数据格式
    convertTagsForDisplay(tags) {
      return tags.map(tag => ({
        id: tag.id || tag.tagId,
        tagId: tag.id || tag.tagId,
        name: tag.name || tag.tagName,
        tagName: tag.name || tag.tagName,
        description: tag.description,
        coverImage: tag.coverImage,
        postCount: tag.postCount || 0,
        viewCount: tag.viewCount || 0,
        isFollowed: tag.isFollowed || false,
        isHot: tag.isHot || false
      }));
    },

    // 格式化头像URL
    formatAvatarUrl(avatar) {
      if (!avatar) {
        return '/static/images/toux.png';
      }
      return 'https://file.foxdance.com.cn' + avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85';
    },
    // 更新搜索历史
    updateHistory(keyword) {
      try {
        // 更新内存中的搜索历史
        const index = this.searchHistory.indexOf(keyword);
        if (index > -1) {
          this.searchHistory.splice(index, 1);
        }
        this.searchHistory.unshift(keyword);
        if (this.searchHistory.length > 20) {
          this.searchHistory.pop();
        }

        // 保存到本地存储
        uni.setStorageSync('searchHistory', this.searchHistory);

        console.log('搜索历史已更新:', this.searchHistory);
      } catch (error) {
        console.error('更新搜索历史失败:', error);
      }
    },
    // 清空搜索历史
    async clearHistory() {
      try {
        uni.showModal({
          title: '确认清空',
          content: '确定要清空所有搜索历史吗？',
          success: async (res) => {
            if (res.confirm) {
              this.searchHistory = [];
              uni.removeStorageSync('searchHistory');

              uni.showToast({
                title: '已清空搜索历史',
                icon: 'success'
              });

              console.log('搜索历史已清空');
            }
          }
        });
      } catch (error) {
        console.error('清空搜索历史失败:', error);
      }
    },
    onTagClick(tag) {
      this.keyword = tag;
      this.onSearch(tag);
    },
    generateMockResults(keyword) {
      const mockPosts = [];
      for (let i = 0; i < 5; i++) {
        mockPosts.push({
          id: `search-${i}`,
          title: `关于“${keyword}”的帖子标题 ${i + 1}`,
          username: `用户${Math.floor(Math.random() * 1000)}`,
          userAvatar: `https://picsum.photos/100/100?random=${i}`,
          coverImage: `https://picsum.photos/300/400?random=${i}`,
          likeCount: Math.floor(Math.random() * 100),
          commentCount: Math.floor(Math.random() * 20)
        });
      }
      return mockPosts;
    },

    // 加载更多搜索结果
    async loadMoreResults() {
      // 全部搜索不支持加载更多
      if (this.searchType === 'all') {
        return;
      }

      if (!this.pagination.hasMore || this.loading) {
        return;
      }

      this.loading = true;
      this.pagination.current += 1;

      try {
        await this.performSearch(this.keyword, true);
      } catch (error) {
        console.error('加载更多失败:', error);
        this.pagination.current -= 1; // 回退页码
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 切换搜索类型
    changeSearchType(type) {
      if (this.searchType === type) return;

      this.searchType = type;
      console.log('切换搜索类型:', type);

      // 如果已经有搜索结果，根据新类型更新显示
      if (this.searched && this.searchResults) {
        this.updateResultsByType();
      }
    },

    // 返回搜索首页
    backToHome() {
      this.searched = false;
      this.keyword = '';
      this.results = [];
      this.searchResults = {
        posts: [],
        users: [],
        tags: [],
        totalCount: 0
      };
      this.pagination.current = 1;
      this.pagination.hasMore = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}
.search-bar {
  padding: 16rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e4e7ed;
}
.discovery-section {
  padding: 32rpx;
  flex: 1;
  overflow-y: auto;
}

.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 0; /* 配合flex: 1使用，确保高度计算正确 */
  box-sizing: border-box;
}
.history-section,
.hot-searches-section {
  margin-bottom: 40rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.results-list {
  width: 100%;
  box-sizing: border-box;
}

.post-card-item,
.user-card-item,
.tag-card-item {
  width: 100%;
  margin-bottom: 24rpx;
  box-sizing: border-box;
  flex-shrink: 0; /* 防止被压缩 */

  &:last-child {
    margin-bottom: 0;
  }
}

/* 调试信息样式 */
.debug-info {
  padding: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #666;
}

/* 搜索标签页 */
.search-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #fff;
  border-bottom: 1rpx solid #e4e7ed;
  padding: 24rpx 32rpx;
  gap: 24rpx;
}

/* 自定义标签样式 */
.search-tabs /deep/ .u-tag {
  min-width: 120rpx;
  text-align: center;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

/* 搜索结果内容 */
.results-content {
  flex: 1;
  width: 100%;
  height: 100%;
  padding: 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #909399;
}

/* 加载更多 */
.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.load-more-tip {
  font-size: 28rpx;
  color: #909399;
}

.no-more {
  padding: 40rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #c0c4cc;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-tips {
  margin-top: 32rpx;
  font-size: 28rpx;
  color: #909399;
}

.back-link {
  color: #ff6b87;
  text-decoration: underline;
  margin-left: 8rpx;
}

/* 全部搜索结果分组样式 */
.result-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24rpx;
  padding-left: 8rpx;
  border-left: 6rpx solid #ff6b87;
}

.view-more {
  margin-top: 24rpx;
  padding: 20rpx;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #ff6b87;
  border: 2rpx solid #ff6b87;
  transition: all 0.3s ease;
}

.view-more:active {
  background-color: #ff6b87;
  color: #ffffff;
}

/* 卡片间距调整 */
.user-card-item,
.tag-card-item,
.post-card-item {
  margin-bottom: 24rpx;
}

.user-card-item:last-child,
.tag-card-item:last-child,
.post-card-item:last-child {
  margin-bottom: 0;
}
</style>