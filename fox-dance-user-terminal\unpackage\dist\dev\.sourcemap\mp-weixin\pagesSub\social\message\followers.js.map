{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/followers.vue?6414", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/followers.vue?357f", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/followers.vue?1732", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/followers.vue?8411", "uni-app:///pagesSub/social/message/followers.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/followers.vue?048e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/followers.vue?781a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "FollowButton", "data", "isRefreshing", "hasMore", "page", "pageSize", "loading", "messageList", "followBtnStyle", "width", "height", "fontSize", "followedBtnStyle", "color", "borderColor", "onLoad", "methods", "formatTime", "openUserProfile", "message", "uni", "url", "onUserFollow", "console", "onUserUnfollow", "onFollowChange", "updateUserFollowStatus", "onRefresh", "loadMore", "loadMessages", "currentUserId", "current", "size", "result", "followers", "newMessages", "id", "userId", "userName", "userAvatar", "userDesc", "userTags", "createTime", "isRead", "isFollowBack", "title", "icon", "getCurrentUserId", "formatAvatarUrl", "parseUserTags"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,kOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAAkvB,CAAgB,msBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACiFtwB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;QACAC;QACAC;QACAE;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QAAA;MAAA;MACA;QACAC;MACA;;MAEA;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAC;MACA;MACA;IACA;IAEA;IACAC;MACAD;MACA;MACA;IACA;IAEA;IACAE;MACAF;MACA;MACA;IACA;IAEA;IACAG;MACA;QAAA;MAAA;MACA;QACAP;MACA;IACA;IAEAQ;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAP;gBAAA;cAAA;gBAIAA;gBAAA;gBAAA,OACA;kBACAQ;kBACAC;gBACA;cAAA;gBAHAC;gBAIAV;gBAEA;kBACAW,kFAEA;kBACAC;oBAAA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBAAA;sBACAC;oBACA;kBAAA;kBAEA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACArB;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAH;kBACAyB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvQA;AAAA;AAAA;AAAA;AAAq6C,CAAgB,0wCAAG,EAAC,C;;;;;;;;;;;ACAz7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/message/followers.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/message/followers.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./followers.vue?vue&type=template&id=2eeb3bc0&scoped=true&\"\nvar renderjs\nimport script from \"./followers.vue?vue&type=script&lang=js&\"\nexport * from \"./followers.vue?vue&type=script&lang=js&\"\nimport style0 from \"./followers.vue?vue&type=style&index=0&id=2eeb3bc0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2eeb3bc0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/followers.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./followers.vue?vue&type=template&id=2eeb3bc0&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tag/u-tag\" */ \"@/components/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.messageList && _vm.messageList.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.messageList, function (message, __i0__) {\n        var $orig = _vm.__get_orig(message)\n        var m0 = _vm.formatTime(message.createTime)\n        var g1 = message.userTags && message.userTags.length > 0\n        var a0 = {\n          id: message.userId,\n          nickname: message.userName,\n        }\n        return {\n          $orig: $orig,\n          m0: m0,\n          g1: g1,\n          a0: a0,\n        }\n      })\n    : null\n  var g2 = _vm.hasMore && _vm.messageList && _vm.messageList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./followers.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./followers.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"followers-container\">\n    <!-- 消息列表 -->\n    <scroll-view \n      class=\"message-list\"\n      scroll-y\n      refresher-enabled\n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMore\"\n    >\n      <view v-if=\"messageList && messageList.length > 0\">\n        <view \n          v-for=\"message in messageList\" \n          :key=\"message.id\"\n          class=\"message-card\"\n          @click=\"openUserProfile(message.userId)\"\n        >\n          <!-- 用户头像 -->\n          <u-avatar \n            :src=\"message.userAvatar\" \n            size=\"50\"\n            class=\"user-avatar\"\n          ></u-avatar>\n          \n          <!-- 用户信息 -->\n          <view class=\"user-info\">\n            <view class=\"user-header\">\n              <text class=\"user-name\">{{ message.userName }}</text>\n              <text class=\"follow-time\">{{ formatTime(message.createTime) }}</text>\n            </view>\n            <text class=\"user-desc\">{{ message.userDesc || '这个人很懒，什么都没有留下' }}</text>\n            \n            <!-- 用户标签 -->\n            <view v-if=\"message.userTags && message.userTags.length > 0\" class=\"user-tags\">\n              <u-tag \n                v-for=\"tag in message.userTags\" \n                :key=\"tag\"\n                :text=\"tag\"\n                size=\"mini\"\n                type=\"primary\"\n                mode=\"light\"\n                class=\"tag-item\"\n              ></u-tag>\n            </view>\n          </view>\n          \n          <!-- 关注按钮 -->\n          <view class=\"follow-actions\">\n            <FollowButton\n              :user=\"{ id: message.userId, nickname: message.userName }\"\n              :followed=\"message.isFollowBack\"\n              size=\"mini\"\n              @follow=\"onUserFollow\"\n              @unfollow=\"onUserUnfollow\"\n              @change=\"onFollowChange\"\n              @click.stop\n            />\n          </view>\n          \n          <!-- 未读标识 -->\n          <view v-if=\"!message.isRead\" class=\"unread-dot\"></view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-else class=\"empty-state\">\n        <u-empty mode=\"data\" text=\"暂无新粉丝\"></u-empty>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"hasMore && messageList && messageList.length > 0\" class=\"load-more\">\n        <u-loading mode=\"flower\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport FollowButton from '../components/FollowButton.vue'\nimport { getFollowersList } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'NewFollowers',\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      isRefreshing: false,\n      hasMore: true,\n      page: 1,\n      pageSize: 20,\n      loading: false,\n      messageList: [],\n      followBtnStyle: {\n        width: '120rpx',\n        height: '60rpx',\n        fontSize: '24rpx'\n      },\n      followedBtnStyle: {\n        width: '120rpx',\n        height: '60rpx',\n        fontSize: '24rpx',\n        color: '#999',\n        borderColor: '#e4e7ed'\n      }\n    }\n  },\n  onLoad() {\n    this.loadMessages()\n  },\n  methods: {\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - time\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n\n      if (minutes < 60) {\n        return `${minutes}分钟前关注了你`\n      } else if (hours < 24) {\n        return `${hours}小时前关注了你`\n      } else {\n        return `${days}天前关注了你`\n      }\n    },\n\n    openUserProfile(userId) {\n      // 标记为已读\n      const message = this.messageList.find(msg => msg.userId === userId)\n      if (message) {\n        message.isRead = true\n      }\n\n      // 跳转到用户主页\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?userId=${userId}&name=${message ? message.userName : ''}`\n      })\n    },\n\n    // 用户关注成功事件\n    onUserFollow(data) {\n      console.log('用户关注成功:', data)\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, true)\n    },\n\n    // 用户取消关注成功事件\n    onUserUnfollow(data) {\n      console.log('用户取消关注成功:', data)\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, false)\n    },\n\n    // 关注状态变化事件\n    onFollowChange(data) {\n      console.log('关注状态变化:', data)\n      // 更新本地数据\n      this.updateUserFollowStatus(data.user.id, data.isFollowed)\n    },\n\n    // 更新用户关注状态的辅助方法\n    updateUserFollowStatus(userId, isFollowed) {\n      const message = this.messageList.find(msg => msg.userId === userId)\n      if (message) {\n        message.isFollowBack = isFollowed\n      }\n    },\n\n    onRefresh() {\n      this.isRefreshing = true\n      this.page = 1\n      this.loadMessages().finally(() => {\n        this.isRefreshing = false\n      })\n    },\n\n    loadMore() {\n      if (!this.hasMore) return\n      this.page++\n      this.loadMessages()\n    },\n\n    async loadMessages() {\n      if (this.loading) return\n\n      try {\n        this.loading = true\n        const currentUserId = this.getCurrentUserId()\n        if (!currentUserId) {\n          console.error('用户未登录，无法加载粉丝列表')\n          return\n        }\n\n        console.log('开始加载新粉丝列表...')\n        const result = await getFollowersList(currentUserId, {\n          current: this.page,\n          size: this.pageSize\n        })\n        console.log('新粉丝API返回:', result)\n\n        if (result && result.code === 0 && result.data) {\n          const followers = Array.isArray(result.data) ? result.data : (result.data.records || [])\n\n          // 转换数据格式\n          const newMessages = followers.map(follower => ({\n            id: follower.userId || follower.id,\n            userId: follower.userId || follower.id,\n            userName: follower.nickname || '用户',\n            userAvatar: this.formatAvatarUrl(follower.avatar),\n            userDesc: follower.bio || '这个人很懒，什么都没有留下',\n            userTags: this.parseUserTags(follower.danceType),\n            createTime: new Date(follower.followTime || follower.createTime || Date.now()),\n            isRead: Math.random() > 0.5, // 暂时随机生成已读状态\n            isFollowBack: follower.isFollowed || false\n          }))\n\n          if (this.page === 1) {\n            this.messageList = newMessages\n          } else {\n            this.messageList.push(...newMessages)\n          }\n\n          // 检查是否还有更多数据\n          this.hasMore = followers.length >= this.pageSize\n        } else {\n          console.error('新粉丝API返回格式不正确:', result)\n          if (this.page === 1) {\n            this.messageList = []\n          }\n        }\n      } catch (error) {\n        console.error('加载新粉丝失败:', error)\n        uni.showToast({\n          title: '加载失败，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 获取当前用户ID\n    getCurrentUserId() {\n      return uni.getStorageSync('userid')\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (!avatar) return '/static/images/toux.png'\n      if (avatar.startsWith('http')) return avatar\n      return avatar.startsWith('/') ? avatar : `/${avatar}`\n    },\n\n    // 解析用户标签\n    parseUserTags(danceType) {\n      if (!danceType) return []\n      return [danceType]\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.followers-container {\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.message-list {\n  height: 100%;\n  padding: 32rpx 0;\n}\n\n.message-card {\n  display: flex;\n  align-items: flex-start;\n  padding: 32rpx;\n  margin: 0 32rpx 24rpx;\n  background: #fff;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.user-avatar {\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.user-info {\n  flex: 1;\n  margin-right: 24rpx;\n}\n\n.user-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 12rpx;\n}\n\n.user-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.follow-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.user-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.user-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.tag-item {\n  margin: 0;\n}\n\n.follow-actions {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.unread-dot {\n  position: absolute;\n  top: 32rpx;\n  right: 32rpx;\n  width: 16rpx;\n  height: 16rpx;\n  background: #ff4757;\n  border-radius: 50%;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 32rpx;\n}\n\n.load-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 16rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./followers.vue?vue&type=style&index=0&id=2eeb3bc0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./followers.vue?vue&type=style&index=0&id=2eeb3bc0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751619132\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}