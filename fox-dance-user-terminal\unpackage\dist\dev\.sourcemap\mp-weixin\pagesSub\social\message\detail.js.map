{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/detail.vue?4df6", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/detail.vue?91dd", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/detail.vue?c235", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/detail.vue?6f06", "uni-app:///pagesSub/social/message/detail.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/detail.vue?8a8a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/detail.vue?dd17"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "messageId", "messageDetail", "id", "type", "title", "content", "htmlContent", "createTime", "isRead", "actions", "text", "action", "url", "attachments", "size", "onLoad", "methods", "goBack", "uni", "getIconName", "system", "activity", "security", "formatTime", "loadMessageDetail", "console", "mark<PERSON><PERSON><PERSON>", "handleAction", "setTimeout", "openAttachment", "success", "filePath", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA+uB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuDnwB;EACAC;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC,ixCAmBA;QACAC;QACAC;QACAC,UACA;UACAP;UACAC;UACAO;UACAC;QACA,GACA;UACAT;UACAC;UACAO;UACAC;UACAC;QACA,EACA;QACAC,cACA;UACAX;UACAJ;UACAc;UACAE;QACA;MAEA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBAAA;gBAAA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;MACAD;IACA;IAEAE;MAAA;MACA;QACA;UACA;UACAC;YACA;UACA;UACA;QACA;UACA;YACA;YACAV;cACAN;YACA;UACA;UACA;QACA;UACAa;MAAA;IAEA;IAEAI;MAAA;MACA;MACAX;QACAN;QACAkB;UACA;YACAZ;cACAa;cACAD;gBACAL;cACA;cACAO;gBACAP;gBACA;cACA;YACA;UACA;QACA;QACAO;UACAP;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9MA;AAAA;AAAA;AAAA;AAAk6C,CAAgB,uwCAAG,EAAC,C;;;;;;;;;;;ACAt7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/message/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/message/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=01825956&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=01825956&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"01825956\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=01825956&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getIconName(_vm.messageDetail.type)\n  var m1 = _vm.formatTime(_vm.messageDetail.createTime)\n  var g0 = _vm.messageDetail.actions && _vm.messageDetail.actions.length > 0\n  var g1 =\n    _vm.messageDetail.attachments && _vm.messageDetail.attachments.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"message-detail-container\">\n\n    <!-- 消息内容 -->\n    <scroll-view class=\"content-scroll\" scroll-y>\n      <view class=\"message-detail\">\n        <!-- 消息头部 -->\n        <view class=\"message-header\">\n          <view class=\"message-icon\" :class=\"'icon-' + messageDetail.type\">\n            <u-icon :name=\"getIconName(messageDetail.type)\" size=\"24\" color=\"#fff\"></u-icon>\n          </view>\n          <view class=\"header-info\">\n            <text class=\"message-title\">{{ messageDetail.title }}</text>\n            <text class=\"message-time\">{{ formatTime(messageDetail.createTime) }}</text>\n          </view>\n        </view>\n\n        <!-- 消息正文 -->\n        <view class=\"message-body\">\n          <rich-text :nodes=\"messageDetail.htmlContent || messageDetail.content\"></rich-text>\n        </view>\n\n        <!-- 相关操作按钮 -->\n        <view v-if=\"messageDetail.actions && messageDetail.actions.length > 0\" class=\"action-buttons\">\n          <view \n            v-for=\"action in messageDetail.actions\" \n            :key=\"action.id\"\n            class=\"action-btn\"\n            :class=\"action.type\"\n            @click=\"handleAction(action)\"\n          >\n            <text class=\"btn-text\">{{ action.text }}</text>\n          </view>\n        </view>\n\n        <!-- 附件列表 -->\n        <view v-if=\"messageDetail.attachments && messageDetail.attachments.length > 0\" class=\"attachments\">\n          <text class=\"section-title\">相关附件</text>\n          <view \n            v-for=\"attachment in messageDetail.attachments\" \n            :key=\"attachment.id\"\n            class=\"attachment-item\"\n            @click=\"openAttachment(attachment)\"\n          >\n            <u-icon name=\"file-text\" size=\"20\" color=\"#2979ff\"></u-icon>\n            <text class=\"attachment-name\">{{ attachment.name }}</text>\n            <u-icon name=\"arrow-right\" size=\"16\" color=\"#999\"></u-icon>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'MessageDetail',\n  data() {\n    return {\n      messageId: '',\n      messageDetail: {\n        id: 1,\n        type: 'system',\n        title: '系统维护通知',\n        content: '系统将于今晚23:00-次日1:00进行维护升级，期间可能影响部分功能使用，请您提前做好准备。',\n        htmlContent: `\n          <div style=\"line-height: 1.6; color: #333;\">\n            <p>尊敬的用户：</p>\n            <p>为了给您提供更好的服务体验，我们将对系统进行维护升级。</p>\n            <p><strong>维护时间：</strong>今晚23:00-次日1:00</p>\n            <p><strong>影响范围：</strong></p>\n            <ul>\n              <li>视频上传功能暂时不可用</li>\n              <li>消息推送可能延迟</li>\n              <li>部分页面可能无法正常访问</li>\n            </ul>\n            <p><strong>建议：</strong></p>\n            <ul>\n              <li>请提前保存您的创作内容</li>\n              <li>避免在维护期间进行重要操作</li>\n            </ul>\n            <p>感谢您的理解与支持！</p>\n            <p style=\"text-align: right; margin-top: 20px;\">Fox Dance 团队<br/>2024年7月16日</p>\n          </div>\n        `,\n        createTime: new Date(Date.now() - 3600000),\n        isRead: true,\n        actions: [\n          {\n            id: 1,\n            type: 'primary',\n            text: '我知道了',\n            action: 'confirm'\n          },\n          {\n            id: 2,\n            type: 'secondary',\n            text: '查看更多',\n            action: 'more',\n            url: 'https://help.foxdance.com'\n          }\n        ],\n        attachments: [\n          {\n            id: 1,\n            name: '维护详情说明.pdf',\n            url: 'https://example.com/maintenance.pdf',\n            size: '2.5MB'\n          }\n        ]\n      }\n    }\n  },\n  onLoad(options) {\n    this.messageId = options.id\n    this.loadMessageDetail()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    getIconName(type) {\n      const iconMap = {\n        system: 'setting',\n        activity: 'volume',\n        security: 'shield'\n      }\n      return iconMap[type] || 'bell'\n    },\n\n    formatTime(time) {\n      const date = new Date(time)\n      const year = date.getFullYear()\n      const month = (date.getMonth() + 1).toString().padStart(2, '0')\n      const day = date.getDate().toString().padStart(2, '0')\n      const hours = date.getHours().toString().padStart(2, '0')\n      const minutes = date.getMinutes().toString().padStart(2, '0')\n      \n      return `${year}-${month}-${day} ${hours}:${minutes}`\n    },\n\n    async loadMessageDetail() {\n      try {\n        // 模拟API请求\n        await new Promise(resolve => setTimeout(resolve, 500))\n        \n        // 标记消息为已读\n        this.markAsRead()\n      } catch (error) {\n        console.error('加载消息详情失败:', error)\n        this.$u.toast('加载失败，请重试')\n      }\n    },\n\n    markAsRead() {\n      // 调用API标记消息为已读\n      console.log('标记消息已读:', this.messageId)\n    },\n\n    handleAction(action) {\n      switch (action.action) {\n        case 'confirm':\n          this.$u.toast('已确认')\n          setTimeout(() => {\n            this.goBack()\n          }, 1000)\n          break\n        case 'more':\n          if (action.url) {\n            // 在小程序中打开网页\n            uni.navigateTo({\n              url: `/pages/webview/index?url=${encodeURIComponent(action.url)}`\n            })\n          }\n          break\n        default:\n          console.log('未知操作:', action)\n      }\n    },\n\n    openAttachment(attachment) {\n      // 下载或预览附件\n      uni.downloadFile({\n        url: attachment.url,\n        success: (res) => {\n          if (res.statusCode === 200) {\n            uni.openDocument({\n              filePath: res.tempFilePath,\n              success: () => {\n                console.log('打开文档成功')\n              },\n              fail: (err) => {\n                console.error('打开文档失败:', err)\n                this.$u.toast('无法打开此文件')\n              }\n            })\n          }\n        },\n        fail: (err) => {\n          console.error('下载失败:', err)\n          this.$u.toast('下载失败')\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.message-detail-container {\n  //height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n}\n\n.header {\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.header-left, .header-right {\n  width: 80rpx;\n  display: flex;\n  justify-content: flex-start;\n}\n\n.header-title {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n}\n\n.title-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.content-scroll {\n  flex: 1;\n}\n\n.message-detail {\n  background: #fff;\n  margin: 32rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n}\n\n.message-header {\n  display: flex;\n  align-items: center;\n  padding: 32rpx;\n  border-bottom: 2rpx solid #f5f5f5;\n}\n\n.message-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.icon-system {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.icon-activity {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.icon-security {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.header-info {\n  flex: 1;\n}\n\n.message-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.message-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.message-body {\n  padding: 32rpx;\n  line-height: 1.6;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.action-buttons {\n  padding: 0 32rpx 32rpx;\n  display: flex;\n  gap: 24rpx;\n}\n\n.action-btn {\n  flex: 1;\n  height: 80rpx;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn.primary {\n  background: #2979ff;\n}\n\n.action-btn.primary .btn-text {\n  color: #fff;\n}\n\n.action-btn.secondary {\n  background: #f5f5f5;\n  border: 2rpx solid #e4e7ed;\n}\n\n.action-btn.secondary .btn-text {\n  color: #666;\n}\n\n.btn-text {\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n.attachments {\n  padding: 32rpx;\n  border-top: 2rpx solid #f5f5f5;\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 24rpx;\n  display: block;\n}\n\n.attachment-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.attachment-name {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n  margin-left: 16rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=01825956&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=01825956&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760720873\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}