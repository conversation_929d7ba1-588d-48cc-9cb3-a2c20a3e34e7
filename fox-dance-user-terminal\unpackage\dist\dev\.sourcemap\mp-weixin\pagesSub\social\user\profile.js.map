{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?6122", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?2803", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?e1f0", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?8074", "uni-app:///pagesSub/social/user/profile.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?f4ce", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/user/profile.vue?bf35"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "PostCard", "data", "userId", "userInfo", "nickname", "avatar", "bio", "postCount", "followingCount", "followersCount", "likeCount", "isFollowed", "userPosts", "loading", "onLoad", "console", "uni", "title", "icon", "methods", "loadUserInfo", "userProfile", "id", "danceType", "followStatus", "setTimeout", "loadUserPosts", "currentUserId", "current", "pageSize", "result", "coverImage", "username", "userAvatar", "content", "commentCount", "isLiked", "createTime", "to<PERSON><PERSON><PERSON><PERSON>", "onPostLike", "post", "index", "goPostDetail", "url", "goToChat", "encodeURIComponent", "goUserProfile", "goBack", "showMoreActions", "itemList", "success", "onShareAppMessage", "path"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAgvB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC+EpwB;AAQA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAD;QACAE;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;IACAA;IAEA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACAN;gBACA;kBACA;oBACAO;oBACApB;oBACAE;oBACAC,QACA,iCACAgB,0BACA;oBACAf;oBACAiB;oBACAhB;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAa;gBACAT;gBAEA;kBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAC;kBACAC;kBACAC;gBACA;gBACA;gBACAO;kBACAT;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAX;gBAAA;gBAEA;gBACAY,8CAEA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;kBACAF;gBACA;cAAA;gBAJAG;gBAMAf;;gBAEA;gBACA;kBACA;oBAAA;sBACAO;sBACAL;sBACAc;sBACAC;sBACAC;sBACAC;sBACAxB;sBACAyB;sBACAC;sBACAC;oBACA;kBAAA;kBACAtB;gBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAC;kBACAC;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAoB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA,0CACA,GACA,mCACA;gBACAtB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACA;gBACA;gBACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,KAEAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAA;gBACAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1B;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAwB;MACA1B;QACA2B;MACA;IACA;IAEAC;MACA;QACA5B;UACAC;UACAC;QACA;QACA;MACA;MAEAH;;MAEA;MACAC;QACA2B,mDACA,2CACAE,mBACA,uBACA;MACA;IACA;IAEAC;MACA;MACA;MAEA9B;QACA2B;MACA;IACA;IAEAI;MACA/B;IACA;IAEAgC;MACAhC;QACAiC;QACAC;UACAnC;QACA;MACA;IACA;IACA;IACAoC;MACA;QACAlC;QACAmC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1VA;AAAA;AAAA;AAAA;AAAm6C,CAAgB,wwCAAG,EAAC,C;;;;;;;;;;;ACAv7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/user/profile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/user/profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./profile.vue?vue&type=template&id=9e24a6bc&scoped=true&\"\nvar renderjs\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./profile.vue?vue&type=style&index=0&id=9e24a6bc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9e24a6bc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/user/profile.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=template&id=9e24a6bc&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userPosts.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"user-profile-container\">\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-state\">\n        <u-loading mode=\"circle\" size=\"24\"></u-loading>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n\n      <!-- 用户信息 -->\n      <view v-else class=\"user-section\">\n        <view class=\"user-info\">\n          <u-avatar :src=\"userInfo.avatar\" size=\"80\"></u-avatar>\n          <view class=\"user-details\">\n            <text class=\"nickname\">{{ userInfo.nickname || '用户' }}</text>\n            <text class=\"user-id\">ID: {{ userInfo.userId }}</text>\n            <text class=\"bio\">{{ userInfo.bio || '这个人很懒，什么都没有留下...' }}</text>\n          </view>\n          <view class=\"action-buttons\">\n            <view\n              class=\"custom-btn follow-btn\"\n              :class=\"{ 'followed': userInfo.isFollowed }\"\n              @click=\"toggleFollow\"\n            >{{ userInfo.isFollowed ? '已关注' : '关注' }}</view>\n            <view class=\"custom-btn chat-btn\" @click=\"goToChat\">私信</view>\n          </view>\n        </view>\n\n        <!-- 数据统计 -->\n        <view class=\"stats-section\">\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.postCount }}</text>\n            <text class=\"stat-label\">帖子</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.followingCount }}</text>\n            <text class=\"stat-label\">关注</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.followersCount }}</text>\n            <text class=\"stat-label\">粉丝</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userInfo.likeCount }}</text>\n            <text class=\"stat-label\">获赞</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 用户帖子 -->\n      <view class=\"posts-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">TA的帖子</text>\n        </view>\n\n        <view class=\"post-grid\">\n          <PostCard\n            v-for=\"post in userPosts\"\n            :key=\"post.id\"\n            :post=\"post\"\n            class=\"post-card-item\"\n            @click=\"goPostDetail\"\n            @user-click=\"goUserProfile\"\n            @like=\"onPostLike\"\n          />\n        </view>\n\n        <!-- 空状态 -->\n        <view v-if=\"!userPosts.length\" class=\"empty-state\">\n          <u-icon name=\"file-text\" color=\"#ccc\" size=\"60\"></u-icon>\n          <text class=\"empty-text\">暂无帖子</text>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport PostCard from \"../components/PostCard.vue\";\nimport {\n  getUserProfile,\n  getUserPosts,\n  followUser,\n  unfollowUser,\n  checkFollowStatus,\n  likePost,\n  unlikePost\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"UserProfile\",\n  components: {\n    PostCard\n  },\n  data() {\n    return {\n      userId: \"\",\n      userInfo: {\n        userId: \"\",\n        nickname: \"\",\n        avatar: \"\",\n        bio: \"\",\n        postCount: 0,\n        followingCount: 0,\n        followersCount: 0,\n        likeCount: 0,\n        isFollowed: false\n      },\n      userPosts: [],\n      loading: true\n    };\n  },\n  onLoad(options) {\n    // 兼容不同的参数名：id 或 userId\n    this.userId = options.id || options.userId;\n    console.log(\"用户资料页面 - 接收到的参数:\", options);\n    console.log(\"用户资料页面 - 用户ID:\", this.userId);\n\n    if (!this.userId) {\n      uni.showToast({\n        title: \"用户ID不能为空\",\n        icon: \"none\"\n      });\n      return;\n    }\n\n    this.loadUserInfo();\n    this.loadUserPosts();\n  },\n  methods: {\n    async loadUserInfo() {\n      try {\n        // 加载用户资料\n        const userProfile = await getUserProfile(this.userId);\n        console.log(\"userProfile:\", userProfile);\n        if (userProfile) {\n          this.userInfo = {\n            id: userProfile.data.userId,\n            userId: userProfile.data.userId,\n            nickname: userProfile.data.nickname || \"无名氏\",\n            avatar:\n              \"https://file.foxdance.com.cn\" +\n              userProfile.data.avatar +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            bio: userProfile.data.bio || \"这个人很懒，什么都没有留下...\",\n            danceType: userProfile.data.danceType || \"街舞\",\n            postCount: userProfile.data.postCount || 0,\n            followingCount: userProfile.data.followingCount || 0,\n            followersCount: userProfile.data.followerCount || 0,\n            likeCount: userProfile.data.likeReceivedCount || 0,\n            isFollowed: userProfile.data.isFollowed || false\n          };\n        }\n\n        // 检查关注状态\n        const followStatus = await checkFollowStatus(this.userId);\n        console.log(\"关注状态检查结果:\", followStatus);\n\n        if (followStatus && followStatus.code === 0 && followStatus.data) {\n          this.userInfo.isFollowed = followStatus.data.isFollowing || false;\n          // 同时更新粉丝数和关注数\n          if (followStatus.data.followerCount !== undefined) {\n            this.userInfo.followersCount = followStatus.data.followerCount;\n          }\n          if (followStatus.data.followingCount !== undefined) {\n            this.userInfo.followingCount = followStatus.data.followingCount;\n          }\n        } else {\n          this.userInfo.isFollowed = false;\n        }\n\n        this.loading = false;\n      } catch (error) {\n        console.error(\"加载用户信息失败:\", error);\n        uni.showToast({\n          title: \"用户信息加载失败\",\n          icon: \"none\"\n        });\n        // 加载失败，返回上一页\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }\n    },\n\n    async loadUserPosts() {\n      console.log(\"开始加载用户帖子，用户ID:\", this.userId);\n      try {\n        // 获取当前登录用户ID\n        const currentUserId = uni.getStorageSync(\"userid\");\n\n        // 使用专门的getUserPosts API\n        const result = await getUserPosts(this.userId, {\n          current: 1,\n          pageSize: 20,\n          currentUserId: currentUserId || undefined\n        });\n\n        console.log(\"用户帖子API返回结果:\", result);\n\n        // 检查API返回格式：{code: 0, data: {records: [...], total: ...}}\n        if (result && result.code === 0 && result.data && result.data.records) {\n          this.userPosts = result.data.records.map(post => ({\n            id: post.id,\n            title: post.title || \"\",\n            coverImage: post.coverImage,\n            username: post.nickname || this.userInfo.nickname,\n            userAvatar: \"https://file.foxdance.com.cn\" + post.avatar + 'imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85',\n            content: post.content,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            isLiked: post.isLiked || false,\n            createTime: new Date(post.createTime)\n          }));\n          console.log(\"用户帖子加载成功，数量:\", this.userPosts.length);\n        } else {\n          console.log(\"没有找到用户帖子数据\");\n          this.userPosts = [];\n        }\n      } catch (error) {\n        console.error(\"加载用户帖子失败:\", error);\n        uni.showToast({\n          title: \"帖子加载失败\",\n          icon: \"none\"\n        });\n        this.userPosts = [];\n      }\n    },\n\n    async toggleFollow() {\n      try {\n        if (this.userInfo.isFollowed) {\n          // 取消关注\n          await unfollowUser(this.userId);\n          this.userInfo.isFollowed = false;\n          this.userInfo.followersCount = Math.max(\n            0,\n            this.userInfo.followersCount - 1\n          );\n          uni.showToast({\n            title: \"取消关注\",\n            icon: \"none\"\n          });\n        } else {\n          // 关注用户\n          await followUser(this.userId);\n          this.userInfo.isFollowed = true;\n          this.userInfo.followersCount += 1;\n          uni.showToast({\n            title: \"关注成功\",\n            icon: \"success\"\n          });\n        }\n      } catch (error) {\n        console.error(\"关注操作失败:\", error);\n        uni.showToast({\n          title: \"操作失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    async onPostLike(post) {\n      try {\n        if (post.isLiked) {\n          // 取消点赞\n          await unlikePost(post.id);\n          post.isLiked = false;\n          post.likeCount = Math.max(0, post.likeCount - 1);\n        } else {\n          // 点赞\n          await likePost(post.id);\n          post.isLiked = true;\n          post.likeCount += 1;\n        }\n\n        // 更新帖子列表中的数据\n        const index = this.userPosts.findIndex(p => p.id === post.id);\n        if (index !== -1) {\n          this.$set(this.userPosts, index, { ...post });\n        }\n      } catch (error) {\n        console.error(\"点赞操作失败:\", error);\n        uni.showToast({\n          title: \"操作失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goToChat() {\n      if (!this.userInfo.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\"跳转到聊天页面，目标用户ID:\", this.userInfo.userId);\n\n      // 跳转到聊天详情页面，传递目标用户ID和用户信息\n      uni.navigateTo({\n        url: `/pagesSub/social/chat/detail?userId=${\n          this.userInfo.userId\n        }&nickname=${encodeURIComponent(\n          this.userInfo.nickname\n        )}&avatar=${encodeURIComponent(this.userInfo.avatar)}`\n      });\n    },\n\n    goUserProfile(post) {\n      // 如果是当前用户，不需要跳转\n      if (post.userId === this.userId) return;\n\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`\n      });\n    },\n\n    goBack() {\n      uni.navigateBack();\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: [\"举报用户\", \"拉黑用户\"],\n        success: res => {\n          console.log(\"更多操作:\", res.tapIndex);\n        }\n      });\n    },\n    // 分享给好友\n    onShareAppMessage() {\n      return {\n        title: \"用户资料\",\n        path: \"/pagesSub/social/user/profile\"\n      };\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-profile-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content {\n  padding: 16px;\n  width: auto;\n}\n\n.user-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.user-info {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20px;\n}\n\n.user-details {\n  flex: 1;\n  margin-left: 16px;\n  margin-right: 12px;\n}\n\n/* 按钮组样式 */\n.action-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  align-items: flex-end;\n  min-width: 80px;\n}\n\n/* 自定义按钮样式 */\n.custom-btn {\n  min-width: 80px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 1px solid transparent;\n}\n\n.follow-btn {\n  background: #2979ff;\n  color: #fff;\n  border-color: #2979ff;\n}\n\n.follow-btn.followed {\n  background: #f8f9fa;\n  color: #666;\n  border-color: #e4e7ed;\n}\n\n.chat-btn {\n  background: #f8f9fa;\n  color: #666;\n  border-color: #e4e7ed;\n}\n\n.custom-btn:active {\n  transform: scale(0.95);\n  opacity: 0.8;\n}\n\n.nickname {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.user-id {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.bio {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n  display: block;\n}\n\n.stats-section {\n  display: flex;\n  justify-content: space-around;\n  padding-top: 16px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.posts-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.section-header {\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.post-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.post-card-item {\n  width: calc(50% - 4px);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n  margin-top: 12px;\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #999;\n  margin-top: 12px;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=9e24a6bc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&id=9e24a6bc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751619927\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}