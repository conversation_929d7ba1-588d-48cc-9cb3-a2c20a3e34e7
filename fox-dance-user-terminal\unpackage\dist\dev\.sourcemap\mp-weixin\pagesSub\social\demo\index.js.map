{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/demo/index.vue?7750", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/demo/index.vue?00a5", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/demo/index.vue?6408", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/demo/index.vue?c4e8", "uni-app:///pagesSub/social/demo/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/demo/index.vue?5a52", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/demo/index.vue?035f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "methods", "goPage", "uni", "url", "fail", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgKlwB;EACAC;EACAC;IACAC;MAAA;MACAC;QACAC;QACAC;UACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7KA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/demo/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/demo/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=ed92b0ba&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=ed92b0ba&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ed92b0ba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/demo/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=ed92b0ba&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"demo-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"title\">帖子分享App演示</text>\n      </view>\n    </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 功能介绍 -->\n      <view class=\"intro-section\">\n        <text class=\"intro-title\">🎉 帖子分享社交App</text>\n        <text class=\"intro-desc\">\n          一个现代化的社交分享平台，支持帖子发布、图片分享、实时聊天等功能\n        </text>\n      </view>\n\n      <!-- 功能列表 -->\n      <view class=\"feature-section\">\n        <text class=\"section-title\">✨ 核心功能</text>\n        \n        <view class=\"feature-grid\">\n          <view class=\"feature-item\" @click=\"goPage('/pagesSub/social/home/<USER>')\">\n            <view class=\"feature-icon home\">\n              <u-icon name=\"home\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"feature-name\">帖子卡片流</text>\n            <text class=\"feature-desc\">网格布局，瀑布流展示</text>\n          </view>\n\n          <view class=\"feature-item\" @click=\"goPage('/pagesSub/social/discover/index')\">\n            <view class=\"feature-icon discover\">\n              <u-icon name=\"search\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"feature-name\">发现页面</text>\n            <text class=\"feature-desc\">热门话题和推荐</text>\n          </view>\n\n          <view class=\"feature-item\" @click=\"goPage('/pagesSub/social/publish/index')\">\n            <view class=\"feature-icon publish\">\n              <u-icon name=\"plus\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"feature-name\">发布帖子</text>\n            <text class=\"feature-desc\">分享生活精彩瞬间</text>\n          </view>\n\n          <view class=\"feature-item\" @click=\"goPage('/pagesSub/social/message/index')\">\n            <view class=\"feature-icon message\">\n              <u-icon name=\"chat\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"feature-name\">消息聊天</text>\n            <text class=\"feature-desc\">与朋友实时交流</text>\n          </view>\n\n          <view class=\"feature-item\" @click=\"goPage('/pagesSub/social/profile/index')\">\n            <view class=\"feature-icon profile\">\n              <u-icon name=\"account\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"feature-name\">个人中心</text>\n            <text class=\"feature-desc\">管理个人资料</text>\n          </view>\n\n          <view class=\"feature-item\" @click=\"goPage('/pagesSub/social/post/detail?id=1')\">\n            <view class=\"feature-icon detail\">\n              <u-icon name=\"file-text\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"feature-name\">帖子详情</text>\n            <text class=\"feature-desc\">查看帖子和评论</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 技术特性 -->\n      <view class=\"tech-section\">\n        <text class=\"section-title\">🛠 技术特性</text>\n        \n        <view class=\"tech-list\">\n          <view class=\"tech-item\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"tech-text\">基于uni-app + Vue2开发</text>\n          </view>\n          <view class=\"tech-item\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"tech-text\">使用uview-ui组件库</text>\n          </view>\n          <view class=\"tech-item\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"tech-text\">支持微信小程序平台</text>\n          </view>\n          <view class=\"tech-item\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"tech-text\">现代化卡片式设计</text>\n          </view>\n          <view class=\"tech-item\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"tech-text\">组件化开发模式</text>\n          </view>\n          <view class=\"tech-item\">\n            <u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"16\"></u-icon>\n            <text class=\"tech-text\">响应式布局适配</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 设计亮点 -->\n      <view class=\"design-section\">\n        <text class=\"section-title\">🎨 设计亮点</text>\n        \n        <view class=\"design-grid\">\n          <view class=\"design-item\">\n            <text class=\"design-title\">简约现代</text>\n            <text class=\"design-desc\">采用简洁的设计语言，注重用户体验</text>\n          </view>\n          <view class=\"design-item\">\n            <text class=\"design-title\">色彩统一</text>\n            <text class=\"design-desc\">统一的色彩系统，保持视觉一致性</text>\n          </view>\n          <view class=\"design-item\">\n            <text class=\"design-title\">交互流畅</text>\n            <text class=\"design-desc\">流畅的动画效果，提升操作体验</text>\n          </view>\n          <view class=\"design-item\">\n            <text class=\"design-title\">适配完善</text>\n            <text class=\"design-desc\">完美适配不同尺寸的移动设备</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 开始体验 -->\n      <view class=\"start-section\">\n        <text class=\"start-title\">🚀 开始体验</text>\n        <text class=\"start-desc\">点击上方功能卡片，体验完整的社交分享功能</text>\n        \n        <view class=\"start-buttons\">\n          <u-button\n            type=\"primary\"\n            size=\"large\"\n            text=\"进入首页\"\n            @click=\"goPage('/pagesSub/social/home/<USER>')\"\n          ></u-button>\n          <u-button\n            type=\"default\"\n            size=\"large\"\n            text=\"发布帖子\"\n            @click=\"goPage('/pagesSub/social/publish/index')\"\n          ></u-button>\n          <u-button\n            type=\"warning\"\n            size=\"large\"\n            text=\"组件测试\"\n            @click=\"goPage('/pagesSub/social/test/index')\"\n          ></u-button>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'SocialDemo',\n  methods: {\n    goPage(url) {\n      uni.navigateTo({\n        url: url,\n        fail: (err) => {\n          console.error('页面跳转失败:', err)\n          this.$u.toast('页面跳转失败')\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.demo-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.header {\n  padding-top: var(--status-bar-height);\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #fff;\n}\n\n.content {\n  margin-top: calc(44px + var(--status-bar-height));\n  padding: 20px 16px;\n}\n\n.intro-section {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.intro-title {\n  font-size: 24px;\n  font-weight: 700;\n  color: #fff;\n  display: block;\n  margin-bottom: 12px;\n}\n\n.intro-desc {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.5;\n  display: block;\n}\n\n.feature-section, .tech-section, .design-section, .start-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 20px;\n  backdrop-filter: blur(10px);\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 16px;\n}\n\n.feature-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.feature-item {\n  width: calc(50% - 8px);\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 12px;\n}\n\n.feature-icon.home {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.feature-icon.discover {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.feature-icon.publish {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.feature-icon.message {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.feature-icon.profile {\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\n}\n\n.feature-icon.detail {\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n}\n\n.feature-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.feature-desc {\n  font-size: 12px;\n  color: #666;\n  display: block;\n}\n\n.tech-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.tech-item {\n  display: flex;\n  align-items: center;\n}\n\n.tech-text {\n  margin-left: 8px;\n  font-size: 14px;\n  color: #333;\n}\n\n.design-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.design-item {\n  width: calc(50% - 8px);\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.design-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.design-desc {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.4;\n  display: block;\n}\n\n.start-section {\n  text-align: center;\n}\n\n.start-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.start-desc {\n  font-size: 14px;\n  color: #666;\n  display: block;\n  margin-bottom: 20px;\n}\n\n.start-buttons {\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=ed92b0ba&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=ed92b0ba&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751619888\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}