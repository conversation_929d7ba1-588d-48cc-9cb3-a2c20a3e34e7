{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/UserCard.vue?f6ed", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/UserCard.vue?a69e", "uni-app:///pagesSub/social/components/UserCard.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/UserCard.vue?f2ba", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/UserCard.vue?4848", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/UserCard.vue?5a32", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/UserCard.vue?f1a6"], "names": ["name", "components", "FollowButton", "props", "user", "type", "required", "methods", "goUserProfile", "uni", "url", "onFollowChange", "formatNumber"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAivB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC2DrwB;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAo6C,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAx7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "pagesSub/social/components/UserCard.js", "sourcesContent": ["var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatNumber(_vm.user.postCount || 0)\n  var m1 = _vm.formatNumber(_vm.user.followerCount || 0)\n  var m2 = _vm.formatNumber(_vm.user.followingCount || 0)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./UserCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./UserCard.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"user-card\" @click=\"goUserProfile\">\n    <!-- 用户头像 -->\n    <view class=\"avatar-container\">\n      <u-avatar \n        :src=\"user.avatar\" \n        size=\"80\"\n      ></u-avatar>\n      \n      <!-- 认证标识 -->\n      <view v-if=\"user.isVerified\" class=\"verified-badge\">\n        <u-icon name=\"checkmark-circle-fill\" color=\"#ff6b87\" size=\"16\"></u-icon>\n      </view>\n    </view>\n\n    <!-- 用户信息 -->\n    <view class=\"user-info\">\n      <view class=\"user-name\">\n        <text class=\"nickname\">{{ user.nickname || user.username || '用户' }}</text>\n        <text v-if=\"user.username && user.nickname\" class=\"username\">@{{ user.username }}</text>\n      </view>\n      \n      <view v-if=\"user.bio\" class=\"user-bio\">\n        <text class=\"bio-text\">{{ user.bio }}</text>\n      </view>\n      \n      <!-- 用户统计 -->\n      <view class=\"user-stats\">\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ formatNumber(user.postCount || 0) }}</text>\n          <text class=\"stat-label\">帖子</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ formatNumber(user.followerCount || 0) }}</text>\n          <text class=\"stat-label\">粉丝</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ formatNumber(user.followingCount || 0) }}</text>\n          <text class=\"stat-label\">关注</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 关注按钮 -->\n    <view class=\"action-container\">\n      <FollowButton\n        :user=\"user\"\n        :followed=\"user.isFollowed\"\n        size=\"mini\"\n        @click.stop\n        @follow-change=\"onFollowChange\"\n      />\n    </view>\n  </view>\n</template>\n\n<script>\nimport FollowButton from './FollowButton.vue';\n\nexport default {\n  name: \"UserCard\",\n  components: {\n    FollowButton\n  },\n  props: {\n    user: {\n      type: Object,\n      required: true\n    }\n  },\n  methods: {\n    // 跳转到用户主页\n    goUserProfile() {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?userId=${this.user.id || this.user.userId}`\n      });\n    },\n\n    // 关注状态变化回调\n    onFollowChange(isFollowed) {\n      // 更新用户的关注状态\n      this.user.isFollowed = isFollowed;\n\n      // 更新粉丝数\n      if (isFollowed) {\n        this.user.followerCount = (this.user.followerCount || 0) + 1;\n      } else {\n        this.user.followerCount = Math.max((this.user.followerCount || 0) - 1, 0);\n      }\n    },\n\n    // 格式化数字\n    formatNumber(num) {\n      if (num >= 10000) {\n        return (num / 10000).toFixed(1) + 'w';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'k';\n      }\n      return num.toString();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.user-card {\n  display: flex;\n  align-items: center;\n  padding: 24rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  \n  &:active {\n    transform: scale(0.98);\n    background-color: #f8f9fa;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.avatar-container {\n  position: relative;\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.verified-badge {\n  position: absolute;\n  bottom: -4rpx;\n  right: -4rpx;\n  background-color: #fff;\n  border-radius: 50%;\n  padding: 2rpx;\n}\n\n.user-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.user-name {\n  margin-bottom: 8rpx;\n}\n\n.nickname {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-right: 8rpx;\n}\n\n.username {\n  font-size: 28rpx;\n  color: #909399;\n}\n\n.user-bio {\n  margin-bottom: 12rpx;\n}\n\n.bio-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.user-stats {\n  display: flex;\n  gap: 24rpx;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #909399;\n  margin-top: 4rpx;\n}\n\n.action-container {\n  margin-left: 16rpx;\n  flex-shrink: 0;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./UserCard.vue?vue&type=style&index=0&id=a7e7df72&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./UserCard.vue?vue&type=style&index=0&id=a7e7df72&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760723484\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./UserCard.vue?vue&type=template&id=a7e7df72&scoped=true&\"\nvar renderjs\nimport script from \"./UserCard.vue?vue&type=script&lang=js&\"\nexport * from \"./UserCard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./UserCard.vue?vue&type=style&index=0&id=a7e7df72&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a7e7df72\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/components/UserCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./UserCard.vue?vue&type=template&id=a7e7df72&scoped=true&\""], "sourceRoot": ""}