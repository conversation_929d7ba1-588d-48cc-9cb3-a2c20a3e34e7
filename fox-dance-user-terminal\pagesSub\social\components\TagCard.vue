<template>
  <view class="tag-card" @click="goTagDetail">
    <!-- 话题图标 -->
    <view class="tag-icon">
      <view v-if="tag.coverImage" class="tag-cover">
        <image 
          :src="tag.coverImage" 
          class="cover-image" 
          mode="aspectFill"
          @error="onImageError"
        />
      </view>
      <view v-else class="default-icon">
        <u-icon name="hash" color="#ff6b87" size="32"></u-icon>
      </view>
    </view>

    <!-- 话题信息 -->
    <view class="tag-info">
      <view class="tag-name">
        <text class="name-text">#{{ tag.name || tag.tagName }}</text>
        
        <!-- 热门标识 -->
        <view v-if="tag.isHot" class="hot-badge">
          <u-icon name="fire" color="#ff6b87" size="14"></u-icon>
          <text class="hot-text">热门</text>
        </view>
      </view>
      
      <view v-if="tag.description" class="tag-description">
        <text class="desc-text">{{ tag.description }}</text>
      </view>
      
      <!-- 话题统计 -->
      <view class="tag-stats">
        <view class="stat-item">
          <u-icon name="edit-pen" color="#909399" size="14"></u-icon>
          <text class="stat-text">{{ formatNumber(tag.postCount || 0) }}篇帖子</text>
        </view>
        <view class="stat-item">
          <u-icon name="eye" color="#909399" size="14"></u-icon>
          <text class="stat-text">{{ formatNumber(tag.viewCount || 0) }}次浏览</text>
        </view>
      </view>
    </view>

    <!-- 关注按钮 -->
    <view class="action-container">
      <TagFollowButton
        :tag="tag"
        :followed="tag.isFollowed"
        size="mini"
        @click.stop
        @follow-change="onFollowChange"
      />
    </view>
  </view>
</template>

<script>
import TagFollowButton from './TagFollowButton.vue';

export default {
  name: "TagCard",
  components: {
    TagFollowButton
  },
  props: {
    tag: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      imageError: false
    };
  },
  methods: {
    // 跳转到话题详情页
    goTagDetail() {
      uni.navigateTo({
        url: `/pagesSub/social/tag/detail?tagId=${this.tag.id || this.tag.tagId}&tagName=${encodeURIComponent(this.tag.name || this.tag.tagName)}`
      });
    },

    // 关注状态变化回调
    onFollowChange(isFollowed) {
      this.tag.isFollowed = isFollowed;
      // 可以在这里更新话题的关注数等统计信息
      if (isFollowed) {
        this.tag.followCount = (this.tag.followCount || 0) + 1;
      } else {
        this.tag.followCount = Math.max((this.tag.followCount || 0) - 1, 0);
      }
    },

    // 图片加载失败
    onImageError() {
      this.imageError = true;
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
      }
      return num.toString();
    }
  }
};
</script>

<style lang="scss" scoped>
.tag-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.tag-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.tag-cover {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.default-icon {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b87, #ff8fa3);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-info {
  flex: 1;
  min-width: 0;
}

.tag-name {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 12rpx;
}

.hot-badge {
  display: flex;
  align-items: center;
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
}

.hot-text {
  font-size: 20rpx;
  color: #ff6b87;
  margin-left: 4rpx;
}

.tag-description {
  margin-bottom: 12rpx;
}

.desc-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.tag-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-text {
  font-size: 24rpx;
  color: #909399;
  margin-left: 6rpx;
}

.action-container {
  margin-left: 16rpx;
  flex-shrink: 0;
}
</style>
