{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-28T01:58:19.752Z", "updatedAt": "2025-07-28T01:58:19.765Z", "resourceCount": 8}, "resources": [{"id": "fox-development-workflow", "source": "project", "protocol": "execution", "name": "Fox Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fox-fullstack-developer/execution/fox-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-28T01:58:19.756Z", "updatedAt": "2025-07-28T01:58:19.756Z", "scannedAt": "2025-07-28T01:58:19.756Z", "path": "role/fox-fullstack-developer/execution/fox-development-workflow.execution.md"}}, {"id": "fox-fullstack-developer", "source": "project", "protocol": "role", "name": "Fox Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fox-fullstack-developer/fox-fullstack-developer.role.md", "metadata": {"createdAt": "2025-07-28T01:58:19.757Z", "updatedAt": "2025-07-28T01:58:19.757Z", "scannedAt": "2025-07-28T01:58:19.757Z", "path": "role/fox-fullstack-developer/fox-fullstack-developer.role.md"}}, {"id": "fullstack-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fullstack-developer/thought/fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-07-28T01:58:19.761Z", "updatedAt": "2025-07-28T01:58:19.762Z", "scannedAt": "2025-07-28T01:58:19.761Z", "path": "role/fullstack-developer/thought/fullstack-thinking.thought.md"}}, {"id": "fullstack-development", "source": "project", "protocol": "execution", "name": "Fullstack Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fullstack-developer/execution/fullstack-development.execution.md", "metadata": {"createdAt": "2025-07-28T01:58:19.759Z", "updatedAt": "2025-07-28T01:58:19.759Z", "scannedAt": "2025-07-28T01:58:19.759Z", "path": "role/fullstack-developer/execution/fullstack-development.execution.md"}}, {"id": "fullstack-developer", "source": "project", "protocol": "role", "name": "Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fullstack-developer/fullstack-developer.role.md", "metadata": {"createdAt": "2025-07-28T01:58:19.760Z", "updatedAt": "2025-07-28T01:58:19.760Z", "scannedAt": "2025-07-28T01:58:19.760Z", "path": "role/fullstack-developer/fullstack-developer.role.md"}}, {"id": "project-analysis-workflow", "source": "project", "protocol": "execution", "name": "Project Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/project-analyst/execution/project-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-07-28T01:58:19.763Z", "updatedAt": "2025-07-28T01:58:19.763Z", "scannedAt": "2025-07-28T01:58:19.763Z", "path": "role/project-analyst/execution/project-analysis-workflow.execution.md"}}, {"id": "project-analyst", "source": "project", "protocol": "role", "name": "Project Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/project-analyst/project-analyst.role.md", "metadata": {"createdAt": "2025-07-28T01:58:19.764Z", "updatedAt": "2025-07-28T01:58:19.764Z", "scannedAt": "2025-07-28T01:58:19.764Z", "path": "role/project-analyst/project-analyst.role.md"}}, {"id": "systematic-analysis", "source": "project", "protocol": "thought", "name": "Systematic Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/project-analyst/thought/systematic-analysis.thought.md", "metadata": {"createdAt": "2025-07-28T01:58:19.765Z", "updatedAt": "2025-07-28T01:58:19.765Z", "scannedAt": "2025-07-28T01:58:19.765Z", "path": "role/project-analyst/thought/systematic-analysis.thought.md"}}], "stats": {"totalResources": 8, "byProtocol": {"execution": 3, "role": 3, "thought": 2}, "bySource": {"project": 8}}}