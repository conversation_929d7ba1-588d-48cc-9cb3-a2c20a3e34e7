{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/likes.vue?339d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/likes.vue?d66b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/likes.vue?690d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/likes.vue?ecc7", "uni-app:///pagesSub/social/message/likes.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/likes.vue?4ec7", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/likes.vue?8902"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "isRefreshing", "hasMore", "page", "pageSize", "messageList", "id", "type", "userId", "userName", "userAvatar", "postId", "postTitle", "postCover", "postType", "createTime", "isRead", "content", "onLoad", "methods", "getPostType", "video", "image", "text", "formatTime", "openMessageDetail", "message", "openPost", "uni", "url", "onRefresh", "loadMore", "loadMessages", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgElwB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAV;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAG;QACAF;QACAC;MACA,GACA;QACAV;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAV;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAG;QACAF;QACAC;MACA;IAEA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACAC;;MAEA;MACA;IACA;IAEAC;MACAC;QACAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBAAA;gBAAA;cAAA;gBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzMA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/message/likes.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/message/likes.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./likes.vue?vue&type=template&id=541b87c7&scoped=true&\"\nvar renderjs\nimport script from \"./likes.vue?vue&type=script&lang=js&\"\nexport * from \"./likes.vue?vue&type=script&lang=js&\"\nimport style0 from \"./likes.vue?vue&type=style&index=0&id=541b87c7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"541b87c7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/likes.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./likes.vue?vue&type=template&id=541b87c7&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.messageList && _vm.messageList.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.messageList, function (message, __i0__) {\n        var $orig = _vm.__get_orig(message)\n        var m0 = _vm.getPostType(message.postType)\n        var m1 = _vm.formatTime(message.createTime)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g1 = _vm.hasMore && _vm.messageList && _vm.messageList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./likes.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./likes.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"likes-container\">\n    <!-- 消息列表 -->\n    <scroll-view \n      class=\"message-list\"\n      scroll-y\n      refresher-enabled\n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMore\"\n    >\n      <view v-if=\"messageList && messageList.length > 0\">\n        <view \n          v-for=\"message in messageList\" \n          :key=\"message.id\"\n          class=\"message-card\"\n          @click=\"openMessageDetail(message)\"\n        >\n          <!-- 用户头像 -->\n          <u-avatar \n            :src=\"message.userAvatar\" \n            size=\"40\"\n            class=\"user-avatar\"\n          ></u-avatar>\n          \n          <!-- 消息内容 -->\n          <view class=\"message-content\">\n            <view class=\"message-header\">\n              <text class=\"user-name\">{{ message.userName }}</text>\n              <text class=\"action-type\">{{ message.type === 'like' ? '赞了你的' : '评论了你的' }}</text>\n              <text class=\"post-type\">{{ getPostType(message.postType) }}</text>\n              <text class=\"message-time\">{{ formatTime(message.createTime) }}</text>\n            </view>\n            \n            <!-- 评论内容 -->\n            <text v-if=\"message.type === 'comment'\" class=\"comment-content\">{{ message.content }}</text>\n            \n            <!-- 相关帖子 -->\n            <view class=\"related-post\" @click.stop=\"openPost(message.postId)\">\n              <image v-if=\"message.postCover\" :src=\"message.postCover\" class=\"post-cover\" mode=\"aspectFill\"></image>\n              <text class=\"post-title\">{{ message.postTitle }}</text>\n            </view>\n          </view>\n          \n          <!-- 未读标识 -->\n          <view v-if=\"!message.isRead\" class=\"unread-dot\"></view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-else class=\"empty-state\">\n        <u-empty mode=\"data\" text=\"暂无赞和评论\"></u-empty>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"hasMore && messageList && messageList.length > 0\" class=\"load-more\">\n        <u-loading mode=\"flower\" size=\"24\"></u-loading>\n        <text class=\"load-text\">加载中...</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'LikesAndComments',\n  data() {\n    return {\n      isRefreshing: false,\n      hasMore: true,\n      page: 1,\n      pageSize: 20,\n      messageList: [\n        {\n          id: 1,\n          type: 'like',\n          userId: 'user001',\n          userName: '舞蹈小仙女',\n          userAvatar: 'https://picsum.photos/100/100?random=801',\n          postId: 'post001',\n          postTitle: '今天的舞蹈练习分享',\n          postCover: 'https://picsum.photos/200/200?random=901',\n          postType: 'video',\n          createTime: new Date(Date.now() - 1800000),\n          isRead: false\n        },\n        {\n          id: 2,\n          type: 'comment',\n          userId: 'user002',\n          userName: '街舞达人',\n          userAvatar: 'https://picsum.photos/100/100?random=802',\n          postId: 'post002',\n          postTitle: '新学的Breaking动作',\n          postCover: 'https://picsum.photos/200/200?random=902',\n          postType: 'video',\n          content: '这个动作太帅了！能教教我吗？',\n          createTime: new Date(Date.now() - 3600000),\n          isRead: true\n        },\n        {\n          id: 3,\n          type: 'like',\n          userId: 'user003',\n          userName: '芭蕾公主',\n          userAvatar: 'https://picsum.photos/100/100?random=803',\n          postId: 'post003',\n          postTitle: '芭蕾基本功练习心得',\n          postCover: null,\n          postType: 'text',\n          createTime: new Date(Date.now() - 7200000),\n          isRead: false\n        },\n        {\n          id: 4,\n          type: 'comment',\n          userId: 'user004',\n          userName: '现代舞爱好者',\n          userAvatar: 'https://picsum.photos/100/100?random=804',\n          postId: 'post004',\n          postTitle: '舞蹈服装搭配分享',\n          postCover: 'https://picsum.photos/200/200?random=904',\n          postType: 'image',\n          content: '这套服装真的很适合现代舞，在哪里买的？',\n          createTime: new Date(Date.now() - 10800000),\n          isRead: true\n        }\n      ]\n    }\n  },\n  onLoad() {\n    this.loadMessages()\n  },\n  methods: {\n    getPostType(type) {\n      const typeMap = {\n        video: '视频',\n        image: '图片',\n        text: '帖子'\n      }\n      return typeMap[type] || '帖子'\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - time\n      const minutes = Math.floor(diff / 60000)\n      const hours = Math.floor(diff / 3600000)\n      const days = Math.floor(diff / 86400000)\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else {\n        return `${days}天前`\n      }\n    },\n\n    openMessageDetail(message) {\n      // 标记为已读\n      message.isRead = true\n      \n      // 跳转到相关帖子\n      this.openPost(message.postId)\n    },\n\n    openPost(postId) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${postId}`\n      })\n    },\n\n    onRefresh() {\n      this.isRefreshing = true\n      this.page = 1\n      this.loadMessages().finally(() => {\n        this.isRefreshing = false\n      })\n    },\n\n    loadMore() {\n      if (!this.hasMore) return\n      this.page++\n      this.loadMessages()\n    },\n\n    async loadMessages() {\n      try {\n        // 模拟API请求\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        if (this.page >= 3) {\n          this.hasMore = false\n        }\n      } catch (error) {\n        console.error('加载消息失败:', error)\n        this.$u.toast('加载失败，请重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.likes-container {\n  //height: 90vh;\n  background: #f5f5f5;\n}\n\n.message-list {\n  height: 100%;\n  padding: 32rpx 0;\n}\n\n.message-card {\n  display: flex;\n  align-items: flex-start;\n  padding: 32rpx;\n  margin: 0 32rpx 24rpx;\n  background: #fff;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.user-avatar {\n  margin-right: 24rpx;\n  flex-shrink: 0;\n}\n\n.message-content {\n  flex: 1;\n}\n\n.message-header {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: 16rpx;\n}\n\n.user-name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-right: 8rpx;\n}\n\n.action-type {\n  font-size: 28rpx;\n  color: #666;\n  margin-right: 8rpx;\n}\n\n.post-type {\n  font-size: 28rpx;\n  color: #2979ff;\n  margin-right: 16rpx;\n}\n\n.message-time {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: auto;\n}\n\n.comment-content {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.5;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.related-post {\n  display: flex;\n  align-items: center;\n  padding: 16rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  border: 2rpx solid #e4e7ed;\n}\n\n.post-cover {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 12rpx;\n  margin-right: 16rpx;\n  flex-shrink: 0;\n}\n\n.post-title {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n  flex: 1;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  line-clamp: 2;\n  overflow: hidden;\n}\n\n.unread-dot {\n  position: absolute;\n  top: 32rpx;\n  right: 32rpx;\n  width: 16rpx;\n  height: 16rpx;\n  background: #ff4757;\n  border-radius: 50%;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  padding: 120rpx 32rpx;\n}\n\n.load-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 32rpx;\n}\n\n.load-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 16rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./likes.vue?vue&type=style&index=0&id=541b87c7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./likes.vue?vue&type=style&index=0&id=541b87c7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760721004\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}