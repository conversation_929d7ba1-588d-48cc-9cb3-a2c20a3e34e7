{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?2686", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?53ed", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?f753", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?f573", "uni-app:///pagesSub/social/message/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?d244", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/message/index.vue?a1db", "uni-app:///main.js"], "names": ["name", "data", "chatList", "loading", "refreshing", "systemUnreadCount", "likeUnreadCount", "followUnreadCount", "onLoad", "onShow", "console", "onPullDownRefresh", "activated", "methods", "loadUnreadCounts", "loadChatList", "currentUserId", "current", "size", "result", "conversations", "Array", "id", "userId", "conversation", "avatar", "lastMessage", "lastMessageTime", "lastMessageType", "unreadCount", "isOnline", "isMuted", "uni", "title", "icon", "formatAvatarUrl", "getMessageTypeString", "formatTime", "onRefresh", "openChat", "chat", "senderId", "receiverId", "index", "url", "encodeURIComponent", "showChatActions", "itemList", "success", "toggleChatTop", "toggleChatMute", "deleteChat", "content", "goSearch", "startNewChat", "goSystemMessages", "goLikeMessages", "goFollowMessages", "forceRefresh", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsGlwB;AAIA;AAAA;AAAA,eAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EAEAC;IACA;IACA;MACAC;MACA;MACA;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEA;EACAC;IACA;MACAF;MACA;MACA;IACA;EACA;EACAG;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAJ;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;gBACA;gBAAA;gBAGA;gBACAM;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAN;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACAO;kBACAC;gBACA;cAAA;gBAHAC;gBAKAT;;gBAEA;gBACAU;gBACA,IACAD,UACAA,qBACAA,eACAE,4BACA;kBACAD;gBACA;kBACAA;gBACA;kBACAV;kBACAU;gBACA;gBAEA;kBACA;oBAAA;sBACAE;sBACAC;sBACAvB,MACAwB,kCACAA,yBACA;sBACAC,+BACAD,oDACA;sBACAE;sBACAC,gDACA,yCACA;sBACAC,6CACAJ,kCACA;sBACAK;sBACAC;sBACAC;oBACA;kBAAA;kBACArB;kBACAA;gBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAsB;kBACAC;kBACAC;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;MAEA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACAN;MACA;IACA;IAEAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA7B;gBAAA;gBAAA,MAIA8B;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;gBACA;cAAA;gBAEA;gBACAF;;gBAEA;gBACAG;kBAAA;gBAAA;gBACA;kBACA;gBACA;cAAA;gBAGA;gBACAX;kBACAY,mDACAJ,kCACAK,mBACAL,UACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9B;gBACA;gBACAsB;kBACAY,mDACAJ,kCACAK,mBACAL,UACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAM;MAAA;MACA,eACA,MACAN,gCACA,OACA;MAEAR;QACAe;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACAV;MACA;IACA;IAEAW;MAAA;MACAnB;QACAC;QACAmB;QACAJ;UACA;YACA;cAAA;YAAA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAK;MACArB;QACAY;MACA;IACA;IAEAU;MACAtB;QACAY;MACA;IACA;IAEAW;MACAvB;QACAY;MACA;IACA;IAEAY;MACAxB;QACAY;MACA;IACA;IAEAa;MACAzB;QACAY;MACA;IACA;IAEA;IACAc;MACAhD;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxaA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAiD,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/message/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2caef1dd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/message/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2caef1dd&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.chatList, function (chat, __i0__) {\n    var $orig = _vm.__get_orig(chat)\n    var m0 = _vm.formatTime(chat.lastMessageTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.chatList.length && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"message-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"title\">消息</text>\n        <view class=\"header-actions\">\n          <u-icon name=\"search\" size=\"24\" color=\"#333\" @click=\"goSearch\"></u-icon>\n          <u-icon name=\"plus\" size=\"24\" color=\"#333\" @click=\"startNewChat\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 页面内容区域 -->\n    <view class=\"page-content\">\n      <!-- 功能入口 -->\n      <view class=\"quick-actions\">\n        <view class=\"action-item\" @click=\"goSystemMessages\">\n          <view class=\"action-icon system\">\n            <u-icon name=\"bell\" color=\"#fff\" size=\"20\"></u-icon>\n          </view>\n          <text class=\"action-text\">系统消息</text>\n          <view v-if=\"systemUnreadCount\" class=\"unread-badge\">{{ systemUnreadCount }}</view>\n        </view>\n\n        <view class=\"action-item\" @click=\"goLikeMessages\">\n          <view class=\"action-icon like\">\n            <u-icon name=\"heart\" color=\"#fff\" size=\"20\"></u-icon>\n          </view>\n          <text class=\"action-text\">赞和评论</text>\n          <view v-if=\"likeUnreadCount\" class=\"unread-badge\">{{ likeUnreadCount }}</view>\n        </view>\n\n        <view class=\"action-item\" @click=\"goFollowMessages\">\n          <view class=\"action-icon follow\">\n            <u-icon name=\"account-fill\" color=\"#fff\" size=\"20\"></u-icon>\n          </view>\n          <text class=\"action-text\">新粉丝</text>\n          <view v-if=\"followUnreadCount\" class=\"unread-badge\">{{ followUnreadCount }}</view>\n        </view>\n      </view>\n\n      <!-- 聊天列表容器 -->\n      <view class=\"chat-list\">\n        <!-- 聊天列表 -->\n        <view\n          v-for=\"chat in chatList\"\n          :key=\"chat.id\"\n          class=\"chat-item\"\n          @click=\"openChat(chat)\"\n          @longpress=\"showChatActions(chat)\"\n        >\n          <view class=\"chat-avatar\">\n            <u-avatar :src=\"chat.avatar\" size=\"50\"></u-avatar>\n            <view v-if=\"chat.isOnline\" class=\"online-dot\"></view>\n          </view>\n\n          <view class=\"chat-content\">\n            <view class=\"chat-header\">\n              <text class=\"chat-name\">{{ chat.name }}</text>\n              <text class=\"chat-time\">{{ formatTime(chat.lastMessageTime) }}</text>\n            </view>\n\n            <view class=\"chat-preview\">\n              <view class=\"message-preview\">\n                <text\n                  v-if=\"chat.lastMessageType === 'text'\"\n                  class=\"preview-text\"\n                >{{ chat.lastMessage }}</text>\n                <text v-else-if=\"chat.lastMessageType === 'image'\" class=\"preview-text\">[图片]</text>\n                <text v-else-if=\"chat.lastMessageType === 'voice'\" class=\"preview-text\">[语音]</text>\n                <text v-else class=\"preview-text\">{{ chat.lastMessage }}</text>\n              </view>\n\n              <view class=\"chat-status\">\n                <view\n                  v-if=\"chat.unreadCount\"\n                  class=\"unread-count\"\n                >{{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}</view>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 空状态 -->\n        <view v-if=\"!chatList.length && !loading\" class=\"empty-state\">\n          <u-icon name=\"chat\" color=\"#ccc\" size=\"60\"></u-icon>\n          <text class=\"empty-text\">暂无消息</text>\n          <text class=\"empty-desc\">开始与朋友聊天吧</text>\n        </view>\n\n        <!-- 加载状态 -->\n        <view v-if=\"loading\" class=\"loading-state\">\n          <u-loading mode=\"circle\" size=\"24\"></u-loading>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport {\n  getConversations,\n  getMessageUnreadCount,\n  markMessageAsRead\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialMessage\",\n  data() {\n    return {\n      chatList: [],\n      loading: false,\n      refreshing: false,\n      systemUnreadCount: 0,\n      likeUnreadCount: 0,\n      followUnreadCount: 0\n    };\n  },\n  onLoad() {\n    this.loadUnreadCounts();\n    this.loadChatList();\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log(\"消息页显示时重新加载数据\");\n      this.loadUnreadCounts();\n      this.loadChatList();\n    }\n  },\n\n  // 页面下拉刷新\n  onPullDownRefresh() {\n    this.onRefresh();\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.chatList || this.chatList.length === 0) {\n      console.log(\"消息页激活时重新加载数据\");\n      this.loadUnreadCounts();\n      this.loadChatList();\n    }\n  },\n  methods: {\n    // 加载未读消息统计\n    async loadUnreadCounts() {\n      try {\n        // 暂时将所有徽标都设为0，不显示假数据\n        this.systemUnreadCount = 0;\n        this.likeUnreadCount = 0;\n        this.followUnreadCount = 0;\n\n        // 如果需要真实数据，可以取消注释下面的代码\n        // const unreadData = await getMessageUnreadCount();\n        // if (unreadData && unreadData.code === 0 && unreadData.data) {\n        //   this.systemUnreadCount = unreadData.data.total || 0;\n        // }\n      } catch (error) {\n        console.error(\"加载未读消息统计失败:\", error);\n        // 使用默认值\n      }\n    },\n\n    async loadChatList() {\n      console.log(\"开始加载聊天列表...\");\n      this.loading = true;\n\n      try {\n        // 检查用户登录状态\n        const currentUserId = uni.getStorageSync(\"userid\");\n        if (!currentUserId) {\n          console.error(\"用户未登录，无法加载聊天列表\");\n          this.chatList = [];\n          return;\n        }\n\n        const result = await getConversations({\n          current: 1,\n          size: 50\n        });\n\n        console.log(\"聊天列表API返回:\", result);\n\n        // 处理API返回的数据格式\n        let conversations = [];\n        if (\n          result &&\n          result.code === 0 &&\n          result.data &&\n          Array.isArray(result.data)\n        ) {\n          conversations = result.data;\n        } else if (result && Array.isArray(result)) {\n          conversations = result;\n        } else {\n          console.log(\"没有找到聊天列表或格式不正确\");\n          conversations = [];\n        }\n\n        if (conversations.length > 0) {\n          this.chatList = conversations.map(conversation => ({\n            id: conversation.id || conversation.conversationId,\n            userId: conversation.otherUserId || conversation.userId,\n            name:\n              conversation.otherUserNickname ||\n              conversation.nickname ||\n              \"用户\" + (conversation.otherUserId || conversation.userId),\n            avatar: this.formatAvatarUrl(\n              conversation.otherUserAvatar || conversation.avatar\n            ),\n            lastMessage: conversation.lastMessageContent || \"\",\n            lastMessageTime: conversation.lastMessageTime\n              ? new Date(conversation.lastMessageTime)\n              : new Date(),\n            lastMessageType: this.getMessageTypeString(\n              conversation.lastMessageType || 1\n            ),\n            unreadCount: conversation.unreadCount || 0,\n            isOnline: conversation.isOnline || false,\n            isMuted: conversation.isMuted || false\n          }));\n          console.log(\"会话列表\", this.chatList);\n          console.log(\"聊天列表处理完成，会话数量:\", this.chatList.length);\n        } else {\n          console.log(\"没有聊天记录，显示空状态\");\n          this.chatList = [];\n        }\n      } catch (error) {\n        console.error(\"加载聊天列表失败:\", error);\n        uni.showToast({\n          title: \"加载失败\",\n          icon: \"none\"\n        });\n        this.chatList = [];\n      } finally {\n        this.loading = false;\n        this.refreshing = false;\n      }\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (!avatar) {\n        return \"/static/images/toux.png\";\n      }\n\n      if (avatar.startsWith(\"http\")) {\n        return avatar;\n      }\n\n      return \"https://file.foxdance.com.cn\" + avatar;\n    },\n\n    // 获取消息类型字符串\n    getMessageTypeString(messageType) {\n      const typeMap = {\n        1: \"text\",\n        2: \"image\",\n        3: \"voice\",\n        4: \"video\",\n        5: \"file\"\n      };\n      return typeMap[messageType] || \"text\";\n    },\n\n    formatTime(time) {\n      const now = new Date();\n      const diff = now - new Date(time);\n      const minutes = Math.floor(diff / 60000);\n      const hours = Math.floor(diff / 3600000);\n      const days = Math.floor(diff / 86400000);\n\n      if (minutes < 1) return \"刚刚\";\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      if (days < 7) return `${days}天前`;\n\n      const date = new Date(time);\n      return `${date.getMonth() + 1}/${date.getDate()}`;\n    },\n\n    onRefresh() {\n      this.refreshing = true;\n      this.loadChatList().finally(() => {\n        // 停止页面下拉刷新\n        uni.stopPullDownRefresh();\n      });\n    },\n\n    async openChat(chat) {\n      console.log(\"打开聊天:\", chat);\n\n      try {\n        // 标记消息已读\n        if (chat.unreadCount > 0) {\n          await markMessageAsRead({\n            senderId: chat.userId,\n            receiverId: uni.getStorageSync(\"userid\")\n          });\n\n          // 清除未读数\n          chat.unreadCount = 0;\n\n          // 更新聊天列表\n          const index = this.chatList.findIndex(c => c.id === chat.id);\n          if (index !== -1) {\n            this.$set(this.chatList, index, { ...chat });\n          }\n        }\n\n        // 跳转到聊天详情页，使用与用户主页一致的参数格式\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?userId=${\n            chat.userId\n          }&nickname=${encodeURIComponent(\n            chat.name\n          )}&avatar=${encodeURIComponent(chat.avatar)}`\n        });\n      } catch (error) {\n        console.error(\"打开聊天失败:\", error);\n        // 即使标记已读失败，也要跳转到聊天页面\n        uni.navigateTo({\n          url: `/pagesSub/social/chat/detail?userId=${\n            chat.userId\n          }&nickname=${encodeURIComponent(\n            chat.name\n          )}&avatar=${encodeURIComponent(chat.avatar)}`\n        });\n      }\n    },\n\n    showChatActions(chat) {\n      const actions = [\n        \"置顶\",\n        chat.isMuted ? \"取消免打扰\" : \"免打扰\",\n        \"删除聊天\"\n      ];\n\n      uni.showActionSheet({\n        itemList: actions,\n        success: res => {\n          switch (res.tapIndex) {\n            case 0:\n              this.toggleChatTop(chat);\n              break;\n            case 1:\n              this.toggleChatMute(chat);\n              break;\n            case 2:\n              this.deleteChat(chat);\n              break;\n          }\n        }\n      });\n    },\n\n    toggleChatTop(chat) {\n      // 置顶/取消置顶逻辑\n      this.$u.toast(\"置顶成功\");\n    },\n\n    toggleChatMute(chat) {\n      chat.isMuted = !chat.isMuted;\n      this.$u.toast(chat.isMuted ? \"已开启免打扰\" : \"已关闭免打扰\");\n    },\n\n    deleteChat(chat) {\n      uni.showModal({\n        title: \"确认删除\",\n        content: \"确定要删除这个聊天吗？\",\n        success: res => {\n          if (res.confirm) {\n            const index = this.chatList.findIndex(item => item.id === chat.id);\n            if (index > -1) {\n              this.chatList.splice(index, 1);\n              this.$u.toast(\"删除成功\");\n            }\n          }\n        }\n      });\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/search/chat\"\n      });\n    },\n\n    startNewChat() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/contact/select\"\n      });\n    },\n\n    goSystemMessages() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/message/system\"\n      });\n    },\n\n    goLikeMessages() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/message/likes\"\n      });\n    },\n\n    goFollowMessages() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/message/followers\"\n      });\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新消息页数据...\");\n      this.chatList = [];\n      this.loadUnreadCounts();\n      this.loadChatList();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.message-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 100px;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 1px solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.header-actions {\n  display: flex;\n  gap: 16px;\n}\n\n.page-content {\n  margin-top: calc(44px + var(--status-bar-height));\n  min-height: calc(100vh - 44px - var(--status-bar-height));\n}\n\n.quick-actions {\n  background: #fff;\n  padding: 16px;\n  display: flex;\n  justify-content: space-around;\n  border-bottom: 8px solid #f8f9fa;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n}\n\n.action-icon {\n  width: 44px;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.action-icon.system {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.action-icon.like {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.action-icon.follow {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.action-text {\n  font-size: 12px;\n  color: #666;\n}\n\n.unread-badge {\n  position: absolute;\n  top: -2px;\n  right: 8px;\n  background: #ff4757;\n  color: #fff;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n}\n\n.chat-list {\n  background: #fff;\n  min-height: calc(100vh - 200px);\n}\n\n.chat-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.chat-item:last-child {\n  border-bottom: none;\n}\n\n.chat-avatar {\n  position: relative;\n  margin-right: 12px;\n}\n\n.online-dot {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  width: 12px;\n  height: 12px;\n  background: #52c41a;\n  border: 2px solid #fff;\n  border-radius: 6px;\n}\n\n.chat-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.chat-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 4px;\n}\n\n.chat-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.chat-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.chat-preview {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.message-preview {\n  flex: 1;\n  min-width: 0;\n}\n\n.preview-text {\n  font-size: 14px;\n  color: #666;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n\n.chat-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.unread-count {\n  background: #ff4757;\n  color: #fff;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n  min-width: 18px;\n  text-align: center;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80px 20px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999;\n  margin: 16px 0 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: #ccc;\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.loading-text {\n  margin-left: 8px;\n  color: #999;\n  font-size: 14px;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2caef1dd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753775919628\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/message/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}