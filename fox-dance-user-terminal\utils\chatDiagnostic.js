/**
 * 聊天功能诊断工具
 * 用于检测和修复实时聊天功能的常见问题
 */

/**
 * 检查WebSocket连接状态
 */
export function checkWebSocketConnection() {
  const diagnostic = {
    timestamp: new Date().toISOString(),
    platform: uni.getSystemInfoSync().platform,
    environment: process.env.NODE_ENV,
    issues: [],
    suggestions: []
  };

  // 检查用户认证信息
  const userId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("bausertoken") || uni.getStorageSync("token");
  
  if (!userId) {
    diagnostic.issues.push("用户ID缺失");
    diagnostic.suggestions.push("请重新登录获取用户ID");
  }
  
  if (!token) {
    diagnostic.issues.push("认证Token缺失");
    diagnostic.suggestions.push("请重新登录获取认证Token");
  }

  // 检查网络状态
  uni.getNetworkType({
    success: (res) => {
      diagnostic.networkType = res.networkType;
      if (res.networkType === 'none') {
        diagnostic.issues.push("网络连接异常");
        diagnostic.suggestions.push("请检查网络连接");
      }
    }
  });

  // 检查域名配置
  const wsUrl = "wss://admin.foxdance.com.cn/api/ws/chat";
  diagnostic.websocketUrl = wsUrl;
  
  // 微信小程序域名检查
  if (diagnostic.platform === 'mp-weixin') {
    diagnostic.suggestions.push("确保WebSocket域名已在微信小程序后台配置");
    diagnostic.suggestions.push("开发时可在开发者工具中关闭域名校验");
  }

  return diagnostic;
}

/**
 * 测试WebSocket连接
 */
export function testWebSocketConnection() {
  return new Promise((resolve, reject) => {
    const userId = uni.getStorageSync("userid");
    const token = uni.getStorageSync("bausertoken") || uni.getStorageSync("token");
    
    if (!userId || !token) {
      reject(new Error("缺少用户认证信息"));
      return;
    }

    const wsUrl = `wss://admin.foxdance.com.cn/api/ws/chat?userId=${userId}&token=${encodeURIComponent(token)}`;
    
    console.log("🧪 开始测试WebSocket连接:", wsUrl);
    
    const testSocket = uni.connectSocket({
      url: wsUrl,
      success: () => {
        console.log("✅ WebSocket连接请求发送成功");
      },
      fail: (error) => {
        console.error("❌ WebSocket连接请求失败:", error);
        reject(error);
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      testSocket.close();
      reject(new Error("连接超时"));
    }, 10000);

    testSocket.onOpen(() => {
      clearTimeout(timeout);
      console.log("✅ WebSocket连接成功建立");
      
      // 发送测试消息
      testSocket.send({
        data: JSON.stringify({
          type: "ping",
          data: { message: "connection test" }
        }),
        success: () => {
          console.log("✅ 测试消息发送成功");
        }
      });
      
      setTimeout(() => {
        testSocket.close();
        resolve({ success: true, message: "连接测试成功" });
      }, 2000);
    });

    testSocket.onError((error) => {
      clearTimeout(timeout);
      console.error("❌ WebSocket连接错误:", error);
      reject(error);
    });

    testSocket.onClose((res) => {
      clearTimeout(timeout);
      console.log("🔌 WebSocket连接关闭:", res);
    });
  });
}

/**
 * 检查消息发送功能
 */
export function checkMessageSending() {
  const issues = [];
  const suggestions = [];

  // 检查API配置
  try {
    const apiConfig = require('@/config/http.api.js');
    if (!apiConfig.sendMessage) {
      issues.push("发送消息API未配置");
      suggestions.push("检查socialApi.js中的sendMessage方法");
    }
  } catch (error) {
    issues.push("API配置文件加载失败");
    suggestions.push("检查@/config/http.api.js文件是否存在");
  }

  return { issues, suggestions };
}

/**
 * 生成诊断报告
 */
export function generateDiagnosticReport() {
  const report = {
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    platform: uni.getSystemInfoSync(),
    websocket: checkWebSocketConnection(),
    messaging: checkMessageSending(),
    recommendations: []
  };

  // 生成建议
  if (report.websocket.issues.length > 0) {
    report.recommendations.push("修复WebSocket连接问题");
  }
  
  if (report.messaging.issues.length > 0) {
    report.recommendations.push("修复消息发送功能");
  }

  if (report.websocket.issues.length === 0 && report.messaging.issues.length === 0) {
    report.recommendations.push("聊天功能配置正常，如仍有问题请检查后端服务");
  }

  return report;
}

/**
 * 显示诊断结果
 */
export function showDiagnosticModal() {
  const report = generateDiagnosticReport();
  
  let content = `📊 聊天功能诊断报告\n\n`;
  content += `🕐 检测时间: ${new Date(report.timestamp).toLocaleString()}\n`;
  content += `📱 平台: ${report.platform.platform}\n\n`;
  
  if (report.websocket.issues.length > 0) {
    content += `❌ WebSocket问题:\n`;
    report.websocket.issues.forEach(issue => {
      content += `  • ${issue}\n`;
    });
    content += `\n💡 建议:\n`;
    report.websocket.suggestions.forEach(suggestion => {
      content += `  • ${suggestion}\n`;
    });
  } else {
    content += `✅ WebSocket配置正常\n`;
  }
  
  if (report.messaging.issues.length > 0) {
    content += `\n❌ 消息功能问题:\n`;
    report.messaging.issues.forEach(issue => {
      content += `  • ${issue}\n`;
    });
  } else {
    content += `✅ 消息功能配置正常\n`;
  }

  uni.showModal({
    title: "聊天诊断",
    content: content,
    showCancel: false,
    confirmText: "知道了"
  });

  // 同时输出到控制台
  console.log("📊 聊天功能诊断报告:", report);
  
  return report;
}

/**
 * 快速修复常见问题
 */
export function quickFix() {
  const fixes = [];
  
  // 检查并修复Token问题
  const bausertoken = uni.getStorageSync("bausertoken");
  const token = uni.getStorageSync("token");
  
  if (!bausertoken && token) {
    uni.setStorageSync("bausertoken", token);
    fixes.push("已同步Token到bausertoken");
  }
  
  // 清理无效的WebSocket连接
  try {
    // 这里可以添加清理逻辑
    fixes.push("已清理无效连接");
  } catch (error) {
    console.error("清理连接时出错:", error);
  }
  
  if (fixes.length > 0) {
    uni.showToast({
      title: `已修复${fixes.length}个问题`,
      icon: "success"
    });
    console.log("🔧 快速修复完成:", fixes);
  } else {
    uni.showToast({
      title: "未发现可修复问题",
      icon: "none"
    });
  }
  
  return fixes;
}
