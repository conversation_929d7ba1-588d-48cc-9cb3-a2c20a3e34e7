package com.yupi.springbootinit.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 员工表
 * 
 * @TableName ba_user
 */
@TableName(value = "ba_user")
@Data
public class BaUser implements Serializable {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 社区ID
     */
    @TableField("social_id")
    private String socialId;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 是否是会员 0-否 1-是
     */
    @TableField("is_member")
    private Integer isMember;

    /**
     * 剩余投票次数
     */
    @TableField("remaining_votes")
    private Integer remainingVotes;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 舞种
     */
    @TableField("dance_type")
    private String danceType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}