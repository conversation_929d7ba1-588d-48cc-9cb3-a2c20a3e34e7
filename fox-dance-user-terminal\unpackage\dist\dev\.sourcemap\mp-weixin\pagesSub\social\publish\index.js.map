{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?0aff", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?d711", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?e559", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?684e", "uni-app:///pagesSub/social/publish/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?547e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/publish/index.vue?b521", "uni-app:///main.js"], "names": ["name", "data", "userInfo", "avatar", "nickname", "postTitle", "postContent", "selectedImages", "coverImageUrl", "selectedTopics", "selectedLocation", "visibility", "publishing", "showTopicModal", "showLocationModal", "topicKeyword", "allTopics", "id", "postCount", "nearbyLocations", "address", "computed", "canPublish", "visibilityText", "public", "friends", "private", "filteredTopics", "topic", "onLoad", "console", "uni", "title", "icon", "duration", "mounted", "methods", "checkUserLogin", "setTimeout", "url", "testApiConnection", "method", "timeout", "response", "loadUserInfo", "userId", "result", "loadHotTopics", "hotTags", "goBack", "content", "success", "chooseImage", "maxCount", "count", "sizeType", "sourceType", "tempFilePaths", "mask", "images", "coverImage", "fail", "removeImage", "handleImageSelectFromTabBar", "app", "selectTopic", "toggleTopic", "searchTopics", "selectLocation", "latitude", "longitude", "selectLocationItem", "clearLocation", "setVisibility", "itemList", "publishPost", "postData", "tags", "locationName", "locationLatitude", "locationLongitude", "locationAddress", "isPublic", "status", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoKlwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAQA;EACAA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEAC;MACAC;MAEAC;MACAC,YACA;QAAAC;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,GACA;QAAAD;QAAAjB;QAAAkB;MAAA,EACA;MAEAC,kBACA;QACAF;QACAjB;QACAoB;MACA,GACA;QACAH;QACAjB;QACAoB;MACA,GACA;QACAH;QACAjB;QACAoB;MACA;IAEA;EACA;EACAC;IACAC;MACA,OACA,oCACA,sCACA;IAEA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QAAA,OACAC;MAAA,EACA;IACA;EACA;EAEAC;IACAC;IACAA;;IAEA;IACA;MACA;IACA;;IAEA;IACAC;MACAC;MACAC;MACAC;IACA;IAEA;IACA;IACA;;IAEA;IACA;MACA;IACA;EACA;EAEAC;IACAL;;IAEA;IACA;MACAA;MACA;MACA;MACA;IACA;EACA;EACAM;IACA;IACAC;MACA;MACA;MAEA;QACAP;QACAC;UACAC;UACAC;UACAC;QACA;QACAI;UACAP;YACAQ;UACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAV;gBAAA;gBAAA;gBAAA,OAEAC;kBACAQ;kBACAE;kBACAC;gBACA;cAAA;gBAJAC;gBAKAb;gBAEA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAd;gBAAA;gBAEA;gBACAe;gBACAf;gBAAA;gBAAA,OAEA;cAAA;gBAAAgB;gBAEAhB;gBAEA;kBACA;oBACA3B,QACA,iCACA2C,qBACA;oBACA1C;kBACA;gBACA;kBACA0B;kBACA;oBACA3B;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA0B;gBACAA;gBAEAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;kBACA/B;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjB;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAkB;gBACAlB;gBAEA,IACAkB,WACAA,sBACAA,gBACAA,yBACA;kBACA;oBAAA;sBACA/B;sBACAjB;sBACAkB;oBACA;kBAAA;kBACAY;gBACA;kBACA;kBACA;oBAAA;sBACAb;sBACAjB;sBACAkB;oBACA;kBAAA;kBACAY;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;gBAEAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACA,oBACA;kBAAAjB;kBAAAjB;kBAAAkB;gBAAA,GACA;kBAAAD;kBAAAjB;kBAAAkB;gBAAA,GACA;kBAAAD;kBAAAjB;kBAAAkB;gBAAA,GACA;kBAAAD;kBAAAjB;kBAAAkB;gBAAA,GACA;kBAAAD;kBAAAjB;kBAAAkB;gBAAA,EACA;gBACAY;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAmB;MACA;QACAlB;UACAC;UACAkB;UACAC;YACA;cACApB;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEAtB;kBACAuB;kBACAC;kBACAC;kBACAL;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAM;8BACA3B;;8BAEA;8BACAC;gCACAC;gCACA0B;8BACA;8BAAA;8BAGA;8BACA5B;8BAAA;8BAAA,OAEA;4BAAA;8BAAAgB;8BACAhB;;8BAEA;8BACAC;8BAEA;gCACA;gCAAA,eACAe,iFAEA;gCACA;;gCAEA;gCACA;gCAEAhB;kCACA6B;kCACAC;gCACA;gCAEA;8BACA;gCACA9B;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAC;8BACAD;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACA+B;oBACA/B;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAgC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjC;gBAAA;gBAGA;gBACAkC;gBACAzD;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAuB;;gBAEA;gBACAC;kBACAC;kBACA0B;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAZ;gBACAhB;;gBAEA;gBACAC;gBAEA;kBACA;kBAAA,gBACAe,mFAEA;kBACA;;kBAEA;kBACA;kBAEAhB;oBACA6B;oBACAC;kBACA;kBAEA;gBACA;kBACA9B;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAC;gBAEAD;gBACA;cAAA;gBAGA;gBACAkC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAlC;gBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAkC;MACA;IACA;IAEAC;MACA;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MAAA;MACAtC;;MAEA;MACAC;QACAoB;UACArB;;UAEA;UACA;YACA9B;YACAoB;YACAiD;YACAC;UACA;UAEAvC;YACAC;YACAC;YACAC;UACA;QACA;QACA2B;UACA/B;UAEA;YACA;YACA;UACA;UAEAC;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;QACA;MACA;IACA;IAEAqC;MACA;MACA;IACA;IAEAC;MACA;MACAzC;QACAC;QACAC;QACAC;MACA;IACA;IAEAuC;MAAA;MACA1C;QACA2C;QACAvB;UACA;UACA;QACA;MACA;IACA;IAEAwB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA9B;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAd;kBACAC;kBACAC;kBACAC;gBACA;gBACAI;kBACAP;oBACAQ;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAGA;gBACAqC;kBACA/B;kBAAA;kBACAb;kBAAA;kBACAkB;kBACAS;kBACAC;kBAAA;kBACAiB;oBAAA;kBAAA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEArD;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAgB;gBAEAhB;gBAEA;kBACA;kBACA;kBACAC;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACptBA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAsD,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/publish/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bb7c3636\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/publish/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=bb7c3636&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.postTitle.length\n  var g1 = _vm.postContent.length\n  var g2 = _vm.selectedImages.length\n  var g3 = _vm.selectedTopics.length\n  var g4 = g3\n    ? _vm.selectedTopics\n        .map(function (t) {\n          return \"#\" + t\n        })\n        .join(\" \")\n    : null\n  var l0 = _vm.__map(_vm.filteredTopics, function (topic, __i0__) {\n    var $orig = _vm.__get_orig(topic)\n    var g5 = _vm.selectedTopics.includes(topic.name)\n    return {\n      $orig: $orig,\n      g5: g5,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showTopicModal = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showLocationModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"publish-container\">\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 用户信息 -->\n      <view class=\"user-section\">\n        <u-avatar :src=\"userInfo.avatar\" size=\"40\"></u-avatar>\n        <text class=\"username\">{{ userInfo.nickname }}</text>\n      </view>\n\n      <!-- 标题输入 -->\n      <view class=\"title-section\">\n        <input v-model=\"postTitle\" class=\"title-input\" placeholder=\"标题（可选）\" :maxlength=\"50\" />\n        <view class=\"title-char-count\">{{ postTitle.length }}/50</view>\n      </view>\n\n      <!-- 文字输入 -->\n      <view class=\"text-section\">\n        <textarea\n          v-model=\"postContent\"\n          class=\"content-input\"\n          placeholder=\"分享你的生活...\"\n          :maxlength=\"500\"\n          auto-height\n          :show-confirm-bar=\"false\"\n        />\n        <view class=\"char-count\">{{ postContent.length }}/500</view>\n      </view>\n\n      <!-- 图片上传 -->\n      <view class=\"image-section\">\n        <view class=\"image-grid\">\n          <view v-for=\"(image, index) in selectedImages\" :key=\"index\" class=\"image-item\">\n            <image :src=\"image\" class=\"uploaded-image\" mode=\"aspectFill\" />\n            <view class=\"delete-btn\" @click=\"removeImage(index)\">\n              <u-icon name=\"close\" color=\"#fff\" size=\"16\"></u-icon>\n            </view>\n          </view>\n          <view v-if=\"selectedImages.length < 9\" class=\"add-image-btn\" @click=\"chooseImage\">\n            <u-icon name=\"camera\" color=\"#999\" size=\"32\"></u-icon>\n            <text class=\"add-text\">添加图片</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 功能选项 -->\n      <view class=\"options-section\">\n        <!-- 话题选择 -->\n        <view class=\"option-item\" @click=\"selectTopic\">\n          <view class=\"option-left\">\n            <u-icon name=\"tags\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加话题</text>\n          </view>\n          <view class=\"option-right\">\n            <text\n              v-if=\"selectedTopics.length\"\n              class=\"selected-topics\"\n            >{{ selectedTopics.map(t => '#' + t).join(' ') }}</text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 位置定位 -->\n        <view class=\"option-item\" @click=\"selectLocation\">\n          <view class=\"option-left\">\n            <u-icon name=\"map\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">添加位置</text>\n          </view>\n          <view class=\"option-right\">\n            <view v-if=\"selectedLocation\" class=\"location-selected\">\n              <view class=\"location-info-inline\">\n                <text class=\"selected-location\">{{ selectedLocation.name }}</text>\n                <text class=\"selected-address\">{{ selectedLocation.address }}</text>\n              </view>\n              <u-icon name=\"close-circle-fill\" color=\"#999\" size=\"18\" @click.stop=\"clearLocation\"></u-icon>\n            </view>\n            <u-icon v-else name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n\n        <!-- 可见性设置 -->\n        <view class=\"option-item\" @click=\"setVisibility\">\n          <view class=\"option-left\">\n            <u-icon name=\"eye\" color=\"#2979ff\" size=\"20\"></u-icon>\n            <text class=\"option-text\">可见性</text>\n          </view>\n          <view class=\"option-right\">\n            <text class=\"visibility-text\">{{ visibilityText }}</text>\n            <u-icon name=\"arrow-right\" color=\"#999\" size=\"16\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 提醒文字 -->\n      <view class=\"tips-section\">\n        <text class=\"tips-text\">发布即表示同意《社区公约》，请文明发言，共建和谐社区</text>\n      </view>\n\n      <!-- 发布按钮 -->\n      <view class=\"publish-section\">\n        <text\n          class=\"publish-btn\"\n          :class=\"{ disabled: !canPublish }\"\n          @click=\"publishPost\"\n        >{{ publishing ? '发布中...' : '发布' }}</text>\n      </view>\n    </scroll-view>\n\n    <!-- 话题选择弹窗 -->\n    <u-popup v-model=\"showTopicModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"topic-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择话题</text>\n          <u-icon name=\"close\" @click=\"showTopicModal = false\"></u-icon>\n        </view>\n        <view class=\"topic-search\">\n          <u-input\n            v-model=\"topicKeyword\"\n            placeholder=\"搜索话题\"\n            prefix-icon=\"search\"\n            @input=\"searchTopics\"\n          />\n        </view>\n        <view class=\"topic-list\">\n          <view\n            v-for=\"topic in filteredTopics\"\n            :key=\"topic.id\"\n            class=\"topic-option\"\n            :class=\"{ selected: selectedTopics.includes(topic.name) }\"\n            @click=\"toggleTopic(topic)\"\n          >\n            <text class=\"topic-name\">#{{ topic.name }}</text>\n            <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n\n    <!-- 位置选择弹窗 -->\n    <u-popup v-model=\"showLocationModal\" mode=\"bottom\" border-radius=\"20\">\n      <view class=\"location-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">选择位置</text>\n          <u-icon name=\"close\" @click=\"showLocationModal = false\"></u-icon>\n        </view>\n        <view class=\"location-list\">\n          <view\n            v-for=\"location in nearbyLocations\"\n            :key=\"location.id\"\n            class=\"location-option\"\n            @click=\"selectLocationItem(location)\"\n          >\n            <u-icon name=\"map-pin\" color=\"#2979ff\" size=\"16\"></u-icon>\n            <view class=\"location-info\">\n              <text class=\"location-name\">{{ location.name }}</text>\n              <text class=\"location-address\">{{ location.address }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport {\n  createPost,\n  getHotTags,\n  getUserProfile,\n  uploadPostImage,\n  uploadPostImages\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialPublish\",\n  data() {\n    return {\n      userInfo: {\n        avatar: \"/static/images/toux.png\",\n        nickname: \"加载中...\"\n      },\n      postTitle: \"\",\n      postContent: \"\",\n      selectedImages: [],\n      coverImageUrl: \"\", // 封面图片URL\n      selectedTopics: [],\n      selectedLocation: null,\n      visibility: \"public\", // public, friends, private\n      publishing: false, // 发布状态\n\n      showTopicModal: false,\n      showLocationModal: false,\n\n      topicKeyword: \"\",\n      allTopics: [\n        { id: 1, name: \"街舞\", postCount: 1234 },\n        { id: 2, name: \"现代舞\", postCount: 856 },\n        { id: 3, name: \"芭蕾\", postCount: 642 },\n        { id: 4, name: \"拉丁舞\", postCount: 789 },\n        { id: 5, name: \"爵士舞\", postCount: 456 },\n        { id: 6, name: \"民族舞\", postCount: 321 },\n        { id: 7, name: \"古典舞\", postCount: 298 },\n        { id: 8, name: \"舞蹈教学\", postCount: 567 },\n        { id: 9, name: \"舞蹈比赛\", postCount: 234 },\n        { id: 10, name: \"舞蹈培训\", postCount: 189 }\n      ],\n\n      nearbyLocations: [\n        {\n          id: 1,\n          name: \"星巴克咖啡\",\n          address: \"北京市朝阳区三里屯太古里\"\n        },\n        {\n          id: 2,\n          name: \"三里屯太古里\",\n          address: \"北京市朝阳区三里屯路19号\"\n        },\n        {\n          id: 3,\n          name: \"朝阳公园\",\n          address: \"北京市朝阳区朝阳公园南路1号\"\n        }\n      ]\n    };\n  },\n  computed: {\n    canPublish() {\n      return (\n        this.postTitle.trim().length > 0 ||\n        this.postContent.trim().length > 0 ||\n        this.selectedImages.length > 0\n      );\n    },\n\n    visibilityText() {\n      const map = {\n        public: \"公开\",\n        friends: \"仅朋友可见\",\n        private: \"仅自己可见\"\n      };\n      return map[this.visibility];\n    },\n\n    filteredTopics() {\n      if (!this.topicKeyword) return this.allTopics;\n      return this.allTopics.filter(topic =>\n        topic.name.includes(this.topicKeyword)\n      );\n    }\n  },\n\n  onLoad(options) {\n    console.log(\"=== 发布页面加载 ===\");\n    console.log(\"onLoad方法被调用了\", options);\n\n    // 首先检查用户登录状态\n    if (!this.checkUserLogin()) {\n      return; // 如果未登录，直接返回，不执行后续操作\n    }\n\n    // 显示一个提示确认方法被调用\n    uni.showToast({\n      title: \"onLoad被调用\",\n      icon: \"none\",\n      duration: 2000\n    });\n\n    this.testApiConnection();\n    this.loadUserInfo();\n    this.loadHotTopics();\n\n    // 检查是否从图片选择跳转过来\n    if (options && options.fromImageSelect === \"true\") {\n      this.handleImageSelectFromTabBar();\n    }\n  },\n\n  mounted() {\n    console.log(\"=== mounted生命周期被调用 ===\");\n\n    // 如果onLoad没有被调用，在这里也执行一次\n    if (this.userInfo.nickname === \"加载中...\") {\n      console.log(\"onLoad可能没有执行，在mounted中重新执行\");\n      this.testApiConnection();\n      this.loadUserInfo();\n      this.loadHotTopics();\n    }\n  },\n  methods: {\n    // 检查用户登录状态\n    checkUserLogin() {\n      const token = uni.getStorageSync(\"token\");\n      const userId = uni.getStorageSync(\"userid\");\n\n      if (!token || !userId) {\n        console.log(\"用户未登录，跳转到登录页\");\n        uni.showToast({\n          title: \"请先登录\",\n          icon: \"none\",\n          duration: 2000\n        });\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/login/login\"\n          });\n        }, 1500);\n        return false;\n      }\n\n      return true;\n    },\n\n    // 测试API连接\n    async testApiConnection() {\n      console.log(\"测试API连接...\");\n      try {\n        const response = await uni.request({\n          url: \"http://localhost:8101/api/health\",\n          method: \"GET\",\n          timeout: 5000\n        });\n        console.log(\"API连接测试结果:\", response);\n\n        if (response.statusCode === 200) {\n          console.log(\"✅ 后端服务连接正常\");\n        } else {\n          console.log(\"❌ 后端服务响应异常:\", response.statusCode);\n        }\n      } catch (error) {\n        console.error(\"❌ 后端服务连接失败:\", error);\n        uni.showToast({\n          title: \"后端服务连接失败\",\n          icon: \"none\",\n          duration: 3000\n        });\n      }\n    },\n\n    // 加载用户信息\n    async loadUserInfo() {\n      console.log(\"开始加载用户信息...\");\n      try {\n        // 从缓存中获取当前用户ID\n        const userId = uni.getStorageSync(\"userid\");\n        console.log(\"调用getUserProfile，用户ID:\", userId);\n\n        const result = await getUserProfile(userId);\n\n        console.log(\"获取用户信息结果:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          this.userInfo = {\n            avatar:\n              \"https://file.foxdance.com.cn\" +\n              result.data.avatar +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            nickname: result.data.nickname || \"无名氏\"\n          };\n        } else {\n          console.warn(\"获取用户信息失败:\", result);\n          this.userInfo = {\n            avatar: \"/static/images/toux.png\",\n            nickname: \"用户\"\n          };\n        }\n      } catch (error) {\n        console.error(\"加载用户信息失败:\", error);\n        console.error(\"错误详情:\", error.message || error);\n\n        uni.showToast({\n          title: \"用户信息加载失败\",\n          icon: \"none\",\n          duration: 2000\n        });\n\n        this.userInfo = {\n          avatar: \"/static/images/toux.png\",\n          nickname: \"用户\"\n        };\n      }\n    },\n\n    // 加载热门话题\n    async loadHotTopics() {\n      console.log(\"开始加载热门话题...\");\n      try {\n        const hotTags = await getHotTags(20);\n        console.log(\"获取热门话题结果:\", hotTags);\n\n        if (\n          hotTags &&\n          hotTags.code === 0 &&\n          hotTags.data &&\n          hotTags.data.length > 0\n        ) {\n          this.allTopics = hotTags.data.map(tag => ({\n            id: tag.id,\n            name: tag.name,\n            postCount: tag.useCount || 0\n          }));\n          console.log(\"话题加载成功，数量:\", this.allTopics.length);\n        } else if (hotTags && hotTags.length > 0) {\n          // 兼容直接返回数组的情况\n          this.allTopics = hotTags.map(tag => ({\n            id: tag.id,\n            name: tag.name,\n            postCount: tag.useCount || 0\n          }));\n          console.log(\"话题加载成功（数组格式），数量:\", this.allTopics.length);\n        } else {\n          console.log(\"没有获取到话题数据，hotTags:\", hotTags);\n        }\n      } catch (error) {\n        console.error(\"加载热门话题失败:\", error);\n        console.error(\"错误详情:\", error.message || error);\n\n        uni.showToast({\n          title: \"话题加载失败\",\n          icon: \"none\",\n          duration: 2000\n        });\n\n        // 使用默认话题列表\n        this.allTopics = [\n          { id: 1, name: \"生活\", postCount: 0 },\n          { id: 2, name: \"美食\", postCount: 0 },\n          { id: 3, name: \"旅行\", postCount: 0 },\n          { id: 4, name: \"摄影\", postCount: 0 },\n          { id: 5, name: \"时尚\", postCount: 0 }\n        ];\n        console.log(\"使用默认话题列表\");\n      }\n    },\n    goBack() {\n      if (this.postTitle || this.postContent || this.selectedImages.length) {\n        uni.showModal({\n          title: \"提示\",\n          content: \"确定要放弃编辑吗？\",\n          success: res => {\n            if (res.confirm) {\n              uni.navigateBack();\n            }\n          }\n        });\n      } else {\n        uni.navigateBack();\n      }\n    },\n\n    async chooseImage() {\n      const maxCount = 9 - this.selectedImages.length;\n\n      uni.chooseImage({\n        count: maxCount,\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: async res => {\n          const tempFilePaths = res.tempFilePaths;\n          console.log(\"🔥 选择图片成功，开始上传到COS:\", tempFilePaths);\n\n          // 显示上传进度\n          uni.showLoading({\n            title: \"上传图片中...\",\n            mask: true\n          });\n\n          try {\n            // 使用批量上传API\n            console.log(\"🔥 开始批量上传图片:\", tempFilePaths);\n\n            const result = await uploadPostImages(tempFilePaths);\n            console.log(\"🔥 批量上传结果:\", result);\n\n            // 隐藏加载提示\n            uni.hideLoading();\n\n            if (result.code === 0 && result.data) {\n              // 获取上传结果\n              const { images, coverImage } = result.data;\n\n              // 更新选中的图片列表\n              this.selectedImages = images;\n\n              // 保存封面图片URL（用于发布时传给后端）\n              this.coverImageUrl = coverImage;\n\n              console.log(\"✅ 批量上传成功:\", {\n                images: images.length,\n                coverImage\n              });\n\n              this.$u.toast(`成功上传${images.length}张图片`);\n            } else {\n              console.error(\"❌ 批量上传失败:\", result);\n              this.$u.toast(\"图片上传失败\");\n            }\n          } catch (error) {\n            uni.hideLoading();\n            console.error(\"❌ 图片上传过程异常:\", error);\n            this.$u.toast(\"图片上传失败\");\n          }\n        },\n        fail: error => {\n          console.error(\"❌ 选择图片失败:\", error);\n          this.$u.toast(\"选择图片失败\");\n        }\n      });\n    },\n\n    removeImage(index) {\n      this.selectedImages.splice(index, 1);\n    },\n\n    // 处理从TabBar图片选择跳转过来的情况\n    async handleImageSelectFromTabBar() {\n      console.log(\"🔥 处理从TabBar选择的图片\");\n\n      try {\n        // 从全局数据中获取选择的图片\n        const app = getApp();\n        const selectedImages = app.globalData.selectedImages;\n\n        if (selectedImages && selectedImages.length > 0) {\n          console.log(\"🔥 获取到选择的图片:\", selectedImages);\n\n          // 显示上传进度\n          uni.showLoading({\n            title: \"上传图片中...\",\n            mask: true\n          });\n\n          try {\n            // 使用批量上传API\n            const result = await uploadPostImages(selectedImages);\n            console.log(\"🔥 批量上传结果:\", result);\n\n            // 隐藏加载提示\n            uni.hideLoading();\n\n            if (result.code === 0 && result.data) {\n              // 获取上传结果\n              const { images, coverImage } = result.data;\n\n              // 更新选中的图片列表\n              this.selectedImages = images;\n\n              // 保存封面图片URL\n              this.coverImageUrl = coverImage;\n\n              console.log(\"✅ 批量上传成功:\", {\n                images: images.length,\n                coverImage\n              });\n\n              this.$u.toast(`成功上传${images.length}张图片`);\n            } else {\n              console.error(\"❌ 批量上传失败:\", result);\n              this.$u.toast(\"图片上传失败\");\n            }\n          } catch (uploadError) {\n            // 隐藏加载提示\n            uni.hideLoading();\n\n            console.error(\"❌ 批量上传异常:\", uploadError);\n            this.$u.toast(uploadError.message || \"图片上传失败\");\n          }\n\n          // 清除全局数据\n          app.globalData.selectedImages = null;\n        }\n      } catch (error) {\n        console.error(\"❌ 处理TabBar图片选择失败:\", error);\n        uni.hideLoading();\n        this.$u.toast(\"处理图片失败\");\n      }\n    },\n\n    selectTopic() {\n      this.showTopicModal = true;\n    },\n\n    toggleTopic(topic) {\n      const index = this.selectedTopics.indexOf(topic.name);\n      if (index > -1) {\n        this.selectedTopics.splice(index, 1);\n      } else {\n        if (this.selectedTopics.length < 3) {\n          this.selectedTopics.push(topic.name);\n        } else {\n          this.$u.toast(\"最多选择3个话题\");\n        }\n      }\n    },\n\n    searchTopics() {\n      // 搜索话题逻辑\n    },\n\n    selectLocation() {\n      console.log(\"打开位置选择...\");\n\n      // 使用uni.chooseLocation打开地图选择位置\n      uni.chooseLocation({\n        success: res => {\n          console.log(\"位置选择成功:\", res);\n\n          // 构建位置对象\n          this.selectedLocation = {\n            name: res.name || res.address,\n            address: res.address,\n            latitude: res.latitude,\n            longitude: res.longitude\n          };\n\n          uni.showToast({\n            title: \"位置选择成功\",\n            icon: \"success\",\n            duration: 1500\n          });\n        },\n        fail: err => {\n          console.error(\"位置选择失败:\", err);\n\n          if (err.errMsg && err.errMsg.includes(\"cancel\")) {\n            // 用户取消选择，不显示错误提示\n            return;\n          }\n\n          uni.showToast({\n            title: \"位置选择失败\",\n            icon: \"none\",\n            duration: 2000\n          });\n\n          // 如果地图选择失败，回退到弹窗选择\n          this.showLocationModal = true;\n        }\n      });\n    },\n\n    selectLocationItem(location) {\n      this.selectedLocation = location;\n      this.showLocationModal = false;\n    },\n\n    clearLocation() {\n      this.selectedLocation = null;\n      uni.showToast({\n        title: \"已清除位置\",\n        icon: \"success\",\n        duration: 1000\n      });\n    },\n\n    setVisibility() {\n      uni.showActionSheet({\n        itemList: [\"公开\", \"仅朋友可见\", \"仅自己可见\"],\n        success: res => {\n          const visibilityMap = [\"public\", \"friends\", \"private\"];\n          this.visibility = visibilityMap[res.tapIndex];\n        }\n      });\n    },\n\n    async publishPost() {\n      if (!this.canPublish || this.publishing) return;\n\n      // 检查用户登录状态\n      const userId = uni.getStorageSync(\"userid\");\n      if (!userId) {\n        uni.showToast({\n          title: \"请先登录\",\n          icon: \"none\",\n          duration: 2000\n        });\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/login/login\"\n          });\n        }, 1500);\n        return;\n      }\n\n      this.publishing = true;\n\n      try {\n        // 构建发布数据 - 符合PostCreateDTO格式\n        const postData = {\n          userId: Number(userId), // 从缓存获取当前用户ID\n          title: this.postTitle.trim() || null, // 单独发送标题字段\n          content: this.postContent.trim(),\n          images: this.selectedImages,\n          coverImage: this.coverImageUrl, // 封面图片URL\n          tags: this.selectedTopics.map(topic => topic.name || topic),\n          locationName: this.selectedLocation?.name || \"\",\n          locationLatitude: this.selectedLocation?.latitude || null,\n          locationLongitude: this.selectedLocation?.longitude || null,\n          locationAddress: this.selectedLocation?.address || \"\",\n          isPublic: this.visibility === \"public\" ? 1 : 0,\n          status: 1 // 1-已发布\n        };\n\n        console.log(\"发布帖子数据:\", postData);\n\n        // 调用发布API\n        const result = await createPost(postData);\n\n        console.log(\"发布API返回结果:\", result);\n\n        if (result && result.code === 0) {\n          this.$u.toast(\"发布成功\");\n          // 返回主页面\n          uni.navigateBack();\n        } else {\n          this.$u.toast(result?.message || \"发布失败，请重试\");\n          this.publishing = false;\n        }\n      } catch (error) {\n        console.error(\"发布帖子失败:\", error);\n        this.$u.toast(\"网络错误，请重试\");\n        this.publishing = false;\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.publish-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.content {\n  padding: 20px 16px;\n  padding-top: calc(20px + var(--status-bar-height)); /* 状态栏高度 */\n  padding-bottom: calc(20px + env(safe-area-inset-bottom)); /* 安全区域 */\n  width: auto;\n}\n\n.user-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.username {\n  margin-left: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n/* 标题输入区域 */\n.title-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 12px;\n  border: 1px solid #f0f0f0;\n}\n\n.title-input {\n  width: 100%;\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  border: none;\n  outline: none;\n  background: transparent;\n  line-height: 1.4;\n}\n\n.title-input::placeholder {\n  color: #c0c4cc;\n  font-weight: 400;\n}\n\n.title-char-count {\n  text-align: right;\n  font-size: 12px;\n  color: #999;\n  margin-top: 8px;\n}\n\n.text-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  position: relative;\n}\n\n.content-input {\n  width: 100%;\n  min-height: 120px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #333;\n}\n\n.char-count {\n  position: absolute;\n  bottom: 12px;\n  right: 16px;\n  font-size: 12px;\n  color: #999;\n}\n\n.image-section {\n  margin-bottom: 16px;\n}\n\n.image-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.image-item {\n  position: relative;\n  width: calc(33.33% - 6px);\n  height: 100px;\n}\n\n.uploaded-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n}\n\n.delete-btn {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  width: 24px;\n  height: 24px;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-image-btn {\n  width: calc(33.33% - 6px);\n  height: 100px;\n  background: #f5f5f5;\n  border: 2px dashed #ddd;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-text {\n  font-size: 12px;\n  color: #999;\n  margin-top: 4px;\n}\n\n.options-section {\n  background: #fff;\n  border-radius: 12px;\n  margin-bottom: 16px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.option-item:last-child {\n  border-bottom: none;\n}\n\n.option-left {\n  display: flex;\n  align-items: center;\n}\n\n.option-text {\n  margin-left: 12px;\n  font-size: 15px;\n  color: #333;\n}\n\n.option-right {\n  display: flex;\n  align-items: center;\n}\n\n.selected-topics,\n.selected-location,\n.visibility-text {\n  font-size: 14px;\n  color: #666;\n  margin-right: 8px;\n}\n\n/* 位置选择相关样式 */\n.location-selected {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  max-width: 200px;\n}\n\n.location-info-inline {\n  flex: 1;\n  margin-right: 8px;\n  overflow: hidden;\n}\n\n.selected-location {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.selected-address {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n.tips-section {\n  padding: 16px;\n}\n\n.tips-text {\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n  text-align: center;\n}\n\n/* 发布按钮区域 */\n.publish-section {\n  padding: 24px 16px;\n  display: flex;\n  justify-content: center;\n}\n\n.publish-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  font-size: 16px;\n  font-weight: 600;\n  padding: 14px 48px;\n  border-radius: 28px;\n  text-align: center;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);\n  min-width: 120px;\n}\n\n.publish-btn.disabled {\n  background: #e4e7ed;\n  color: #c0c4cc;\n  box-shadow: none;\n}\n\n.publish-btn:not(.disabled):active {\n  transform: scale(0.95);\n  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.4);\n}\n\n.topic-modal,\n.location-modal {\n  background: #fff;\n  border-radius: 20px 20px 0 0;\n  max-height: 60vh;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px 20px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.modal-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.topic-search {\n  padding: 16px 20px;\n}\n\n.topic-list,\n.location-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.topic-option {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.topic-option.selected {\n  background: #f0f8ff;\n}\n\n.topic-name {\n  font-size: 15px;\n  color: #333;\n}\n\n.topic-count {\n  font-size: 12px;\n  color: #999;\n}\n\n.location-option {\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.location-info {\n  margin-left: 12px;\n  flex: 1;\n}\n\n.location-name {\n  font-size: 15px;\n  color: #333;\n  display: block;\n}\n\n.location-address {\n  font-size: 12px;\n  color: #999;\n  margin-top: 2px;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bb7c3636&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760719539\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/publish/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}