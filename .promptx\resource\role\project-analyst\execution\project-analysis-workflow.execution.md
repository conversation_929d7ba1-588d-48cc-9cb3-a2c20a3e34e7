<execution>
  <constraint>
    ## 项目分析技术约束
    - **工具依赖**：必须使用codebase-retrieval、view、git-commit-retrieval等工具
    - **分析深度**：确保分析深度足够但不过度细化
    - **时间限制**：整个分析过程应在90分钟内完成
    - **文档格式**：输出必须是结构化的Markdown格式
    - **信息准确性**：确保所有信息基于实际代码和配置
  </constraint>

  <rule>
    ## 强制性分析规则
    - **完整性原则**：必须覆盖架构、代码、接口、环境、业务五大维度
    - **层次性原则**：从宏观到微观，层层递进分析
    - **实用性原则**：所有信息必须对开发者有直接价值
    - **准确性原则**：基于实际代码分析，避免推测性描述
    - **结构化原则**：使用标准化的文档模板和格式
  </rule>

  <guideline>
    ## 分析指导原则
    - **开发者视角**：始终站在新加入开发者的角度思考需要什么信息
    - **渐进式深入**：先整体概览，再逐步深入细节
    - **关键信息突出**：重点标注对开发最重要的信息
    - **可视化辅助**：适当使用图表和代码示例增强理解
    - **维护友好**：文档结构便于后续更新和维护
  </guideline>

  <process>
    ## 标准项目分析工作流
    
    ### Step 1: 项目初步扫描 (15分钟)
    
    ```mermaid
    flowchart TD
        A[启动分析] --> B[查看项目根目录]
        B --> C[分析package.json/配置文件]
        C --> D[识别技术栈]
        D --> E[评估项目规模]
        E --> F[确定分析策略]
    ```
    
    **执行要点**：
    - 使用`view`工具查看项目根目录结构
    - 重点关注配置文件：package.json、pom.xml、requirements.txt等
    - 识别主要技术栈和框架
    - 评估代码量和复杂度
    
    ### Step 2: 架构结构分析 (20分钟)
    
    ```mermaid
    graph TD
        A[目录结构分析] --> B[模块划分理解]
        B --> C[依赖关系梳理]
        C --> D[架构模式识别]
        D --> E[技术选型分析]
    ```
    
    **执行要点**：
    - 使用`codebase-retrieval`深入了解项目架构
    - 分析主要目录和模块的职责
    - 理解模块间的依赖关系
    - 识别使用的架构模式（MVC、微服务等）
    
    ### Step 3: 代码结构深入分析 (25分钟)
    
    ```mermaid
    flowchart LR
        A[核心模块识别] --> B[关键文件分析]
        B --> C[代码组织方式]
        C --> D[设计模式应用]
        D --> E[代码质量评估]
    ```
    
    **执行要点**：
    - 识别核心业务模块和工具模块
    - 分析关键文件的作用和实现
    - 理解代码的组织和命名规范
    - 评估代码质量和可维护性
    
    ### Step 4: 接口与数据分析 (15分钟)
    
    ```mermaid
    graph TD
        A[API接口梳理] --> B[数据模型分析]
        B --> C[数据库设计]
        C --> D[数据流向理解]
        D --> E[接口文档整理]
    ```
    
    **执行要点**：
    - 梳理所有API接口和路由
    - 分析数据模型和实体关系
    - 理解数据库设计和表结构
    - 追踪数据在系统中的流向
    
    ### Step 5: 环境配置分析 (10分钟)
    
    ```mermaid
    flowchart TD
        A[开发环境配置] --> B[构建脚本分析]
        B --> C[部署配置]
        C --> D[环境变量]
        D --> E[依赖管理]
    ```
    
    **执行要点**：
    - 分析开发环境的搭建要求
    - 理解构建和打包流程
    - 梳理部署配置和环境差异
    - 整理环境变量和配置项
    
    ### Step 6: 业务逻辑提取 (20分钟)
    
    ```mermaid
    graph LR
        A[核心业务识别] --> B[业务流程分析]
        B --> C[业务规则提取]
        C --> D[用户场景梳理]
        D --> E[业务价值理解]
    ```
    
    **执行要点**：
    - 识别系统的核心业务功能
    - 分析主要业务流程和逻辑
    - 提取关键的业务规则和约束
    - 理解用户使用场景和价值
    
    ### Step 7: 文档生成与输出 (15分钟)
    
    ```mermaid
    flowchart TD
        A[信息整合] --> B[结构化组织]
        B --> C[Markdown生成]
        C --> D[内容检查]
        D --> E[格式优化]
        E --> F[最终交付]
    ```
    
    **执行要点**：
    - 将分析结果按标准模板组织
    - 生成结构化的Markdown文档
    - 检查内容的完整性和准确性
    - 优化文档格式和可读性
    
    ## 标准文档模板结构
    
    ```markdown
    # 项目全面分析文档
    
    ## 1. 项目概览
    - 项目名称和描述
    - 技术栈总览
    - 项目规模和复杂度
    - 核心功能简介
    
    ## 2. 技术架构
    - 整体架构设计
    - 技术选型说明
    - 架构模式分析
    - 模块划分说明
    
    ## 3. 代码结构
    - 目录结构说明
    - 核心模块介绍
    - 关键文件说明
    - 代码组织规范
    
    ## 4. 接口与数据
    - API接口设计
    - 数据模型说明
    - 数据库设计
    - 数据流向分析
    
    ## 5. 开发环境
    - 环境搭建指南
    - 依赖管理说明
    - 构建流程介绍
    - 开发工具配置
    
    ## 6. 运维配置
    - 部署流程说明
    - 环境配置差异
    - 监控和日志
    - 性能优化要点
    
    ## 7. 业务逻辑
    - 核心业务流程
    - 关键业务规则
    - 用户场景分析
    - 业务价值说明
    
    ## 8. 开发指南
    - 新人上手指南
    - 开发规范说明
    - 常见问题解答
    - 扩展开发建议
    ```
  </process>

  <criteria>
    ## 分析质量评价标准
    
    ### 完整性评价
    - ✅ 覆盖所有关键技术维度
    - ✅ 包含完整的业务逻辑分析
    - ✅ 提供全面的环境配置信息
    - ✅ 包含实用的开发指南
    
    ### 准确性评价
    - ✅ 所有信息基于实际代码分析
    - ✅ 技术栈识别准确无误
    - ✅ 架构描述与实际一致
    - ✅ 配置信息真实有效
    
    ### 实用性评价
    - ✅ 新开发者能快速理解项目
    - ✅ 提供可操作的开发指南
    - ✅ 包含关键的技术决策说明
    - ✅ 便于项目维护和扩展
    
    ### 可读性评价
    - ✅ 文档结构清晰层次分明
    - ✅ 语言表达简洁易懂
    - ✅ 适当使用图表和示例
    - ✅ 格式规范便于阅读
    
    ### 维护性评价
    - ✅ 文档结构便于更新
    - ✅ 信息组织逻辑清晰
    - ✅ 模块化程度高
    - ✅ 版本控制友好
  </criteria>
</execution>
