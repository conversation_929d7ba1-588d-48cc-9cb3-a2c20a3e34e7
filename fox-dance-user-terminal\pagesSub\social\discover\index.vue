<template>
  <view class="discover-container">
    <!-- 页面加载动画 -->
    <view v-if="loading.page" class="page-loading">
      <view class="loading-container">
        <view class="loading-spinner">
          <u-loading mode="circle" size="40" color="#ff6b87"></u-loading>
        </view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-else>
      <!-- 顶部搜索 -->
      <view class="header">
        <view class="search-bar" @click="goSearch">
          <u-icon name="search" size="18" color="#999"></u-icon>
          <text class="search-placeholder">搜索话题、用户...</text>
        </view>
      </view>

    <scroll-view class="content" scroll-y>
      <!-- 热门话题 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">热门话题</text>
          <text class="more-btn" @click="goTopicList">更多</text>
        </view>

        <!-- 话题加载状态 -->
        <view v-if="loading.topics" class="section-loading">
          <view class="loading-container">
            <u-loading mode="circle" size="32" color="#ff6b87"></u-loading>
            <text class="loading-text">加载话题中...</text>
          </view>
        </view>

        <!-- 话题实际内容 -->
        <view v-else class="topic-grid">
          <view
            v-for="topic in hotTopics"
            :key="topic.id"
            class="topic-card"
            @click="goTopic(topic)"
          >
            <image :src="topic.cover" class="topic-cover" mode="aspectFill" />
            <view class="topic-info">
              <text class="topic-name">#{{ topic.name }}</text>
              <text class="topic-count">{{ topic.postCount }}条帖子</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 推荐用户 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">推荐关注</text>
        </view>

        <!-- 用户加载状态 -->
        <view v-if="loading.users" class="section-loading">
          <view class="loading-container">
            <u-loading mode="circle" size="32" color="#ff6b87"></u-loading>
            <text class="loading-text">加载用户中...</text>
          </view>
        </view>

        <!-- 用户实际内容 -->
        <scroll-view v-else class="user-scroll" scroll-x>
          <view class="user-list">
            <view
              v-for="user in recommendUsers"
              :key="user.id"
              class="user-card"
              @click="goUserProfile(user)"
            >
              <u-avatar :src="user.avatar" size="60"></u-avatar>
              <text class="user-name">{{ user.nickname }}</text>
              <text class="user-desc">{{ user.description }}</text>
              <FollowButton
                :user="user"
                :followed="user.isFollowed"
                size="mini"
                @follow="onUserFollow"
                @change="onFollowChange"
                @click.stop
              />
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 热门帖子 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">热门帖子</text>
        </view>

        <!-- 帖子加载状态 -->
        <view v-if="loading.posts" class="section-loading">
          <view class="loading-container">
            <u-loading mode="circle" size="32" color="#ff6b87"></u-loading>
            <text class="loading-text">加载帖子中...</text>
          </view>
        </view>

        <!-- 帖子实际内容 -->
        <view v-else class="hot-posts">
          <view
            v-for="post in hotPosts"
            :key="post.id"
            class="hot-post-item"
            @click="goPostDetail(post)"
          >
            <view class="post-content">
              <view class="user-info">
                <u-avatar :src="post.userAvatar" size="32"></u-avatar>
                <text class="username">{{ post.username }}</text>
              </view>
              <text class="post-text">{{ post.title || post.content || '无标题' }}</text>
              <view class="post-stats">
                <text class="stat-item">{{ post.likeCount }}赞</text>
                <text class="stat-item">{{ post.commentCount }}评论</text>
              </view>
            </view>
            <image
              v-if="post.coverImage"
              :src="post.coverImage"
              class="post-cover"
              mode="aspectFill"
            />
          </view>
        </view>
      </view>

      <!-- 精选内容 -->
      <!-- <view class="section">
        <view class="section-header">
          <text class="section-title">精选内容</text>
        </view>
        <view class="featured-grid">
          <view
            v-for="item in featuredContent"
            :key="item.id"
            class="featured-item"
            @click="goFeaturedDetail(item)"
          >
            <image :src="item.cover" class="featured-cover" mode="aspectFill" />
            <view class="featured-overlay">
              <text class="featured-title">{{ item.title }}</text>
              <text class="featured-subtitle">{{ item.subtitle }}</text>
            </view>
          </view>
        </view>
      </view>-->
    </scroll-view>
    </view>
  </view>
</template>

<script>
import FollowButton from "../components/FollowButton.vue";
import {
  getHotTopics,
  getHotPosts,
  getRecommendUsers,
  getFeaturedContent,
  batchCheckFollowStatus
} from "@/utils/socialApi.js";

export default {
  name: "SocialDiscover",
  components: {
    FollowButton
  },
  data() {
    return {
      hotTopics: [],
      recommendUsers: [],
      hotPosts: [],
      featuredContent: [],
      loading: {
        page: true,        // 页面整体加载
        topics: true,      // 热门话题加载
        users: true,       // 推荐用户加载
        posts: true,       // 热门帖子加载
        featured: true     // 精选内容加载
      }
    };
  },
  onLoad() {
    this.loadDiscoverData();
  },

  onShow() {
    // 页面显示时检查是否需要加载数据
    if (!this.hotTopics || this.hotTopics.length === 0) {
      console.log("发现页显示时重新加载数据");
      this.loadDiscoverData();
    }
  },

  // 组件激活时重新加载数据（用于keep-alive场景）
  activated() {
    if (!this.hotTopics || this.hotTopics.length === 0) {
      console.log("发现页激活时重新加载数据");
      this.loadDiscoverData();
    }
  },
  methods: {
    // 初始化页面数据
    async loadDiscoverData() {
      try {
        // 设置加载状态
        this.loading = {
          page: true,
          topics: true,
          users: true,
          posts: true,
          featured: true
        }

        // 并行加载数据
        await Promise.all([
          this.loadHotTopics(),
          this.loadRecommendUsers(),
          this.loadHotPosts(),
          this.loadFeaturedContent()
        ])

        // 所有数据加载完成，关闭页面加载状态
        this.loading.page = false

      } catch (error) {
        console.error('页面数据初始化失败:', error)
        this.loading.page = false

        uni.showToast({
          title: '页面加载失败',
          icon: 'none'
        })
      }
    },

    async loadHotTopics() {
      try {
        this.loading.topics = true

        console.log("开始加载热门话题...");
        const result = await getHotTopics({ limit: 4 });
        console.log("热门话题API返回:", result);

        if (result && result.code === 0 && result.data) {
          console.log("🔥 API返回的原始标签数据:", result.data);
          result.data.forEach(tag => {
            console.log(`🔥 标签 ${tag.name} - coverImage: ${tag.coverImage}`);
          });

          this.hotTopics = result.data.map(tag => ({
            id: tag.id,
            name: tag.name,
            cover:
              tag.coverImage +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
            postCount: tag.useCount || 0,
            description: tag.description
          }));
          console.log("🔥 处理后的热门话题:", this.hotTopics);
        } else {
          console.error("热门话题API返回格式不正确:", result);
          // 使用默认数据作为后备
          this.hotTopics = this.getDefaultTopics();
        }

        this.loading.topics = false
      } catch (error) {
        console.error("加载热门话题失败:", error);
        this.loading.topics = false
        // 使用默认数据作为后备
        this.hotTopics = this.getDefaultTopics();
      }
    },

    // 默认话题数据（后备方案）
    getDefaultTopics() {
      return [
        {
          id: 1,
          name: "街舞",
          cover: "https://picsum.photos/200/120?random=1",
          postCount: 1234
        },
        {
          id: 2,
          name: "爵士舞",
          cover: "https://picsum.photos/200/120?random=2",
          postCount: 856
        },
        {
          id: 3,
          name: "芭蕾",
          cover: "https://picsum.photos/200/120?random=3",
          postCount: 642
        },
        {
          id: 4,
          name: "现代舞",
          cover: "https://picsum.photos/200/120?random=4",
          postCount: 789
        }
      ];
    },

    async loadRecommendUsers() {
      try {
        this.loading.users = true

        console.log("开始加载推荐用户...");
        const result = await getRecommendUsers({ limit: 10 });
        console.log("推荐用户API返回:", result);

        if (result && result.code === 0 && result.data) {
          this.recommendUsers = result.data.map(user => ({
            id: user.userId || user.id,
            nickname: user.nickname || "用户",
            avatar: this.formatAvatarUrl(user.avatar),
            description: user.bio || "暂无简介",
            isFollowed: user.isFollowed || false, // 使用后端返回的关注状态
            followersCount: user.followerCount || 0, // 修正字段名：followerCount
            postCount: user.postCount || 0
          }));
          console.log("推荐用户加载成功:", this.recommendUsers);

          // 批量检查关注状态
          this.batchCheckFollowStatus();
        } else {
          console.error("推荐用户API返回格式不正确:", result);
          // 使用默认数据作为后备
          this.recommendUsers = this.getDefaultRecommendUsers();
        }

        this.loading.users = false
      } catch (error) {
        console.error("加载推荐用户失败:", error);
        this.loading.users = false
        // 使用默认数据作为后备
        this.recommendUsers = this.getDefaultRecommendUsers();
      }
    },

    // 默认推荐用户数据（后备方案）
    getDefaultRecommendUsers() {
      return [
        {
          id: 18,
          nickname: "舞蹈达人",
          avatar: "https://picsum.photos/100/100?random=18",
          description: "分享舞蹈技巧和心得",
          isFollowed: false,
          followersCount: 1024,
          postCount: 128
        }
      ];
    },

    async loadHotPosts() {
      try {
        this.loading.posts = true

        console.log("开始加载热门帖子...");
        const result = await getHotPosts({ limit: 4, timeRange: "7d" });
        console.log("热门帖子API返回:", result);

        if (result && result.code === 0 && result.data) {
          const posts = Array.isArray(result.data)
            ? result.data
            : result.data.records || [];
          this.hotPosts = posts.map(post => ({
            id: post.id,
            username: post.nickname || post.username || "用户",
            userAvatar: this.formatAvatarUrl(post.avatar || post.userAvatar),
            title: post.title || "", // 添加标题字段
            content: this.truncateContent(post.title || post.content || ""), // 优先显示标题，没有标题则显示内容
            coverImage: post.coverImage,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            createTime: post.createTime
          }));
          console.log("热门帖子加载成功:", this.hotPosts);
        } else {
          console.error("热门帖子API返回格式不正确:", result);
          // 使用默认数据作为后备
          this.hotPosts = this.getDefaultPosts();
        }

        this.loading.posts = false
      } catch (error) {
        console.error("加载热门帖子失败:", error);
        this.loading.posts = false
        // 使用默认数据作为后备
        this.hotPosts = this.getDefaultPosts();
      }
    },

    // 格式化头像URL
    formatAvatarUrl(avatar) {
      if (!avatar) {
        return "/static/images/toux.png";
      }
      return (
        "https://file.foxdance.com.cn" +
        avatar +
        "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85"
      );
    },

    // 截断内容
    truncateContent(content) {
      if (!content) return "暂无内容";
      return content.length > 50 ? content.substring(0, 50) + "..." : content;
    },

    // 默认帖子数据（后备方案）
    getDefaultPosts() {
      return [
        {
          id: 1,
          username: "舞蹈达人",
          userAvatar: "https://picsum.photos/100/100?random=20",
          content: "今天的舞蹈练习分享，基础动作很重要...",
          coverImage: "https://picsum.photos/120/120?random=30",
          likeCount: 234,
          commentCount: 45
        },
        {
          id: 2,
          username: "街舞教练",
          userAvatar: "https://picsum.photos/100/100?random=21",
          content: "新手入门街舞的几个要点，记得收藏！",
          coverImage: "https://picsum.photos/120/120?random=31",
          likeCount: 189,
          commentCount: 32
        }
      ];
    },

    async loadFeaturedContent() {
      try {
        this.loading.featured = true

        // 模拟异步加载延迟
        await new Promise(resolve => setTimeout(resolve, 500))

        // 模拟精选内容数据
        this.featuredContent = [
          {
            id: 1,
            title: "春日穿搭指南",
            subtitle: "时尚达人教你搭配",
            cover: "https://picsum.photos/300/200?random=40"
          },
          {
            id: 2,
            title: "周末好去处",
            subtitle: "城市探索攻略",
            cover: "https://picsum.photos/300/200?random=41"
          }
        ];

        this.loading.featured = false
      } catch (error) {
        console.error("加载精选内容失败:", error);
        this.loading.featured = false
      }
    },

    onUserFollow(data) {
      console.log("关注操作:", data);
      // 这里可以调用API进行关注/取消关注操作
    },

    onUserFollow(data) {
      console.log("用户关注成功:", data);
      // 更新本地数据
      const user = this.recommendUsers.find(u => u.id === data.user.id);
      if (user) {
        user.isFollowed = true;
        // 可以增加粉丝数
        if (user.followersCount !== undefined) {
          user.followersCount += 1;
        }
      }
    },

    onUserUnfollow(data) {
      console.log("用户取消关注成功:", data);
      // 更新本地数据
      const user = this.recommendUsers.find(u => u.id === data.user.id);
      if (user) {
        user.isFollowed = false;
        // 可以减少粉丝数
        if (user.followersCount !== undefined && user.followersCount > 0) {
          user.followersCount -= 1;
        }
      }
    },

    onFollowChange(data) {
      // 更新本地数据
      const user = this.recommendUsers.find(u => u.id === data.user.id);
      if (user) {
        user.isFollowed = data.isFollowed;
      }
      console.log("关注状态变化:", data);
    },

    goSearch() {
      uni.navigateTo({
        url: "/pagesSub/social/search/index"
      });
    },

    goTopic(topic) {
      uni.navigateTo({
        url: `/pagesSub/social/topic/detail?id=${topic.id}`
      });
    },

    goTopicList() {
      uni.navigateTo({
        url: "/pagesSub/social/topic/list"
      });
    },

    // 强制刷新数据（供父组件调用）
    forceRefresh() {
      console.log("强制刷新发现页数据...");

      // 重置数据
      this.hotTopics = [];
      this.recommendUsers = [];
      this.hotPosts = [];
      this.featuredContent = [];

      // 重置加载状态
      this.loading = {
        page: true,
        topics: true,
        users: true,
        posts: true,
        featured: true
      }

      // 重新加载数据
      this.loadDiscoverData();
    },

    goUserProfile(user) {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${user.id}`
      });
    },

    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    },

    goFeaturedDetail(item) {
      uni.navigateTo({
        url: `/pagesSub/social/featured/detail?id=${item.id}`
      });
    },

    // 批量检查关注状态
    async batchCheckFollowStatus() {
      if (!this.recommendUsers || this.recommendUsers.length === 0) return;

      const currentUserId = uni.getStorageSync("userid");
      if (!currentUserId) return;

      try {
        const userIds = this.recommendUsers
          .map(user => user.id)
          .filter(id => id != currentUserId);
        if (userIds.length === 0) return;

        const result = await batchCheckFollowStatus(userIds);
        console.log("批量检查关注状态结果:", result);

        if (result && result.code === 0 && result.data) {
          // 更新关注状态
          this.recommendUsers.forEach(user => {
            if (result.data[user.id] !== undefined) {
              user.isFollowed = result.data[user.id];
            }
          });
        }
      } catch (error) {
        console.error("批量检查关注状态失败:", error);
      }
    },


  }
};
</script>

<style lang="scss" scoped>
/* 页面加载动画 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f9fa;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  margin-bottom: 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 16rpx;
}

.discover-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 200rpx;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  padding: var(--status-bar-height) 32rpx 24rpx;
  border-bottom: 2rpx solid #e4e7ed;
}

.search-bar {
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
}

.search-placeholder {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;
}

.content {
  margin-top: calc(125rpx + var(--status-bar-height));
  padding: 0 32rpx;
  width: auto;
}

.section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-btn {
  font-size: 28rpx;
  color: #2979ff;
}

.topic-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 22rpx;
}

.topic-card {
  width: calc(50% - 12rpx);
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.topic-cover {
  width: 100%;
  height: 160rpx;
}

.topic-info {
  padding: 24rpx;
}

.topic-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.topic-count {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.user-scroll {
  white-space: nowrap;
}

.user-list {
  display: flex;
  gap: 32rpx;
  padding-bottom: 16rpx;
}

.user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  min-width: 200rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin: 16rpx 0 8rpx;
  text-align: center;
}

.user-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
  text-align: center;
}

.hot-posts {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.hot-post-item {
  display: flex;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.hot-post-item:last-child {
  border-bottom: none;
}

.post-content {
  flex: 1;
  margin-right: 24rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.username {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.post-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 16rpx;
}

.post-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
}

.post-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.featured-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.featured-item {
  position: relative;
  height: 240rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.featured-cover {
  width: 100%;
  height: 100%;
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 32rpx 32rpx;
}

.featured-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  display: block;
}

.featured-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
  display: block;
}

/* 分段加载动画样式 */
.section-loading {
  padding: 80rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.section-loading .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.section-loading .loading-text {
  font-size: 28rpx;
  color: #909399;
}
</style>
