{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/editinformation.vue?39f0", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/editinformation.vue?2ff6", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/editinformation.vue?b7f1", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/editinformation.vue?b696", "uni-app:///pages/mine/editinformation.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/editinformation.vue?6e45", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/editinformation.vue?03cf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loding", "isLogined", "isH5", "avatar", "nickname", "rek", "imgbaseUrl", "baseUrl_admin", "qj<PERSON>ton", "is_store", "created", "onLoad", "methods", "getuserinfo", "console", "chooseAvatarsc", "uni", "title", "driver", "that", "logoutTap", "content", "success", "icon", "duration", "setTimeout", "userData", "UploadImg", "count", "sizeType", "sourceType", "sctxTap", "url", "filePath", "header", "name", "formData", "tjxxTap", "introduction"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AAC4L;AAC5L,gBAAgB,6LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACiC7vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IACA;IACAC;MACA;MACA;MACA;MACAC;QACAC;MACA;MACA;QAAAC;MAAA;QACAJ;QACA;UACAE;UACAG;QACA;MACA;IAEA;IACA;IACAC;MACA;MACAJ;QACAC;QACAI;QACAC;UACA;YACAN;YACAA;YACAA;cACAO;cACAN;cACAO;YACA;YACAC;cACAT;YACA;UACA;YACAF;UACA;QACA;MACA;IACA;IACA;IACAY;MACAV;QACAC;MACA;MACA;MACA;QACA;UACAH;UACAK;UACA;UACAA;UACAA;UACAA;UACAA;UACAH;QACA;MACA;IACA;IACA;IACAW;MACA;MACAX;QACAY;QACAC;QACAC;QACAR;UACA;UACAN;YACAC;UACA;UACA;YACAH;YACA;cACAE;cACAG;YACA;UACA;QAEA;MACA;IAEA;IACA;IACAY;MAEA;;MAMA;MACAf;QACAY;QAAA;QACAN;UACA;UACAN;YACA;YACAgB;YACAC;YACAC;cACA;cACA;YACA;YACAC;YACAC;cACA;YACA;YACAd;cACAR;cACA;cACA;gBACAK;gBACAL;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAuB;MACA;QACArB;UACAO;UACAN;UACAO;QACA;QACA;MACA;MACAR;QACAC;MACA;MACA;QACA;QACAd;QACAC;QACAkC;MACA;QACA;UACAtB;UACAA;YACAC;YACAO;UACA;UACAC;YACAT;UACA;QACA,QAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChOA;AAAA;AAAA;AAAA;AAAw1C,CAAgB,ytCAAG,EAAC,C;;;;;;;;;;;ACA52C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/editinformation.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/editinformation.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./editinformation.vue?vue&type=template&id=25e48fc2&\"\nvar renderjs\nimport script from \"./editinformation.vue?vue&type=script&lang=js&\"\nexport * from \"./editinformation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./editinformation.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/editinformation.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editinformation.vue?vue&type=template&id=25e48fc2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editinformation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editinformation.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"editinformation\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"loding\">\r\n\t\t\r\n\t\t<view class=\"edi_ban\"><image :src=\"baseUrl_admin + '/static/images/icon23.jpg'\"></image></view>\r\n\t\t<view class=\"edi_one\">\r\n\t\t\t <!-- @click=\"UploadImg\" -->\r\n\t\t\t<image :src=\"avatar == '' ? '/static/images/toux.png' : imgbaseUrl + avatar\" mode=\"aspectFill\" class=\"edma_one_a\"></image>\r\n\t\t\t<text>点击修改头像</text>\r\n\t\t\t<button class=\"btn\" open-type=\"chooseAvatar\" @chooseavatar=\"chooseAvatarsc\">获取头像</button>\r\n\t\t</view>\r\n\t\t<!-- <button class=\"btn\" open-type=\"chooseAvatar\" @chooseavatar=\"chooseAvatarsc\">获取头像</button> -->\r\n\t\t<!-- <button class=\"btn\" open-type=\"getUserInfo\" @getuserinfo=\"getuserinfo\">获取头像昵称1</button> -->\r\n\t\t<!-- <input type=\"nickname\" v-model=\"nickname\" /> -->\r\n\t\t<view class=\"edma_two\">\r\n\t\t    <view class=\"edma_two_li\">\r\n\t\t        <view>昵称：</view>\r\n\t\t        <input type=\"nickname\" placeholder=\"请输入昵称\" :disabled=\"is_store > 0 ? true : false\" v-model=\"nickname\" maxlength=\"8\" placeholder-style=\"color:#999999;font-size:28rpx\" />\r\n\t\t    </view>\r\n\t\t\t<view class=\"edma_two_li\">\r\n\t\t\t    <view>个人简介：</view>\r\n\t\t\t    <input type=\"text\" placeholder=\"请输入个人简介\" v-model=\"rek\" placeholder-style=\"color:#999999;font-size:28rpx\" style=\"width:calc(100% - 146rpx);\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"edma_thr\">\r\n\t\t\t<view @click=\"tjxxTap\">提交修改</view>\r\n\t\t\t<view @click=\"logoutTap\">退出登录</view>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tupImg,\r\n\tuserInfoApi,\r\n\ttoeditUserApi\r\n} from '@/config/http.achieve.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloding:false,\r\n\t\t\tisLogined:false,//是否登录\r\n\t\t\tisH5:false,//是否是h5\r\n\t\t\tavatar:'',//头像\r\n\t\t\tnickname:'',//昵称\r\n\t\t\trek:'',\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tbaseUrl_admin:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t\tis_store:0,\r\n\t\t}\r\n\t},\r\n\tcreated(){\r\n\t\t\r\n\t},\r\n\tonLoad(options) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.userData();//个人信息\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.baseUrl_admin = this.$baseUrl_admin;\r\n\t},\r\n\tmethods: {\r\n\t\t//获取头像昵称\r\n\t\tgetuserinfo(event){\r\n\t\t\tconsole.log(event,'获取头像昵称')\r\n\t\t},\r\n\t\t//获取头像\r\n\t\tchooseAvatarsc(event){\r\n\t\t  // console.log(event,'event')\r\n\t\t  var that = this;\r\n\t\t  const tempFilePaths = event.detail.avatarUrl\r\n\t\t  uni.showLoading({\r\n\t\t  \ttitle:'加载中'\r\n\t\t  })\r\n\t\t  upImg(tempFilePaths, 'file',{driver:'cos'}).then(ress => {\r\n\t\t  \tconsole.log('上传图片',ress)\r\n\t\t  \tif (ress.code == 1) {\r\n\t\t  \t\tuni.hideLoading();\r\n\t\t  \t\tthat.avatar = ress.data.file.url\r\n\t\t  \t}\r\n\t\t  })\r\n\t\t  \r\n\t\t},\r\n\t\t//退出登录\r\n\t\tlogoutTap(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle:'温馨提示',\r\n\t\t\t\tcontent:'确定要退出登录吗？',\r\n\t\t\t\tsuccess: function (rep) {\r\n\t\t\t\t\tif (rep.confirm) {\r\n\t\t\t\t\t\tuni.removeStorageSync('token');\r\n\t\t\t\t\t\tuni.removeStorageSync('userid');\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\t\ttitle:'退出成功',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t} else if (rep.cancel) {\r\n\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tconsole.log('个人信息',res);\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\t// res.data.is_store = 0\r\n\t\t\t\t\tthat.is_store = res.data.is_store;\r\n\t\t\t\t\tthat.avatar = res.data.avatar;\r\n\t\t\t\t\tthat.nickname = res.data.nickname == '' ? '微信昵称' : res.data.nickname;\r\n\t\t\t\t\tthat.rek = res.data.introduction ? res.data.introduction : '';\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 更换头像\r\n\t\tUploadImg() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: 1,\r\n\t\t\t\tsizeType: ['original', 'compressed'],\r\n\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconst tempFilePaths = res.tempFilePaths[0]\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle:'加载中'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tupImg(tempFilePaths, 'file', ).then(ress => {\r\n\t\t\t\t\t\tconsole.log('上传图片',ress)\r\n\t\t\t\t\t\tif (ress.code == 1) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tthat.avatar = ress.data.file.url\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\r\n\t\t},\r\n\t\t//上传头像\r\n\t\tsctxTap(){\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t// var DeviceType = 'wxapp';\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef H5\r\n\t\t\t// var DeviceType = 'mobile';\r\n\t\t\t// #endif\r\n\t\t\tvar that = this;\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount:1, //默认9\r\n\t\t\t\tsuccess: (chooseImageRes) => {\r\n\t\t\t\t\tconst tempFilePaths = chooseImageRes.tempFilePaths;\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\t//url: 'https://wx.auxphen.com/api/home/<USER>/one', //仅为示例，非真实的接口地址\r\n\t\t\t\t\t\turl:that.imgbaseUrl + '/api/ajax/upload',\r\n\t\t\t\t\t\tfilePath: tempFilePaths[0],\r\n\t\t\t\t\t\theader:{\r\n\t\t\t\t\t\t\t'server':1,\r\n\t\t\t\t\t\t\t'bausertoken': uni.getStorageSync('token'),\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\t'user': 'test'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\tconsole.log(uploadFileRes);\r\n\t\t\t\t\t\t\tvar res = JSON.parse(uploadFileRes.data)\r\n\t\t\t\t\t\t\tif(res.code = 1){\r\n\t\t\t\t\t\t\t\tthat.avatar = res.data.fullurl;\r\n\t\t\t\t\t\t\t\tconsole.log(res,'嘻嘻嘻哈哈123')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//信息提交\r\n\t\ttjxxTap(){\r\n\t\t\tif (this.nickname.split(\" \").join(\"\").length == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t\ttitle: '请输入昵称',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\ttoeditUserApi({\r\n\t\t\t\t// userId:uni.getStorageSync('user').userId,\r\n\t\t\t\tavatar:this.avatar,\r\n\t\t\t\tnickname:this.nickname,\r\n\t\t\t\tintroduction:this.rek\r\n\t\t\t}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'修改成功',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tuni.navigateBack({})\r\n\t\t\t\t\t},1000);\r\n\t\t\t\t}else{\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n\r\n<style lang=\"less\">\r\n.editinformation{\r\n\toverflow:hidden;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editinformation.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editinformation.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751619119\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}