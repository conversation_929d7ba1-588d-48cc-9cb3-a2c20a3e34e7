<template>
  <view class="user-profile-container">
    <scroll-view class="content" scroll-y>
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <u-loading mode="circle" size="24"></u-loading>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 用户信息 -->
      <view v-else class="user-section">
        <view class="user-info">
          <u-avatar :src="userInfo.avatar" size="80"></u-avatar>
          <view class="user-details">
            <text class="nickname">{{ userInfo.nickname || '用户' }}</text>
            <text class="user-id">ID: {{ userInfo.userId }}</text>
            <text class="bio">{{ userInfo.bio || '这个人很懒，什么都没有留下...' }}</text>
          </view>
          <view class="action-buttons">
            <view
              class="custom-btn follow-btn"
              :class="{ 'followed': userInfo.isFollowed }"
              @click="toggleFollow"
            >{{ userInfo.isFollowed ? '已关注' : '关注' }}</view>
            <view class="custom-btn chat-btn" @click="goToChat">私信</view>
          </view>
        </view>

        <!-- 数据统计 -->
        <view class="stats-section">
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.postCount }}</text>
            <text class="stat-label">帖子</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.followingCount }}</text>
            <text class="stat-label">关注</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.followersCount }}</text>
            <text class="stat-label">粉丝</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.likeCount }}</text>
            <text class="stat-label">获赞</text>
          </view>
        </view>
      </view>

      <!-- 用户帖子 -->
      <view class="posts-section">
        <view class="section-header">
          <text class="section-title">TA的帖子</text>
        </view>

        <view class="post-grid">
          <PostCard
            v-for="post in userPosts"
            :key="post.id"
            :post="post"
            class="post-card-item"
            @click="goPostDetail"
            @user-click="goUserProfile"
            @like="onPostLike"
          />
        </view>

        <!-- 空状态 -->
        <view v-if="!userPosts.length" class="empty-state">
          <u-icon name="file-text" color="#ccc" size="60"></u-icon>
          <text class="empty-text">暂无帖子</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import PostCard from "../components/PostCard.vue";
import {
  getUserProfile,
  getUserPosts,
  followUser,
  unfollowUser,
  checkFollowStatus,
  likePost,
  unlikePost
} from "@/utils/socialApi.js";

export default {
  name: "UserProfile",
  components: {
    PostCard
  },
  data() {
    return {
      userId: "",
      userInfo: {
        userId: "",
        nickname: "",
        avatar: "",
        bio: "",
        postCount: 0,
        followingCount: 0,
        followersCount: 0,
        likeCount: 0,
        isFollowed: false
      },
      userPosts: [],
      loading: true
    };
  },
  onLoad(options) {
    // 兼容不同的参数名：id 或 userId
    this.userId = options.id || options.userId;
    console.log("用户资料页面 - 接收到的参数:", options);
    console.log("用户资料页面 - 用户ID:", this.userId);

    if (!this.userId) {
      uni.showToast({
        title: "用户ID不能为空",
        icon: "none"
      });
      return;
    }

    this.loadUserInfo();
    this.loadUserPosts();
  },
  methods: {
    async loadUserInfo() {
      try {
        // 加载用户资料
        const userProfile = await getUserProfile(this.userId);
        console.log("userProfile:", userProfile);
        if (userProfile) {
          this.userInfo = {
            id: userProfile.data.userId,
            userId: userProfile.data.userId,
            nickname: userProfile.data.nickname || "无名氏",
            avatar:
              "https://file.foxdance.com.cn" +
              userProfile.data.avatar +
              "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
            bio: userProfile.data.bio || "这个人很懒，什么都没有留下...",
            danceType: userProfile.data.danceType || "街舞",
            postCount: userProfile.data.postCount || 0,
            followingCount: userProfile.data.followingCount || 0,
            followersCount: userProfile.data.followerCount || 0,
            likeCount: userProfile.data.likeReceivedCount || 0,
            isFollowed: userProfile.data.isFollowed || false
          };
        }

        // 检查关注状态
        const followStatus = await checkFollowStatus(this.userId);
        console.log("关注状态检查结果:", followStatus);

        if (followStatus && followStatus.code === 0 && followStatus.data) {
          this.userInfo.isFollowed = followStatus.data.isFollowing || false;
          // 同时更新粉丝数和关注数
          if (followStatus.data.followerCount !== undefined) {
            this.userInfo.followersCount = followStatus.data.followerCount;
          }
          if (followStatus.data.followingCount !== undefined) {
            this.userInfo.followingCount = followStatus.data.followingCount;
          }
        } else {
          this.userInfo.isFollowed = false;
        }

        this.loading = false;
      } catch (error) {
        console.error("加载用户信息失败:", error);
        uni.showToast({
          title: "用户信息加载失败",
          icon: "none"
        });
        // 加载失败，返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    async loadUserPosts() {
      console.log("开始加载用户帖子，用户ID:", this.userId);
      try {
        // 获取当前登录用户ID
        const currentUserId = uni.getStorageSync("userid");

        // 使用专门的getUserPosts API
        const result = await getUserPosts(this.userId, {
          current: 1,
          pageSize: 20,
          currentUserId: currentUserId || undefined
        });

        console.log("用户帖子API返回结果:", result);

        // 检查API返回格式：{code: 0, data: {records: [...], total: ...}}
        if (result && result.code === 0 && result.data && result.data.records) {
          this.userPosts = result.data.records.map(post => ({
            id: post.id,
            title: post.title || "",
            coverImage: post.coverImage,
            username: post.nickname || this.userInfo.nickname,
            userAvatar: "https://file.foxdance.com.cn" + post.avatar + 'imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85',
            content: post.content,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            createTime: new Date(post.createTime)
          }));
          console.log("用户帖子加载成功，数量:", this.userPosts.length);
        } else {
          console.log("没有找到用户帖子数据");
          this.userPosts = [];
        }
      } catch (error) {
        console.error("加载用户帖子失败:", error);
        uni.showToast({
          title: "帖子加载失败",
          icon: "none"
        });
        this.userPosts = [];
      }
    },

    async toggleFollow() {
      try {
        if (this.userInfo.isFollowed) {
          // 取消关注
          await unfollowUser(this.userId);
          this.userInfo.isFollowed = false;
          this.userInfo.followersCount = Math.max(
            0,
            this.userInfo.followersCount - 1
          );
          uni.showToast({
            title: "取消关注",
            icon: "none"
          });
        } else {
          // 关注用户
          await followUser(this.userId);
          this.userInfo.isFollowed = true;
          this.userInfo.followersCount += 1;
          uni.showToast({
            title: "关注成功",
            icon: "success"
          });
        }
      } catch (error) {
        console.error("关注操作失败:", error);
        uni.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },

    async onPostLike(post) {
      try {
        if (post.isLiked) {
          // 取消点赞
          await unlikePost(post.id);
          post.isLiked = false;
          post.likeCount = Math.max(0, post.likeCount - 1);
        } else {
          // 点赞
          await likePost(post.id);
          post.isLiked = true;
          post.likeCount += 1;
        }

        // 更新帖子列表中的数据
        const index = this.userPosts.findIndex(p => p.id === post.id);
        if (index !== -1) {
          this.$set(this.userPosts, index, { ...post });
        }
      } catch (error) {
        console.error("点赞操作失败:", error);
        uni.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },

    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    },

    goToChat() {
      if (!this.userInfo.userId) {
        uni.showToast({
          title: "用户信息错误",
          icon: "none"
        });
        return;
      }

      console.log("跳转到聊天页面，目标用户ID:", this.userInfo.userId);

      // 跳转到聊天详情页面，传递目标用户ID和用户信息
      uni.navigateTo({
        url: `/pagesSub/social/chat/detail?userId=${
          this.userInfo.userId
        }&nickname=${encodeURIComponent(
          this.userInfo.nickname
        )}&avatar=${encodeURIComponent(this.userInfo.avatar)}`
      });
    },

    goUserProfile(post) {
      // 如果是当前用户，不需要跳转
      if (post.userId === this.userId) return;

      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${post.userId || post.id}`
      });
    },

    goBack() {
      uni.navigateBack();
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ["举报用户", "拉黑用户"],
        success: res => {
          console.log("更多操作:", res.tapIndex);
        }
      });
    },
    // 分享给好友
    onShareAppMessage() {
      return {
        title: "用户资料",
        path: "/pagesSub/social/user/profile"
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.user-profile-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.content {
  padding: 16px;
  width: auto;
}

.user-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.user-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.user-details {
  flex: 1;
  margin-left: 16px;
  margin-right: 12px;
}

/* 按钮组样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
  min-width: 80px;
}

/* 自定义按钮样式 */
.custom-btn {
  min-width: 80px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.follow-btn {
  background: #2979ff;
  color: #fff;
  border-color: #2979ff;
}

.follow-btn.followed {
  background: #f8f9fa;
  color: #666;
  border-color: #e4e7ed;
}

.chat-btn {
  background: #f8f9fa;
  color: #666;
  border-color: #e4e7ed;
}

.custom-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.nickname {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.user-id {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 8px;
}

.bio {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: block;
}

.stats-section {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.posts-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.post-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.post-card-item {
  width: calc(50% - 4px);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 12px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 12px;
}
</style>
