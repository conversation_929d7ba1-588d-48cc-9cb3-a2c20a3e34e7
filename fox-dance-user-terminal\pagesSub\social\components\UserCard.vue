<template>
  <view class="user-card" @click="goUserProfile">
    <!-- 用户头像 -->
    <view class="avatar-container">
      <u-avatar 
        :src="user.avatar" 
        size="80"
      ></u-avatar>
      
      <!-- 认证标识 -->
      <view v-if="user.isVerified" class="verified-badge">
        <u-icon name="checkmark-circle-fill" color="#ff6b87" size="16"></u-icon>
      </view>
    </view>

    <!-- 用户信息 -->
    <view class="user-info">
      <view class="user-name">
        <text class="nickname">{{ user.nickname || user.username || '用户' }}</text>
        <text v-if="user.username && user.nickname" class="username">@{{ user.username }}</text>
      </view>
      
      <view v-if="user.bio" class="user-bio">
        <text class="bio-text">{{ user.bio }}</text>
      </view>
      
      <!-- 用户统计 -->
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ formatNumber(user.postCount || 0) }}</text>
          <text class="stat-label">帖子</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ formatNumber(user.followerCount || 0) }}</text>
          <text class="stat-label">粉丝</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ formatNumber(user.followingCount || 0) }}</text>
          <text class="stat-label">关注</text>
        </view>
      </view>
    </view>

    <!-- 关注按钮 -->
    <view class="action-container">
      <FollowButton
        :user="user"
        :followed="user.isFollowed"
        size="mini"
        @click.stop
        @follow-change="onFollowChange"
      />
    </view>
  </view>
</template>

<script>
import FollowButton from './FollowButton.vue';

export default {
  name: "UserCard",
  components: {
    FollowButton
  },
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  methods: {
    // 跳转到用户主页
    goUserProfile() {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?userId=${this.user.id || this.user.userId}`
      });
    },

    // 关注状态变化回调
    onFollowChange(isFollowed) {
      // 更新用户的关注状态
      this.user.isFollowed = isFollowed;

      // 更新粉丝数
      if (isFollowed) {
        this.user.followerCount = (this.user.followerCount || 0) + 1;
      } else {
        this.user.followerCount = Math.max((this.user.followerCount || 0) - 1, 0);
      }
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
      }
      return num.toString();
    }
  }
};
</script>

<style lang="scss" scoped>
.user-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.verified-badge {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  background-color: #fff;
  border-radius: 50%;
  padding: 2rpx;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin-bottom: 8rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 8rpx;
}

.username {
  font-size: 28rpx;
  color: #909399;
}

.user-bio {
  margin-bottom: 12rpx;
}

.bio-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.user-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #909399;
  margin-top: 4rpx;
}

.action-container {
  margin-left: 16rpx;
  flex-shrink: 0;
}
</style>
