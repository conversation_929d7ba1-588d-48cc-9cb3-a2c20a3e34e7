package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.model.dto.search.SearchRequest;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.model.entity.Post;
import com.yupi.springbootinit.model.entity.UserStats;
import com.yupi.springbootinit.model.vo.SearchResultVO;
import com.yupi.springbootinit.service.BaUserService;
import com.yupi.springbootinit.service.PostService;
import com.yupi.springbootinit.service.SearchService;
import com.yupi.springbootinit.service.TagService;
import com.yupi.springbootinit.service.FollowService;
import com.yupi.springbootinit.service.UserStatsService;
import com.yupi.springbootinit.model.entity.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 搜索服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@Slf4j
public class SearchServiceImpl implements SearchService {

    @Resource
    private PostService postService;

    @Resource
    private BaUserService baUserService;

    @Resource
    private TagService tagService;

    @Resource
    private FollowService followService;

    @Resource
    private UserStatsService userStatsService;

    // 模拟热门搜索词缓存
    private static final List<String> HOT_KEYWORDS = Arrays.asList(
            "街舞", "现代舞", "芭蕾", "拉丁舞", "爵士舞", "民族舞", "古典舞", "舞蹈教学", "舞蹈比赛", "舞蹈培训");

    // 模拟用户搜索历史缓存（实际应该使用Redis）
    private static final Map<Long, List<String>> USER_SEARCH_HISTORY = new HashMap<>();

    @Override
    public SearchResultVO comprehensiveSearch(SearchRequest searchRequest) {
        long startTime = System.currentTimeMillis();

        SearchResultVO result = new SearchResultVO();
        result.setKeyword(searchRequest.getKeyword());

        String keyword = searchRequest.getKeyword();
        String type = searchRequest.getType();
        Integer current = searchRequest.getCurrent();
        Integer size = searchRequest.getSize();
        Long currentUserId = searchRequest.getUserId();

        try {
            // 根据搜索类型进行不同的搜索
            if ("all".equals(type) || StringUtils.isBlank(type)) {
                // 综合搜索
                result.setPosts(searchPosts(keyword, current, size));
                result.setUsers(searchUsers(keyword, 1, 5, currentUserId)); // 用户结果较少
                result.setTags(searchTags(keyword, 1, 5)); // 话题结果较少
            } else if ("post".equals(type)) {
                // 只搜索帖子
                result.setPosts(searchPosts(keyword, current, size));
            } else if ("user".equals(type)) {
                // 只搜索用户
                result.setUsers(searchUsers(keyword, current, size, currentUserId));
            } else if ("tag".equals(type)) {
                // 只搜索话题
                result.setTags(searchTags(keyword, current, size));
            }

            // 计算总数量
            long totalCount = 0;
            if (result.getPosts() != null)
                totalCount += result.getPosts().size();
            if (result.getUsers() != null)
                totalCount += result.getUsers().size();
            if (result.getTags() != null)
                totalCount += result.getTags().size();

            result.setTotalCount(totalCount);
            result.setHasMore(totalCount >= size);

            // 更新搜索统计
            updateSearchStats(keyword, searchRequest.getUserId());

        } catch (Exception e) {
            log.error("综合搜索异常 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            // 返回空结果
            result.setPosts(new ArrayList<>());
            result.setUsers(new ArrayList<>());
            result.setTags(new ArrayList<>());
            result.setTotalCount(0L);
            result.setHasMore(false);
        }

        long endTime = System.currentTimeMillis();
        result.setSearchTime(endTime - startTime);

        return result;
    }

    /**
     * 搜索帖子
     */
    private List<SearchResultVO.PostSearchVO> searchPosts(String keyword, Integer current, Integer size) {
        try {
            QueryWrapper<Post> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("content", keyword)
                    .or()
                    .like("title", keyword)
                    .eq("is_public", 1)
                    .orderByDesc("create_time");

            Page<Post> page = new Page<>(current, size);
            Page<Post> postPage = postService.page(page, queryWrapper);

            return postPage.getRecords().stream().map(post -> {
                SearchResultVO.PostSearchVO postVO = new SearchResultVO.PostSearchVO();
                postVO.setId(post.getId());
                postVO.setContent(post.getContent());
                postVO.setCoverImage(post.getCoverImage());

                // 获取用户信息
                BaUser user = baUserService.getById(post.getUserId());
                if (user != null) {
                    postVO.setUsername(user.getNickname());
                    postVO.setUserAvatar(user.getAvatar());
                }

                postVO.setLikeCount(post.getLikeCount());
                postVO.setCommentCount(post.getCommentCount());

                // 格式化时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                postVO.setCreateTime(sdf.format(post.getCreateTime()));

                return postVO;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("搜索帖子异常 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 搜索用户
     */
    private List<SearchResultVO.UserSearchVO> searchUsers(String keyword, Integer current, Integer size,
            Long currentUserId) {
        try {
            QueryWrapper<BaUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("nickname", keyword)
                    .or()
                    .like("bio", keyword)
                    .or()
                    .like("dance_type", keyword)
                    .orderByDesc("create_time");

            Page<BaUser> page = new Page<>(current, size);
            Page<BaUser> userPage = baUserService.page(page, queryWrapper);

            return userPage.getRecords().stream().map(user -> {
                SearchResultVO.UserSearchVO userVO = new SearchResultVO.UserSearchVO();
                userVO.setUserId(user.getId() != null ? user.getId().longValue() : null);
                userVO.setNickname(user.getNickname());
                userVO.setAvatar(user.getAvatar());
                userVO.setBio(user.getBio());
                userVO.setDanceType(user.getDanceType());

                // 获取用户统计数据
                Long userId = user.getId() != null ? user.getId().longValue() : null;
                if (userId != null) {
                    try {
                        // 获取用户统计信息
                        UserStats userStats = userStatsService.getUserStats(userId);
                        if (userStats != null) {
                            userVO.setPostCount(userStats.getPostCount() != null ? userStats.getPostCount() : 0);
                            userVO.setFollowerCount(
                                    userStats.getFollowerCount() != null ? userStats.getFollowerCount() : 0);
                            userVO.setFollowingCount(
                                    userStats.getFollowingCount() != null ? userStats.getFollowingCount() : 0);
                        } else {
                            userVO.setPostCount(0);
                            userVO.setFollowerCount(0);
                            userVO.setFollowingCount(0);
                        }

                        // 获取关注状态
                        if (currentUserId != null && !currentUserId.equals(userId)) {
                            boolean isFollowed = followService.isFollowing(currentUserId, userId);
                            userVO.setIsFollowed(isFollowed);
                        } else {
                            userVO.setIsFollowed(false);
                        }
                    } catch (Exception e) {
                        log.error("获取用户统计数据异常 - userId: {}, error: {}", userId, e.getMessage());
                        userVO.setPostCount(0);
                        userVO.setFollowerCount(0);
                        userVO.setFollowingCount(0);
                        userVO.setIsFollowed(false);
                    }
                } else {
                    userVO.setPostCount(0);
                    userVO.setFollowerCount(0);
                    userVO.setFollowingCount(0);
                    userVO.setIsFollowed(false);
                }

                return userVO;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("搜索用户异常 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 搜索话题
     */
    private List<SearchResultVO.TagSearchVO> searchTags(String keyword, Integer current, Integer size) {
        try {
            QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("name", keyword)
                    .or()
                    .like("description", keyword)
                    .eq("is_delete", 0)
                    .orderByDesc("use_count")
                    .orderByDesc("create_time");

            Page<Tag> page = new Page<>(current, size);
            Page<Tag> tagPage = tagService.page(page, queryWrapper);

            return tagPage.getRecords().stream().map(tag -> {
                SearchResultVO.TagSearchVO tagVO = new SearchResultVO.TagSearchVO();
                tagVO.setTagId(tag.getId());
                tagVO.setTagName(tag.getName());
                tagVO.setDescription(tag.getDescription());
                tagVO.setCoverImage(tag.getCoverImage());
                tagVO.setPostCount(tag.getUseCount() != null ? tag.getUseCount() : 0);

                // TODO: 获取话题关注数和用户关注状态
                tagVO.setFollowCount(0);
                tagVO.setIsFollowed(false);

                return tagVO;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("搜索话题异常 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getHotKeywords(Integer limit) {
        try {
            // 实际应该从数据库或缓存中获取热门搜索词
            return HOT_KEYWORDS.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取热门搜索词异常 - error: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getSearchHistory(Long userId, Integer limit) {
        try {
            List<String> history = USER_SEARCH_HISTORY.getOrDefault(userId, new ArrayList<>());
            return history.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取搜索历史异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean saveSearchHistory(Long userId, String keyword) {
        try {
            List<String> history = USER_SEARCH_HISTORY.computeIfAbsent(userId, k -> new ArrayList<>());

            // 移除已存在的相同关键词
            history.remove(keyword);

            // 添加到最前面
            history.add(0, keyword);

            // 限制历史记录数量
            if (history.size() > 50) {
                history = history.subList(0, 50);
                USER_SEARCH_HISTORY.put(userId, history);
            }

            return true;
        } catch (Exception e) {
            log.error("保存搜索历史异常 - userId: {}, keyword: {}, error: {}", userId, keyword, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean clearSearchHistory(Long userId) {
        try {
            USER_SEARCH_HISTORY.remove(userId);
            return true;
        } catch (Exception e) {
            log.error("清空搜索历史异常 - userId: {}, error: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteSearchHistory(Long userId, String keyword) {
        try {
            List<String> history = USER_SEARCH_HISTORY.get(userId);
            if (history != null) {
                history.remove(keyword);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除搜索历史异常 - userId: {}, keyword: {}, error: {}", userId, keyword, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<String> getSearchSuggestions(String keyword, Integer limit) {
        try {
            // 基于热门搜索词生成建议
            return HOT_KEYWORDS.stream()
                    .filter(hotKeyword -> hotKeyword.contains(keyword))
                    .limit(limit)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取搜索建议异常 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public void updateSearchStats(String keyword, Long userId) {
        try {
            // TODO: 实际应该更新搜索统计到数据库
            log.debug("更新搜索统计 - keyword: {}, userId: {}", keyword, userId);
        } catch (Exception e) {
            log.error("更新搜索统计异常 - keyword: {}, userId: {}, error: {}", keyword, userId, e.getMessage(), e);
        }
    }
}
