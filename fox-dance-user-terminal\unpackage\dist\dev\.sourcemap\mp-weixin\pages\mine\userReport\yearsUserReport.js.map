{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/yearsUserReport.vue?5c9c", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/yearsUserReport.vue?c3f6", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/yearsUserReport.vue?73d9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/yearsUserReport.vue?a208", "uni-app:///pages/mine/userReport/yearsUserReport.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/yearsUserReport.vue?0f2a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/yearsUserReport.vue?f2d7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgbaseUrl", "imgbaseUrlOss", "safeAreaTop", "menuButtonInfoHeight", "loding", "isLogined", "currentYear", "swiperIndex", "configDate", "oneAni1", "oneAni2", "oneAni3", "twoAni0", "twoAni1", "twoAni2", "twoAni3", "twoAni4", "thrAni1", "thrAni2", "thrAni3", "thrAni4", "thrAni5", "fouAni1", "fouAni2", "fouAni3", "fouAni4", "fouAni5", "fouAni6", "userReport", "onShow", "onLoad", "methods", "yearUserReportData", "uni", "title", "id", "console", "that", "cshData", "setTimeout", "swiper<PERSON><PERSON>e", "swiperEnd", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACsF5wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;MAEA;MACAC;IACA;EACA;EACAC;IACA;IACA;EAGA;EACAC;IACA;IACA;IACA;EAEA;;EACAC;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QAAAC;MAAA;QACAC;QACA;UACAH;UACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;;UAGAI;UACAA;UACAA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAD;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;MAEA;QACAH;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;MAEA;QACAA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;MAEA;QACAA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;IAEA;IACA;IACAI;MACA;IAAA,CACA;IACAC;MACAT;QACAU;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAAm5C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACAv6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/userReport/yearsUserReport.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/userReport/yearsUserReport.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./yearsUserReport.vue?vue&type=template&id=45150510&\"\nvar renderjs\nimport script from \"./yearsUserReport.vue?vue&type=script&lang=js&\"\nexport * from \"./yearsUserReport.vue?vue&type=script&lang=js&\"\nimport style0 from \"./yearsUserReport.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/userReport/yearsUserReport.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yearsUserReport.vue?vue&type=template&id=45150510&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-navbar/u-navbar\" */ \"@/components/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.swiperIndex = 1\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yearsUserReport.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yearsUserReport.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"userReport\" v-if=\"loding\">\r\n\t\t\r\n\t\t<u-navbar back-icon-color=\"#fff\" back-icon-size=\"42\" title=\"用户报告\" background=\"none\" :border-bottom=\"false\" title-color=\"#fff\" title-size=\"32\">\r\n\t\t</u-navbar>\r\n\t\t\r\n\t\t<view class=\"yueb years_yueb\">\r\n\t\t\t<swiper class=\"swiper\" :current=\"swiperIndex\" :vertical=\"true\" @change=\"swiperChange\" @animationfinish=\"swiperEnd\">\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item years_one\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon8.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<view class=\"years_one_a animate__animated animate__bounceInDown\" v-if=\"configDate.oneAni1\">{{currentYear}}*</view>\r\n\t\t\t\t\t\t<view class=\"years_one_b animate__animated animate__bounceInDown\" v-if=\"configDate.oneAni2\">年度报告</view>\r\n\t\t\t\t\t\t<view class=\"years_one_c animate__animated animate__bounceInDown\" @click=\"swiperIndex = 1\" v-if=\"configDate.oneAni3\">回顾心动时刻 →</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item years_two\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon9.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<view class=\"years_two\" v-if=\"userReport.appointment_num*1 > 0\">\r\n\t\t\t\t\t\t\t<view class=\"years_two_a animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni0\">今年你一共约课</view>\r\n\t\t\t\t\t\t\t<view class=\"years_two_b animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni1\" style=\"margin:10rpx 0;\"><text>{{userReport.appointment_num*1}}</text>次</view>\r\n\t\t\t\t\t\t\t<view class=\"years_two_b animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni2\"><text>{{userReport.total_time*1}}</text>分钟</view>\r\n\t\t\t\t\t\t\t<view class=\"years_two_d animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni3\">最近一次你上的课程是</view>\r\n\t\t\t\t\t\t\t<view class=\"years_two_e animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni4\">{{userReport.last_course.teacher.name}}的《{{userReport.last_course.name}}》</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"years_two\" v-else>\r\n\t\t\t\t\t\t\t<view class=\"years_two_f animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni0\">今年你约课的次数为<text>0</text></view>\r\n\t\t\t\t\t\t\t<view class=\"years_two_f animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.twoAni1\">每个小任务都是通往目标的阶梯，别忘了拾级而上，别让它孤单地留在原地。</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item years_thr\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon10.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<view class=\"years_thr_a\">\r\n\t\t\t\t\t\t\t<text class=\"animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni1\">一堂</text>\r\n\t\t\t\t\t\t\t<text class=\"animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni1\">被遗忘的课程</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"years_thr_b\" v-if=\"userReport.appointment_num*1 > 0\">\r\n\t\t\t\t\t\t\t<view class=\"years_thr_b_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni2\">{{userReport.forget_teacher}}的</view>\r\n\t\t\t\t\t\t\t<view class=\"years_thr_b_b animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni3\">《{{userReport.course_name}}》</view>\r\n\t\t\t\t\t\t\t<view class=\"years_thr_b_c animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni4\">曾经是你的挚爱</view>\r\n\t\t\t\t\t\t\t<view class=\"years_thr_b_d animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni5\">但是已经<text>{{userReport.days}}</text>天没有练习它了</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"years_thr_f\" v-else>\r\n\t\t\t\t\t\t\t<view class=\"years_thr_b_e animate__animated animate__rotateInDownLeft\" v-if=\"configDate.thrAni2\"><text>{{currentYear}}</text>年，你还没有上过课。 打开用户报告的你，想必也怀揣着进取的心期待明年你的表现</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item years_fou\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon11.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<template v-if=\"userReport.sign_num*1 > 0\">\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" style=\"margin-top: 44rpx;\" v-if=\"configDate.fouAni1\">这是你坚持最久的两个习惯......</view>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.fouAni2\">本年度打卡<text>{{userReport.sign_num*1}}</text>次</view>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.fouAni3\">本年度练习<text>{{userReport.practice_time*1}}</text>小时</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" style=\"margin-top: 92rpx;\" v-if=\"configDate.fouAni4\">在此过程中，你发现了自己很多不足</view>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.fouAni5\">也让你明白</view>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.fouAni6\">放弃很容易，但坚持更酷！</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" style=\"margin-top: 44rpx;\" v-if=\"configDate.fouAni1\">本年度你并未坚持打卡......</view>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.fouAni2\">本年度打卡<text>0</text>次</view>\r\n\t\t\t\t\t\t\t<view class=\"years_fou_a animate__animated animate__rotateInDownLeft\" v-if=\"configDate.fouAni3\">本年度练习<text>0</text>次</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"userd1 animate__animated animate__bounce\">搜索</view>\r\n\t\t\r\n\t\t<view class=\"userd1 animate__animated animate__fadeInLeftBig\">搜索</view> -->\r\n\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tyearReportApi,\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\timgbaseUrlOss:'',\r\n\t\t\tsafeAreaTop:wx.getWindowInfo().safeArea.top,\r\n\t\t\tmenuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,\r\n\t\t\tloding:false,\r\n\t\t\tisLogined:true,\r\n\t\t\tcurrentYear:'',\r\n\t\t\tswiperIndex:0,\r\n\t\t\tconfigDate:{\r\n\t\t\t\toneAni1:false,\r\n\t\t\t\toneAni2:false,\r\n\t\t\t\toneAni3:false,\r\n\t\t\t\t\r\n\t\t\t\ttwoAni0:false,\r\n\t\t\t\ttwoAni1:false,\r\n\t\t\t\ttwoAni2:false,\r\n\t\t\t\ttwoAni3:false,\r\n\t\t\t\ttwoAni4:false,\r\n\t\t\t\t\r\n\t\t\t\tthrAni1:false,\r\n\t\t\t\tthrAni2:false,\r\n\t\t\t\tthrAni3:false,\r\n\t\t\t\tthrAni4:false,\r\n\t\t\t\tthrAni5:false,\r\n\t\t\t\t\r\n\t\t\t\tfouAni1:false,\r\n\t\t\t\tfouAni2:false,\r\n\t\t\t\tfouAni3:false,\r\n\t\t\t\tfouAni4:false,\r\n\t\t\t\tfouAni5:false,\r\n\t\t\t\tfouAni6:false,\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tuserReport:{}\r\n\t\t}\r\n\t},\r\n\tonShow: function(){\r\n\t   this.imgbaseUrl = this.$baseUrl;\r\n\t   this.imgbaseUrlOss = this.$baseUrlOss;\r\n\t   \r\n\t  \r\n\t},\r\n\tonLoad(option) {\r\n\t\tlet currentDate = new Date();\r\n\t\tthis.currentYear = currentDate.getFullYear();\r\n\t\tthis.yearUserReportData(option.id);//年报\r\n\t\t\r\n\t},\r\n\tmethods: {\r\n\t\t//年报\r\n\t\tyearUserReportData(id){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tyearReportApi({id:id}).then(res => {\r\n\t\t\t\tconsole.log('年报',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t/*res.data.appointment_num = 2\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\tres.data.last_course = {\r\n\t\t\t\t\t\t\"name\": \"JAZZ/KPOP常规课堂——Sunny\", //课程名称\r\n\t\t\t\t\t\t\"teacher\": {\r\n\t\t\t\t\t\t\t\"name\": \"Sunny\" //老师名称\r\n\t\t\t\t\t\t} //老师\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tres.data.sign_num = 2;*/\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.userReport = res.data;\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.cshData()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//初始化\r\n\t\tcshData(){\r\n\t\t\tvar that = this;\r\n\t\t\tif(this.swiperIndex == 0){\r\n\t\t\t\tthat.configDate.oneAni1 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.oneAni2 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.oneAni3 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t}\r\n\t\t},\r\n\t\t//监听swiper\r\n\t\tswiperChange(e){\r\n\t\t\tvar that = this;\r\n\t\t\t// console.log(e,'监听swiper')\r\n\t\t\tthis.swiperIndex = e.detail.current\r\n\t\t\t\r\n\t\t\tif(this.swiperIndex == 1){\r\n\t\t\t\tthat.configDate.twoAni0 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni1 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni2 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni3 = true;\r\n\t\t\t\t},3000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni4 = true;\r\n\t\t\t\t},4000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(this.swiperIndex == 2){\r\n\t\t\t\tthat.configDate.thrAni1 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.thrAni2 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.thrAni3 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.thrAni4 = true;\r\n\t\t\t\t},3000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.thrAni5 = true;\r\n\t\t\t\t},4000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(this.swiperIndex == 3){\r\n\t\t\t\tthat.configDate.fouAni1 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni2 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni3 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni4 = true;\r\n\t\t\t\t},3000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni5 = true;\r\n\t\t\t\t},4000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni6 = true;\r\n\t\t\t\t},5000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\t//动画结束时会触发\r\n\t\tswiperEnd(e){\r\n\t\t\t// console.log(e,'动画结束时会触发')\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\r\n.userReport{-overflow: hidden;}\r\npage{padding-bottom: 0;background:#fff;}\r\n.userd1{\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tbackground:red;\r\n\tmargin: auto;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yearsUserReport.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./yearsUserReport.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760724985\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}