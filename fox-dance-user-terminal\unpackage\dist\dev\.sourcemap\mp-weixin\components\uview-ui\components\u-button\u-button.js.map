{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-button/u-button.vue?2ebd", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-button/u-button.vue?0073", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-button/u-button.vue?f08e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-button/u-button.vue?2e15", "uni-app:///components/uview-ui/components/u-button/u-button.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-button/u-button.vue?5453", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/components/uview-ui/components/u-button/u-button.vue?7fe4"], "names": ["name", "props", "hairLine", "type", "default", "size", "shape", "plain", "disabled", "loading", "openType", "formType", "appParameter", "hoverStopPropagation", "lang", "sessionFrom", "sendMessageTitle", "sendMessagePath", "sendMessageImg", "showMessageCard", "hoverBgColor", "rippleBgColor", "ripple", "hoverClass", "customStyle", "dataName", "throttleTime", "hoverStartTime", "hoverStayTime", "computed", "getHoverClass", "showHairLineBorder", "data", "rippleTop", "rippleLeft", "fields", "waveActive", "methods", "click", "getWave<PERSON><PERSON>y", "touchesY", "touchesX", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "queryInfo", "resolve", "getphonenumber", "getuserinfo", "error", "opensetting", "launchapp"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAgwB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwDpxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA,gBA4BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;EACA;EACAyB;IACA;IACAC;MACA;MACA;MACA;MACAP;MACA;IACA;IACA;IACAQ;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAP;QACA;QACA;QACA;UACAQ;QAUAC;QACAD;;QAEA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAE;MAAA;MACA;QACA;QACA;QACA;QACAC;QAIAA;QACAA;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpVA;AAAA;AAAA;AAAA;AAA+7C,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAn9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-button/u-button.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-button.vue?vue&type=template&id=8ef4aa1a&scoped=true&\"\nvar renderjs\nimport script from \"./u-button.vue?vue&type=script&lang=js&\"\nexport * from \"./u-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-button.vue?vue&type=style&index=0&id=8ef4aa1a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8ef4aa1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-button/u-button.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=template&id=8ef4aa1a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    _vm.customStyle,\n    {\n      overflow: _vm.ripple ? \"hidden\" : \"visible\",\n    },\n  ])\n  var m0 = Number(_vm.hoverStartTime)\n  var m1 = Number(_vm.hoverStayTime)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<button\r\n\t\tid=\"u-wave-btn\"\r\n\t\tclass=\"u-btn u-line-1 u-fix-ios-appearance\"\r\n\t\t:class=\"[\r\n\t\t\t'u-size-' + size,\r\n\t\t\tplain ? 'u-btn--' + type + '--plain' : '',\r\n\t\t\tloading ? 'u-loading' : '',\r\n\t\t\tshape == 'circle' ? 'u-round-circle' : '',\r\n\t\t\thairLine ? showHairLineBorder : 'u-btn--bold-border',\r\n\t\t\t'u-btn--' + type,\r\n\t\t\tdisabled ? `u-btn--${type}--disabled` : '',\r\n\t\t]\"\r\n\t\t:hover-start-time=\"Number(hoverStartTime)\"\r\n\t\t:hover-stay-time=\"Number(hoverStayTime)\"\r\n\t\t:disabled=\"disabled\"\r\n\t\t:form-type=\"formType\"\r\n\t\t:open-type=\"openType\"\r\n\t\t:app-parameter=\"appParameter\"\r\n\t\t:hover-stop-propagation=\"hoverStopPropagation\"\r\n\t\t:send-message-title=\"sendMessageTitle\"\r\n\t\tsend-message-path=\"sendMessagePath\"\r\n\t\t:lang=\"lang\"\r\n\t\t:data-name=\"dataName\"\r\n\t\t:session-from=\"sessionFrom\"\r\n\t\t:send-message-img=\"sendMessageImg\"\r\n\t\t:show-message-card=\"showMessageCard\"\r\n\t\t@getphonenumber=\"getphonenumber\"\r\n\t\t@getuserinfo=\"getuserinfo\"\r\n\t\t@error=\"error\"\r\n\t\t@opensetting=\"opensetting\"\r\n\t\t@launchapp=\"launchapp\"\r\n\t\t:style=\"[customStyle, {\r\n\t\t\toverflow: ripple ? 'hidden' : 'visible'\r\n\t\t}]\"\r\n\t\**********=\"click($event)\"\r\n\t\t:hover-class=\"getHoverClass\"\r\n\t\t:loading=\"loading\"\r\n\t>\r\n\t\t<slot></slot>\r\n\t\t<view\r\n\t\t\tv-if=\"ripple\"\r\n\t\t\tclass=\"u-wave-ripple\"\r\n\t\t\t:class=\"[waveActive ? 'u-wave-active' : '']\"\r\n\t\t\t:style=\"{\r\n\t\t\t\ttop: rippleTop + 'px',\r\n\t\t\t\tleft: rippleLeft + 'px',\r\n\t\t\t\twidth: fields.targetWidth + 'px',\r\n\t\t\t\theight: fields.targetWidth + 'px',\r\n\t\t\t\t'background-color': rippleBgColor || 'rgba(0, 0, 0, 0.15)'\r\n\t\t\t}\"\r\n\t\t></view>\r\n\t</button>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * button 按钮\r\n * @description Button 按钮\r\n * @tutorial https://www.uviewui.com/components/button.html\r\n * @property {String} size 按钮的大小\r\n * @property {Boolean} ripple 是否开启点击水波纹效果\r\n * @property {String} ripple-bg-color 水波纹的背景色，ripple为true时有效\r\n * @property {String} type 按钮的样式类型\r\n * @property {Boolean} plain 按钮是否镂空，背景色透明\r\n * @property {Boolean} disabled 是否禁用\r\n * @property {Boolean} hair-line 是否显示按钮的细边框(默认true)\r\n * @property {Boolean} shape 按钮外观形状，见文档说明\r\n * @property {Boolean} loading 按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈)\r\n * @property {String} form-type 用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\r\n * @property {String} open-type 开放能力\r\n * @property {String} data-name 额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\r\n * @property {String} hover-class 指定按钮按下去的样式类。当 hover-class=\"none\" 时，没有点击态效果(App-nvue 平台暂不支持)\r\n * @property {Number} hover-start-time 按住后多久出现点击态，单位毫秒\r\n * @property {Number} hover-stay-time 手指松开后点击态保留时间，单位毫秒\r\n * @property {Object} custom-style 对按钮的自定义样式，对象形式，见文档说明\r\n * @event {Function} click 按钮点击\r\n * @event {Function} getphonenumber open-type=\"getPhoneNumber\"时有效\r\n * @event {Function} getuserinfo 用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo\r\n * @event {Function} error 当使用开放能力时，发生错误的回调\r\n * @event {Function} opensetting 在打开授权设置页并关闭后回调\r\n * @event {Function} launchapp 打开 APP 成功的回调\r\n * @example <u-button>月落</u-button>\r\n */\r\nexport default {\r\n\tname: 'u-button',\r\n\tprops: {\r\n\t\t// 是否细边框\r\n\t\thairLine: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 按钮的预置样式，default，primary，error，warning，success\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'default'\r\n\t\t},\r\n\t\t// 按钮尺寸，default，medium，mini\r\n\t\tsize: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'default'\r\n\t\t},\r\n\t\t// 按钮形状，circle（两边为半圆），square（带圆角）\r\n\t\tshape: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'square'\r\n\t\t},\r\n\t\t// 按钮是否镂空\r\n\t\tplain: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否禁止状态\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否加载中\r\n\t\tloading: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 开放能力，具体请看uniapp稳定关于button组件部分说明\r\n\t\t// https://uniapp.dcloud.io/component/button\r\n\t\topenType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\r\n\t\t// 取值为submit（提交表单），reset（重置表单）\r\n\t\tformType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效\r\n\t\t// 只微信小程序、QQ小程序有效\r\n\t\tappParameter: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 指定是否阻止本节点的祖先节点出现点击态，微信小程序有效\r\n\t\thoverStopPropagation: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文。只微信小程序有效\r\n\t\tlang: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'en'\r\n\t\t},\r\n\t\t// 会话来源，open-type=\"contact\"时有效。只微信小程序有效\r\n\t\tsessionFrom: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 会话内消息卡片标题，open-type=\"contact\"时有效\r\n\t\t// 默认当前标题，只微信小程序有效\r\n\t\tsendMessageTitle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 会话内消息卡片点击跳转小程序路径，open-type=\"contact\"时有效\r\n\t\t// 默认当前分享路径，只微信小程序有效\r\n\t\tsendMessagePath: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 会话内消息卡片图片，open-type=\"contact\"时有效\r\n\t\t// 默认当前页面截图，只微信小程序有效\r\n\t\tsendMessageImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，\r\n\t\t// 用户点击后可以快速发送小程序消息，open-type=\"contact\"时有效\r\n\t\tshowMessageCard: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 手指按（触摸）按钮时按钮时的背景颜色\r\n\t\thoverBgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 水波纹的背景颜色\r\n\t\trippleBgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 是否开启水波纹效果\r\n\t\tripple: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 按下的类名\r\n\t\thoverClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 自定义样式，对象形式\r\n\t\tcustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\r\n\t\tdataName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 节流，一定时间内只能触发一次\r\n\t\tthrottleTime: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 1000\r\n\t\t},\r\n\t\t// 按住后多久出现点击态，单位毫秒\r\n\t\thoverStartTime: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 20\r\n\t\t},\r\n\t\t// 手指松开后点击态保留时间，单位毫秒\r\n\t\thoverStayTime: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 150\r\n\t\t},\r\n\t},\r\n\tcomputed: {\r\n\t\t// 当没有传bgColor变量时，按钮按下去的颜色类名\r\n\t\tgetHoverClass() {\r\n\t\t\t// 如果开启水波纹效果，则不启用hover-class效果\r\n\t\t\tif (this.loading || this.disabled || this.ripple || this.hoverClass) return '';\r\n\t\t\tlet hoverClass = '';\r\n\t\t\thoverClass = this.plain ? 'u-' + this.type + '-plain-hover' : 'u-' + this.type + '-hover';\r\n\t\t\treturn hoverClass;\r\n\t\t},\r\n\t\t// 在'primary', 'success', 'error', 'warning'类型下，不显示边框，否则会造成四角有毛刺现象\r\n\t\tshowHairLineBorder() {\r\n\t\t\tif (['primary', 'success', 'error', 'warning'].indexOf(this.type) >= 0 && !this.plain) {\r\n\t\t\t\treturn '';\r\n\t\t\t} else {\r\n\t\t\t\treturn 'u-hairline-border';\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\trippleTop: 0, // 水波纹的起点Y坐标到按钮上边界的距离\r\n\t\t\trippleLeft: 0, // 水波纹起点X坐标到按钮左边界的距离\r\n\t\t\tfields: {}, // 波纹按钮节点信息\r\n\t\t\twaveActive: false // 激活水波纹\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\t// 按钮点击\r\n\t\tclick(e) {\r\n\t\t\t// 进行节流控制，每this.throttle毫秒内，只在开始处执行\r\n\t\t\tthis.$u.throttle(() => {\r\n\t\t\t\t// 如果按钮时disabled和loading状态，不触发水波纹效果\r\n\t\t\t\tif (this.loading === true || this.disabled === true) return;\r\n\t\t\t\t// 是否开启水波纹效果\r\n\t\t\t\tif (this.ripple) {\r\n\t\t\t\t\t// 每次点击时，移除上一次的类，再次添加，才能触发动画效果\r\n\t\t\t\t\tthis.waveActive = false;\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\tthis.getWaveQuery(e);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('click', e);\r\n\t\t\t}, this.throttleTime);\r\n\t\t},\r\n\t\t// 查询按钮的节点信息\r\n\t\tgetWaveQuery(e) {\r\n\t\t\tthis.getElQuery().then(res => {\r\n\t\t\t\t// 查询返回的是一个数组节点\r\n\t\t\t\tlet data = res[0];\r\n\t\t\t\t// 查询不到节点信息，不操作\r\n\t\t\t\tif (!data.width || !data.width) return;\r\n\t\t\t\t// 水波纹的最终形态是一个正方形(通过border-radius让其变为一个圆形)，这里要保证正方形的边长等于按钮的最长边\r\n\t\t\t\t// 最终的方形（变换后的圆形）才能覆盖整个按钮\r\n\t\t\t\tdata.targetWidth = data.height > data.width ? data.height : data.width;\r\n\t\t\t\tif (!data.targetWidth) return;\r\n\t\t\t\tthis.fields = data;\r\n\t\t\t\tlet touchesX = '',\r\n\t\t\t\t\ttouchesY = '';\r\n\t\t\t\t// #ifdef MP-BAIDU\r\n\t\t\t\ttouchesX = e.changedTouches[0].clientX;\r\n\t\t\t\ttouchesY = e.changedTouches[0].clientY;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\ttouchesX = e.detail.clientX;\r\n\t\t\t\ttouchesY = e.detail.clientY;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY\r\n\t\t\t\ttouchesX = e.touches[0].clientX;\r\n\t\t\t\ttouchesY = e.touches[0].clientY;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// 获取触摸点相对于按钮上边和左边的x和y坐标，原理是通过屏幕的触摸点（touchesY），减去按钮的上边界data.top\r\n\t\t\t\t// 但是由于`transform-origin`默认是center，所以这里再减去半径才是水波纹view应该的位置\r\n\t\t\t\t// 总的来说，就是把水波纹的矩形（变换后的圆形）的中心点，移动到我们的触摸点位置\r\n\t\t\t\tthis.rippleTop = touchesY - data.top - data.targetWidth / 2;\r\n\t\t\t\tthis.rippleLeft = touchesX - data.left - data.targetWidth / 2;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.waveActive = true;\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取节点信息\r\n\t\tgetElQuery() {\r\n\t\t\treturn new Promise(resolve => {\r\n\t\t\t\tlet queryInfo = '';\r\n\t\t\t\t// 获取元素节点信息，请查看uniapp相关文档\r\n\t\t\t\t// https://uniapp.dcloud.io/api/ui/nodes-info?id=nodesrefboundingclientrect\r\n\t\t\t\tqueryInfo = uni.createSelectorQuery().in(this);\r\n\t\t\t\t//#ifdef MP-ALIPAY\r\n\t\t\t\tqueryInfo = uni.createSelectorQuery();\r\n\t\t\t\t//#endif\r\n\t\t\t\tqueryInfo.select('.u-btn').boundingClientRect();\r\n\t\t\t\tqueryInfo.exec(data => {\r\n\t\t\t\t\tresolve(data);\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 下面为对接uniapp官方按钮开放能力事件回调的对接\r\n\t\tgetphonenumber(res) {\r\n\t\t\tthis.$emit('getphonenumber', res);\r\n\t\t},\r\n\t\tgetuserinfo(res) {\r\n\t\t\tthis.$emit('getuserinfo', res);\r\n\t\t},\r\n\t\terror(res) {\r\n\t\t\tthis.$emit('error', res);\r\n\t\t},\r\n\t\topensetting(res) {\r\n\t\t\tthis.$emit('opensetting', res);\r\n\t\t},\r\n\t\tlaunchapp(res) {\r\n\t\t\tthis.$emit('launchapp', res);\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '../../libs/css/style.components.scss';\r\n.u-btn::after {\r\n\tborder: none;\r\n}\r\n\r\n.u-btn {\r\n\tposition: relative;\r\n\tborder: 0;\r\n\t//border-radius: 10rpx;\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: inline-flex;\t\t\r\n\t/* #endif */\r\n\t// 避免边框某些场景可能被“裁剪”，不能设置为hidden\r\n\toverflow: visible;\r\n\tline-height: 1;\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcursor: pointer;\r\n\tpadding: 0 40rpx;\r\n\tz-index: 1;\r\n\tbox-sizing: border-box;\r\n\ttransition: all 0.15s;\r\n\t\r\n\t&--bold-border {\r\n\t\tborder: 1px solid #ffffff;\r\n\t}\r\n\t\r\n\t&--default {\r\n\t\tcolor: $u-content-color;\r\n\t\tborder-color: #c0c4cc;\r\n\t\tbackground-color: #ffffff;\r\n\t}\r\n\t\r\n\t&--primary {\r\n\t\tcolor: #ffffff;\r\n\t\tborder-color: $u-type-primary;\r\n\t\tbackground-color: $u-type-primary;\r\n\t}\r\n\t\r\n\t&--success {\r\n\t\tcolor: #ffffff;\r\n\t\tborder-color: $u-type-success;\r\n\t\tbackground-color: $u-type-success;\r\n\t}\r\n\t\r\n\t&--error {\r\n\t\tcolor: #ffffff;\r\n\t\tborder-color: $u-type-error;\r\n\t\tbackground-color: $u-type-error;\r\n\t}\r\n\t\r\n\t&--warning {\r\n\t\tcolor: #ffffff;\r\n\t\tborder-color: $u-type-warning;\r\n\t\tbackground-color: $u-type-warning;\r\n\t}\r\n\t\r\n\t&--default--disabled {\r\n\t\tcolor: #ffffff;\r\n\t\tborder-color: #e4e7ed;\r\n\t\tbackground-color: #ffffff;\r\n\t}\r\n\t\r\n\t&--primary--disabled {\r\n\t\tcolor: #ffffff!important;\r\n\t\tborder-color: $u-type-primary-disabled!important;\r\n\t\tbackground-color: $u-type-primary-disabled!important;\r\n\t}\r\n\t\r\n\t&--success--disabled {\r\n\t\tcolor: #ffffff!important;\r\n\t\tborder-color: $u-type-success-disabled!important;\r\n\t\tbackground-color: $u-type-success-disabled!important;\r\n\t}\r\n\t\r\n\t&--error--disabled {\r\n\t\tcolor: #ffffff!important;\r\n\t\tborder-color: $u-type-error-disabled!important;\r\n\t\tbackground-color: $u-type-error-disabled!important;\r\n\t}\r\n\t\r\n\t&--warning--disabled {\r\n\t\tcolor: #ffffff!important;\r\n\t\tborder-color: $u-type-warning-disabled!important;\r\n\t\tbackground-color: $u-type-warning-disabled!important;\r\n\t}\r\n\t\r\n\t&--primary--plain {\r\n\t\tcolor: $u-type-primary!important;\r\n\t\tborder-color: $u-type-primary-disabled!important;\r\n\t\tbackground-color: $u-type-primary-light!important;\r\n\t}\r\n\t\r\n\t&--success--plain {\r\n\t\tcolor: $u-type-success!important;\r\n\t\tborder-color: $u-type-success-disabled!important;\r\n\t\tbackground-color: $u-type-success-light!important;\r\n\t}\r\n\t\r\n\t&--error--plain {\r\n\t\tcolor: $u-type-error!important;\r\n\t\tborder-color: $u-type-error-disabled!important;\r\n\t\tbackground-color: $u-type-error-light!important;\r\n\t}\r\n\t\r\n\t&--warning--plain {\r\n\t\tcolor: $u-type-warning!important;\r\n\t\tborder-color: $u-type-warning-disabled!important;\r\n\t\tbackground-color: $u-type-warning-light!important;\r\n\t}\r\n}\r\n\r\n.u-hairline-border:after {\r\n\tcontent: ' ';\r\n\tposition: absolute;\r\n\tpointer-events: none;\r\n\t// 设置为border-box，意味着下面的scale缩小为0.5，实际上缩小的是伪元素的内容（border-box意味着内容不含border）\r\n\tbox-sizing: border-box;\r\n\t// 中心点作为变形(scale())的原点\r\n\t-webkit-transform-origin: 0 0;\r\n\ttransform-origin: 0 0;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\twidth: 199.8%;\r\n\theight: 199.7%;\r\n\t-webkit-transform: scale(0.5, 0.5);\r\n\ttransform: scale(0.5, 0.5);\r\n\tborder: 1px solid currentColor;\r\n\tz-index: 1;\r\n}\r\n\r\n.u-wave-ripple {\r\n\tz-index: 0;\r\n\tposition: absolute;\r\n\tborder-radius: 100%;\r\n\tbackground-clip: padding-box;\r\n\tpointer-events: none;\r\n\tuser-select: none;\r\n\ttransform: scale(0);\r\n\topacity: 1;\r\n\ttransform-origin: center;\r\n}\r\n\r\n.u-wave-ripple.u-wave-active {\r\n\topacity: 0;\r\n\ttransform: scale(2);\r\n\ttransition: opacity 1s linear, transform 0.4s linear;\r\n}\r\n\r\n.u-round-circle {\r\n\tborder-radius: 100rpx;\r\n}\r\n\r\n.u-round-circle::after {\r\n\tborder-radius: 100rpx;\r\n}\r\n\r\n.u-loading::after {\r\n\tbackground-color: hsla(0, 0%, 100%, 0.35);\r\n}\r\n\r\n.u-size-default {\r\n\tfont-size: 30rpx;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n}\r\n\r\n.u-size-medium {\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: inline-flex;\t\t\r\n\t/* #endif */\r\n\twidth: auto;\r\n\tfont-size: 26rpx;\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n\tpadding: 0 80rpx;\r\n}\r\n\r\n.u-size-mini {\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: inline-flex;\t\t\r\n\t/* #endif */\r\n\twidth: auto;\r\n\tfont-size: 22rpx;\r\n\tpadding-top: 1px;\r\n\theight: 50rpx;\r\n\tline-height: 50rpx;\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n.u-primary-plain-hover {\r\n\tcolor: #ffffff !important;\r\n\tbackground: $u-type-primary-dark !important;\r\n}\r\n\r\n.u-default-plain-hover {\r\n\tcolor: $u-type-primary-dark !important;\r\n\tbackground: $u-type-primary-light !important;\r\n}\r\n\r\n.u-success-plain-hover {\r\n\tcolor: #ffffff !important;\r\n\tbackground: $u-type-success-dark !important;\r\n}\r\n\r\n.u-warning-plain-hover {\r\n\tcolor: #ffffff !important;\r\n\tbackground: $u-type-warning-dark !important;\r\n}\r\n\r\n.u-error-plain-hover {\r\n\tcolor: #ffffff !important;\r\n\tbackground: $u-type-error-dark !important;\r\n}\r\n\r\n.u-info-plain-hover {\r\n\tcolor: #ffffff !important;\r\n\tbackground: $u-type-info-dark !important;\r\n}\r\n\r\n.u-default-hover {\r\n\tcolor: $u-type-primary-dark !important;\r\n\tborder-color: $u-type-primary-dark !important;\r\n\tbackground-color: $u-type-primary-light !important;\r\n}\r\n\r\n.u-primary-hover {\r\n\tbackground: $u-type-primary-dark !important;\r\n\tcolor: #fff;\r\n}\r\n\r\n.u-success-hover {\r\n\tbackground: $u-type-success-dark !important;\r\n\tcolor: #fff;\r\n}\r\n\r\n.u-info-hover {\r\n\tbackground: $u-type-info-dark !important;\r\n\tcolor: #fff;\r\n}\r\n\r\n.u-warning-hover {\r\n\tbackground: $u-type-warning-dark !important;\r\n\tcolor: #fff;\r\n}\r\n\r\n.u-error-hover {\r\n\tbackground: $u-type-error-dark !important;\r\n\tcolor: #fff;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=style&index=0&id=8ef4aa1a&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=style&index=0&id=8ef4aa1a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760724725\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}