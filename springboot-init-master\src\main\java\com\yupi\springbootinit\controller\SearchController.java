package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.search.SearchRequest;
import com.yupi.springbootinit.model.vo.SearchResultVO;
import com.yupi.springbootinit.service.SearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索功能接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/search")
@Slf4j
@Api(tags = "搜索功能接口")
public class SearchController {

    @Resource
    private SearchService searchService;

    /**
     * 综合搜索
     */
    @GetMapping("/comprehensive")
    @ApiOperation(value = "综合搜索")
    public BaseResponse<SearchResultVO> comprehensiveSearch(@RequestParam String keyword,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Long userId,
            HttpServletRequest request) {
        if (StringUtils.isBlank(keyword)) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能为空");
        }

        if (keyword.length() > 50) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能超过50个字符");
        }

        try {
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.setKeyword(keyword.trim());
            searchRequest.setCurrent(current);
            searchRequest.setSize(size);
            searchRequest.setType(type);

            // 使用前端传递的用户ID
            searchRequest.setUserId(userId);

            SearchResultVO result = searchService.comprehensiveSearch(searchRequest);

            // 保存搜索历史
            if (userId != null) {
                searchService.saveSearchHistory(userId, keyword);
            }

            log.info("综合搜索成功 - keyword: {}, userId: {}, resultCount: {}",
                    keyword, userId, result.getTotalCount());
            return ResultUtils.success(result);

        } catch (Exception e) {
            log.error("综合搜索失败 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "搜索失败");
        }
    }

    /**
     * 获取热门搜索词
     */
    @GetMapping("/hot-keywords")
    @ApiOperation(value = "获取热门搜索词")
    public BaseResponse<List<String>> getHotKeywords(@RequestParam(defaultValue = "10") Integer limit) {
        if (limit <= 0 || limit > 50) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "限制数量必须在1-50之间");
        }

        try {
            List<String> hotKeywords = searchService.getHotKeywords(limit);
            log.info("获取热门搜索词成功 - limit: {}, count: {}", limit, hotKeywords.size());
            return ResultUtils.success(hotKeywords);

        } catch (Exception e) {
            log.error("获取热门搜索词失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取热门搜索词失败");
        }
    }

    /**
     * 获取搜索历史
     */
    @GetMapping("/history")
    @ApiOperation(value = "获取搜索历史")
    public BaseResponse<List<String>> getSearchHistory(@RequestParam(defaultValue = "20") Integer limit,
            @RequestParam(required = false) Long userId,
            HttpServletRequest request) {
        if (limit <= 0 || limit > 100) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "限制数量必须在1-100之间");
        }

        try {
            // 如果没有传递userId，返回空列表
            if (userId == null) {
                return ResultUtils.success(new ArrayList<>());
            }

            List<String> searchHistory = searchService.getSearchHistory(userId, limit);
            log.info("获取搜索历史成功 - userId: {}, limit: {}, count: {}",
                    userId, limit, searchHistory.size());
            return ResultUtils.success(searchHistory);

        } catch (Exception e) {
            log.error("获取搜索历史失败 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取搜索历史失败");
        }
    }

    /**
     * 清空搜索历史
     */
    @DeleteMapping("/history")
    @ApiOperation(value = "清空搜索历史")
    public BaseResponse<Boolean> clearSearchHistory(@RequestParam(required = false) Long userId,
            HttpServletRequest request) {
        try {
            // 如果没有传递userId，返回成功（无需操作）
            if (userId == null) {
                return ResultUtils.success(true);
            }

            boolean result = searchService.clearSearchHistory(userId);
            if (result) {
                log.info("清空搜索历史成功 - userId: {}", userId);
                return ResultUtils.success(true);
            } else {
                log.warn("清空搜索历史失败 - userId: {}", userId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "清空搜索历史失败");
            }

        } catch (Exception e) {
            log.error("清空搜索历史异常 - error: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "清空搜索历史失败");
        }
    }

    /**
     * 删除单个搜索历史
     */
    @DeleteMapping("/history/{keyword}")
    @ApiOperation(value = "删除单个搜索历史")
    public BaseResponse<Boolean> deleteSearchHistory(@PathVariable String keyword,
            @RequestParam(required = false) Long userId,
            HttpServletRequest request) {
        if (StringUtils.isBlank(keyword)) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能为空");
        }

        try {
            // 如果没有传递userId，返回成功（无需操作）
            if (userId == null) {
                return ResultUtils.success(true);
            }

            boolean result = searchService.deleteSearchHistory(userId, keyword);
            if (result) {
                log.info("删除搜索历史成功 - userId: {}, keyword: {}", userId, keyword);
                return ResultUtils.success(true);
            } else {
                log.warn("删除搜索历史失败 - userId: {}, keyword: {}", userId, keyword);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "删除搜索历史失败");
            }

        } catch (Exception e) {
            log.error("删除搜索历史异常 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "删除搜索历史失败");
        }
    }

    /**
     * 搜索建议
     */
    @GetMapping("/suggestions")
    @ApiOperation(value = "搜索建议")
    public BaseResponse<List<String>> getSearchSuggestions(@RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        if (StringUtils.isBlank(keyword)) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能为空");
        }

        if (keyword.length() > 20) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "搜索关键词不能超过20个字符");
        }

        if (limit <= 0 || limit > 20) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "限制数量必须在1-20之间");
        }

        try {
            List<String> suggestions = searchService.getSearchSuggestions(keyword.trim(), limit);
            log.info("获取搜索建议成功 - keyword: {}, limit: {}, count: {}",
                    keyword, limit, suggestions.size());
            return ResultUtils.success(suggestions);

        } catch (Exception e) {
            log.error("获取搜索建议失败 - keyword: {}, error: {}", keyword, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取搜索建议失败");
        }
    }
}
