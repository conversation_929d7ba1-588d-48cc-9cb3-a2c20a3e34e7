<template>
  <view class="message-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="title">消息</text>
        <view class="header-actions">
          <u-icon name="search" size="24" color="#333" @click="goSearch"></u-icon>
          <u-icon name="plus" size="24" color="#333" @click="startNewChat"></u-icon>
        </view>
      </view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view
      class="scroll-content"
      scroll-y
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <!-- 功能入口 -->
      <view class="quick-actions">
        <view class="action-item" @click="goSystemMessages">
          <view class="action-icon system">
            <u-icon name="bell" color="#fff" size="20"></u-icon>
          </view>
          <text class="action-text">系统消息</text>
          <view v-if="systemUnreadCount" class="unread-badge">{{ systemUnreadCount }}</view>
        </view>

        <view class="action-item" @click="goLikeMessages">
          <view class="action-icon like">
            <u-icon name="heart" color="#fff" size="20"></u-icon>
          </view>
          <text class="action-text">赞和评论</text>
          <view v-if="likeUnreadCount" class="unread-badge">{{ likeUnreadCount }}</view>
        </view>

        <view class="action-item" @click="goFollowMessages">
          <view class="action-icon follow">
            <u-icon name="account-fill" color="#fff" size="20"></u-icon>
          </view>
          <text class="action-text">新粉丝</text>
          <view v-if="followUnreadCount" class="unread-badge">{{ followUnreadCount }}</view>
        </view>
      </view>

      <!-- 聊天列表容器 -->
      <view class="chat-list">
        <!-- 聊天列表 -->
        <view
          v-for="chat in chatList"
          :key="chat.id"
          class="chat-item"
          @click="openChat(chat)"
          @longpress="showChatActions(chat)"
        >
          <view class="chat-avatar">
            <u-avatar :src="chat.avatar" size="50"></u-avatar>
            <view v-if="chat.isOnline" class="online-dot"></view>
          </view>

          <view class="chat-content">
            <view class="chat-header">
              <text class="chat-name">{{ chat.name }}</text>
              <text class="chat-time">{{ formatTime(chat.lastMessageTime) }}</text>
            </view>

            <view class="chat-preview">
              <view class="message-preview">
                <text
                  v-if="chat.lastMessageType === 'text'"
                  class="preview-text"
                >{{ chat.lastMessage }}</text>
                <text v-else-if="chat.lastMessageType === 'image'" class="preview-text">[图片]</text>
                <text v-else-if="chat.lastMessageType === 'voice'" class="preview-text">[语音]</text>
                <text v-else class="preview-text">{{ chat.lastMessage }}</text>
              </view>

              <view class="chat-status">
                <view
                  v-if="chat.unreadCount"
                  class="unread-count"
                >{{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!chatList.length && !loading" class="empty-state">
          <u-icon name="chat" color="#ccc" size="60"></u-icon>
          <text class="empty-text">暂无消息</text>
          <text class="empty-desc">开始与朋友聊天吧</text>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <u-loading mode="circle" size="24"></u-loading>
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {
  getConversations,
  getMessageUnreadCount,
  markMessageAsRead
} from "@/utils/socialApi.js";

export default {
  name: "SocialMessage",
  data() {
    return {
      chatList: [],
      loading: false,
      refreshing: false,
      systemUnreadCount: 0,
      likeUnreadCount: 0,
      followUnreadCount: 0
    };
  },
  onLoad() {
    this.loadUnreadCounts();
    this.loadChatList();
  },

  onShow() {
    // 页面显示时检查是否需要加载数据
    if (!this.chatList || this.chatList.length === 0) {
      console.log("消息页显示时重新加载数据");
      this.loadUnreadCounts();
      this.loadChatList();
    }
  },

  // 组件激活时重新加载数据（用于keep-alive场景）
  activated() {
    if (!this.chatList || this.chatList.length === 0) {
      console.log("消息页激活时重新加载数据");
      this.loadUnreadCounts();
      this.loadChatList();
    }
  },
  methods: {
    // 加载未读消息统计
    async loadUnreadCounts() {
      try {
        // 暂时将所有徽标都设为0，不显示假数据
        this.systemUnreadCount = 0;
        this.likeUnreadCount = 0;
        this.followUnreadCount = 0;

        // 如果需要真实数据，可以取消注释下面的代码
        // const unreadData = await getMessageUnreadCount();
        // if (unreadData && unreadData.code === 0 && unreadData.data) {
        //   this.systemUnreadCount = unreadData.data.total || 0;
        // }
      } catch (error) {
        console.error("加载未读消息统计失败:", error);
        // 使用默认值
      }
    },

    async loadChatList() {
      console.log("开始加载聊天列表...");
      this.loading = true;

      try {
        // 检查用户登录状态
        const currentUserId = uni.getStorageSync("userid");
        if (!currentUserId) {
          console.error("用户未登录，无法加载聊天列表");
          this.chatList = [];
          return;
        }

        const result = await getConversations({
          current: 1,
          size: 50
        });

        console.log("聊天列表API返回:", result);

        // 处理API返回的数据格式
        let conversations = [];
        if (
          result &&
          result.code === 0 &&
          result.data &&
          Array.isArray(result.data)
        ) {
          conversations = result.data;
        } else if (result && Array.isArray(result)) {
          conversations = result;
        } else {
          console.log("没有找到聊天列表或格式不正确");
          conversations = [];
        }

        if (conversations.length > 0) {
          this.chatList = conversations.map(conversation => ({
            id: conversation.id || conversation.conversationId,
            userId: conversation.otherUserId || conversation.userId,
            name:
              conversation.otherUserNickname ||
              conversation.nickname ||
              "用户" + (conversation.otherUserId || conversation.userId),
            avatar: this.formatAvatarUrl(
              conversation.otherUserAvatar || conversation.avatar
            ),
            lastMessage: conversation.lastMessageContent || "",
            lastMessageTime: conversation.lastMessageTime
              ? new Date(conversation.lastMessageTime)
              : new Date(),
            lastMessageType: this.getMessageTypeString(
              conversation.lastMessageType || 1
            ),
            unreadCount: conversation.unreadCount || 0,
            isOnline: conversation.isOnline || false,
            isMuted: conversation.isMuted || false
          }));
          console.log("会话列表", this.chatList);
          console.log("聊天列表处理完成，会话数量:", this.chatList.length);
        } else {
          console.log("没有聊天记录，显示空状态");
          this.chatList = [];
        }
      } catch (error) {
        console.error("加载聊天列表失败:", error);
        uni.showToast({
          title: "加载失败",
          icon: "none"
        });
        this.chatList = [];
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 格式化头像URL
    formatAvatarUrl(avatar) {
      if (!avatar) {
        return "/static/images/toux.png";
      }

      if (avatar.startsWith("http")) {
        return avatar;
      }

      return "https://file.foxdance.com.cn" + avatar;
    },

    // 获取消息类型字符串
    getMessageTypeString(messageType) {
      const typeMap = {
        1: "text",
        2: "image",
        3: "voice",
        4: "video",
        5: "file"
      };
      return typeMap[messageType] || "text";
    },

    formatTime(time) {
      const now = new Date();
      const diff = now - new Date(time);
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);

      if (minutes < 1) return "刚刚";
      if (minutes < 60) return `${minutes}分钟前`;
      if (hours < 24) return `${hours}小时前`;
      if (days < 7) return `${days}天前`;

      const date = new Date(time);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    },

    onRefresh() {
      this.refreshing = true;
      this.loadChatList();
    },

    async openChat(chat) {
      console.log("打开聊天:", chat);

      try {
        // 标记消息已读
        if (chat.unreadCount > 0) {
          await markMessageAsRead({
            senderId: chat.userId,
            receiverId: uni.getStorageSync("userid")
          });

          // 清除未读数
          chat.unreadCount = 0;

          // 更新聊天列表
          const index = this.chatList.findIndex(c => c.id === chat.id);
          if (index !== -1) {
            this.$set(this.chatList, index, { ...chat });
          }
        }

        // 跳转到聊天详情页，使用与用户主页一致的参数格式
        uni.navigateTo({
          url: `/pagesSub/social/chat/detail?userId=${
            chat.userId
          }&nickname=${encodeURIComponent(
            chat.name
          )}&avatar=${encodeURIComponent(chat.avatar)}`
        });
      } catch (error) {
        console.error("打开聊天失败:", error);
        // 即使标记已读失败，也要跳转到聊天页面
        uni.navigateTo({
          url: `/pagesSub/social/chat/detail?userId=${
            chat.userId
          }&nickname=${encodeURIComponent(
            chat.name
          )}&avatar=${encodeURIComponent(chat.avatar)}`
        });
      }
    },

    showChatActions(chat) {
      const actions = [
        "置顶",
        chat.isMuted ? "取消免打扰" : "免打扰",
        "删除聊天"
      ];

      uni.showActionSheet({
        itemList: actions,
        success: res => {
          switch (res.tapIndex) {
            case 0:
              this.toggleChatTop(chat);
              break;
            case 1:
              this.toggleChatMute(chat);
              break;
            case 2:
              this.deleteChat(chat);
              break;
          }
        }
      });
    },

    toggleChatTop(chat) {
      // 置顶/取消置顶逻辑
      this.$u.toast("置顶成功");
    },

    toggleChatMute(chat) {
      chat.isMuted = !chat.isMuted;
      this.$u.toast(chat.isMuted ? "已开启免打扰" : "已关闭免打扰");
    },

    deleteChat(chat) {
      uni.showModal({
        title: "确认删除",
        content: "确定要删除这个聊天吗？",
        success: res => {
          if (res.confirm) {
            const index = this.chatList.findIndex(item => item.id === chat.id);
            if (index > -1) {
              this.chatList.splice(index, 1);
              this.$u.toast("删除成功");
            }
          }
        }
      });
    },

    goSearch() {
      uni.navigateTo({
        url: "/pagesSub/social/search/chat"
      });
    },

    startNewChat() {
      uni.navigateTo({
        url: "/pagesSub/social/contact/select"
      });
    },

    goSystemMessages() {
      uni.navigateTo({
        url: "/pagesSub/social/message/system"
      });
    },

    goLikeMessages() {
      uni.navigateTo({
        url: "/pagesSub/social/message/likes"
      });
    },

    goFollowMessages() {
      uni.navigateTo({
        url: "/pagesSub/social/message/followers"
      });
    },

    // 强制刷新数据（供父组件调用）
    forceRefresh() {
      console.log("强制刷新消息页数据...");
      this.chatList = [];
      this.loadUnreadCounts();
      this.loadChatList();
    }
  }
};
</script>

<style lang="scss" scoped>
.message-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 100px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.scroll-content {
  height: calc(100vh - 44px - var(--status-bar-height));
  margin-top: calc(44px + var(--status-bar-height));
}

.quick-actions {
  background: #fff;
  padding: 16px;
  display: flex;
  justify-content: space-around;
  border-bottom: 8px solid #f8f9fa;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.action-icon {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.action-icon.system {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon.like {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-icon.follow {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-text {
  font-size: 12px;
  color: #666;
}

.unread-badge {
  position: absolute;
  top: -2px;
  right: 8px;
  background: #ff4757;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.chat-list {
  background: #fff;
  min-height: calc(100vh - 200px);
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.chat-item:last-child {
  border-bottom: none;
}

.chat-avatar {
  position: relative;
  margin-right: 12px;
}

.online-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #52c41a;
  border: 2px solid #fff;
  border-radius: 6px;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.chat-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.chat-time {
  font-size: 12px;
  color: #999;
}

.chat-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-preview {
  flex: 1;
  min-width: 0;
}

.preview-text {
  font-size: 14px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.chat-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unread-count {
  background: #ff4757;
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin: 16px 0 8px;
}

.empty-desc {
  font-size: 14px;
  color: #ccc;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-text {
  margin-left: 8px;
  color: #999;
  font-size: 14px;
}
</style>
