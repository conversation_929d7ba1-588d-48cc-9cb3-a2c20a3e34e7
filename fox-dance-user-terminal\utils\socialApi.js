/**
 * 社交功能API接口封装
 * 基于vote_baseUrl配置的后端服务
 */

import { http } from "@/config/http.api.js";
import config from "@/config/http.api.js";

// 获取基础URL
const getBaseUrl = () => {
  return config.apis.vote_baseUrl;
};

// 通用请求方法
const request = (url, options = {}) => {
  const baseUrl = getBaseUrl();
  const fullUrl = `${baseUrl}/api${url}`;

  // 添加调试日志
  console.log("🚀 发起API请求:", {
    url: fullUrl,
    method: options.method || "GET",
    data: options.data,
    header: options.header,
  });

  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method: options.method || "GET",
      data: options.data || {},
      header: {
        "Content-Type": "application/json",
        // TODO: 添加认证token
        // 'Authorization': `Bearer ${getToken()}`
        ...options.header,
      },
      success: (res) => {
        console.log("✅ API请求成功:", {
          url: fullUrl,
          statusCode: res.statusCode,
          data: res.data,
        });

        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            // 返回完整的响应对象，包含code、data、message
            resolve(res.data);
          } else {
            console.error("❌ API业务错误:", res.data);
            reject(new Error(res.data.message || "请求失败"));
          }
        } else {
          console.error("❌ HTTP状态错误:", res.statusCode);
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      },
      fail: (err) => {
        console.error("❌ API请求失败:", {
          url: fullUrl,
          error: err,
        });
        reject(err);
      },
    });
  });
};

// ==================== 帖子相关API ====================

/**
 * 获取帖子列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码
 * @param {number} params.size - 每页大小
 * @param {string} params.sortField - 排序字段
 * @param {string} params.sortOrder - 排序方向
 * @param {string} params.searchText - 搜索关键词
 */
export const getPostList = (params = {}) => {
  const { size, ...otherParams } = params;
  return request("/post/list", {
    method: "POST",
    data: {
      current: 1,
      pageSize: size || 10,
      sortField: "createTime",
      sortOrder: "desc",
      ...otherParams,
    },
  });
};

/**
 * 获取帖子详情
 * @param {number} id - 帖子ID
 */
export const getPostDetail = (id) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  let url = `/post/detail?postId=${id}`;
  if (currentUserId) {
    url += `&userId=${currentUserId}`;
  }

  return request(url, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取用户的帖子列表
 * @param {number} userId - 用户ID
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码
 * @param {number} params.pageSize - 每页大小
 * @param {number} params.currentUserId - 当前登录用户ID（可选）
 */
export const getUserPosts = (userId, params = {}) => {
  const { current = 1, pageSize = 10, currentUserId } = params;
  let url = `/post/user?userId=${userId}&current=${current}&pageSize=${pageSize}`;

  if (currentUserId) {
    url += `&currentUserId=${currentUserId}`;
  }

  return request(url);
};

/**
 * 点赞帖子
 * @param {number} postId - 帖子ID
 * @param {number} userId - 用户ID
 */
export const likePost = (postId, userId) => {
  // 从缓存获取当前用户ID
  const currentUserId = userId || uni.getStorageSync("userid");
  return request(`/interaction/like?postId=${postId}&userId=${currentUserId}`, {
    method: "POST",
  });
};

/**
 * 取消点赞帖子
 * @param {number} postId - 帖子ID
 * @param {number} userId - 用户ID
 */
export const unlikePost = (postId, userId) => {
  // 从缓存获取当前用户ID
  const currentUserId = userId || uni.getStorageSync("userid");
  return request(
    `/interaction/unlike?postId=${postId}&userId=${currentUserId}`,
    {
      method: "POST",
    }
  );
};

/**
 * 发布帖子
 * @param {Object} postData - 帖子数据
 */
export const createPost = (postData) => {
  return request("/post/create", {
    method: "POST",
    data: postData,
  });
};

// ==================== 搜索相关API ====================

/**
 * 综合搜索
 * @param {Object} params - 搜索参数
 */
export const comprehensiveSearch = (params) => {
  return request("/search/comprehensive", {
    method: "GET",
    data: params,
  });
};

/**
 * 获取热门搜索词
 * @param {number} limit - 限制数量
 */
export const getHotKeywords = (limit = 10) => {
  return request(`/search/hot-keywords?limit=${limit}`);
};

/**
 * 获取搜索历史
 * @param {number} limit - 限制数量
 */
export const getSearchHistory = (limit = 20) => {
  return request(`/search/history?limit=${limit}`);
};

/**
 * 获取搜索建议
 * @param {string} keyword - 关键词
 * @param {number} limit - 限制数量
 */
export const getSearchSuggestions = (keyword, limit = 10) => {
  return request(
    `/search/suggestions?keyword=${encodeURIComponent(keyword)}&limit=${limit}`
  );
};

// ==================== 话题相关API ====================

/**
 * 获取热门话题（实际是tags）
 * @param {Object} params - 查询参数
 */
export const getHotTopics = (params = {}) => {
  const { limit = 8 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("bausertoken");

  return request(`/tag/hot?limit=${limit}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取热门帖子
 * @param {Object} params - 查询参数
 */
export const getHotPosts = (params = {}) => {
  const { limit = 4, timeRange = "7d" } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  // 将timeRange转换为days参数
  const daysMap = { "1d": 1, "3d": 3, "7d": 7, "30d": 30 };
  const days = daysMap[timeRange] || 7;

  return request(`/post/hot?pageSize=${limit}&days=${days}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取推荐用户
 * @param {Object} params - 查询参数
 */
export const getRecommendUsers = (params = {}) => {
  const { limit = 10 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/users/recommend?limit=${limit}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取精选内容
 * @param {Object} params - 查询参数
 */
export const getFeaturedContent = (params = {}) => {
  const { limit = 4, type = "all" } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/content/featured?limit=${limit}&type=${type}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取热门标签（保持兼容性）
 * @param {number} limit - 限制数量
 */
export const getHotTags = (limit = 20) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("bausertoken");

  return request(`/tag/hot?limit=${limit}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 关注用户
 * @param {number} userId - 要关注的用户ID
 */
export const followUser = (userId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/follow/user/${userId}`, {
    method: "POST",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 取消关注用户
 * @param {number} userId - 要取消关注的用户ID
 */
export const unfollowUser = (userId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/follow/user/${userId}`, {
    method: "DELETE",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 检查关注状态
 * @param {number} userId - 要检查的用户ID
 */
export const checkFollowStatus = (userId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/follow/status/${userId}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 批量检查关注状态
 * @param {Array} userIds - 要检查的用户ID列表
 */
export const batchCheckFollowStatus = (userIds) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request("/follow/batch-status", {
    method: "POST",
    data: userIds,
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取关注列表
 * @param {number} userId - 用户ID
 * @param {Object} params - 查询参数
 */
export const getFollowingList = (userId, params = {}) => {
  const { current = 1, size = 20 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(
    `/follow/following/${userId}?current=${current}&size=${size}`,
    {
      method: "GET",
      header: {
        bausertoken: token,
        userid: currentUserId,
      },
    }
  );
};

/**
 * 获取粉丝列表
 * @param {number} userId - 用户ID
 * @param {Object} params - 查询参数
 */
export const getFollowersList = (userId, params = {}) => {
  const { current = 1, size = 20 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(
    `/follow/followers/${userId}?current=${current}&size=${size}`,
    {
      method: "GET",
      header: {
        bausertoken: token,
        userid: currentUserId,
      },
    }
  );
};

/**
 * 获取新粉丝通知列表
 * @param {Object} params - 查询参数
 */
export const getNewFollowersNotifications = (params = {}) => {
  const { current = 1, size = 20 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(
    `/follow/notifications/followers?current=${current}&size=${size}`,
    {
      method: "GET",
      header: {
        bausertoken: token,
        userid: currentUserId,
      },
    }
  );
};

/**
 * 获取话题详情
 * @param {number} tagId - 话题ID
 */
export const getTagDetail = (tagId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("bausertoken");

  return request(`/tag/detail/${tagId}?userId=${currentUserId}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取话题下的帖子
 * @param {number} tagId - 话题ID
 * @param {Object} params - 查询参数
 */
export const getTagPosts = (tagId, params = {}) => {
  const { current = 1, size = 10, sortBy = "time" } = params;
  return request(
    `/tag/${tagId}/posts?current=${current}&size=${size}&sortBy=${sortBy}`
  );
};

/**
 * 关注话题
 * @param {number} tagId - 话题ID
 */
export const followTag = (tagId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("bausertoken");

  return request(`/tag/follow/${tagId}`, {
    method: "POST",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 取消关注话题
 * @param {number} tagId - 话题ID
 */
export const unfollowTag = (tagId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("bausertoken");

  return request(`/tag/unfollow/${tagId}`, {
    method: "POST",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

// ==================== 关注相关API ====================

// ==================== 消息通知相关API ====================

/**
 * 获取未读消息数
 */
export const getUnreadCount = () => {
  return request("/notifications/unread-count");
};

/**
 * 获取消息列表
 * @param {Object} params - 查询参数
 */
export const getNotificationList = (params = {}) => {
  const { type = "all", current = 1, size = 20 } = params;
  return request(
    `/notifications/list?type=${type}&current=${current}&size=${size}`
  );
};

/**
 * 标记消息已读
 * @param {Object} params - 参数
 */
export const markNotificationAsRead = (params) => {
  return request("/notifications/read", {
    method: "PUT",
    data: params,
  });
};

// ==================== 私信相关API ====================

/**
 * 发送消息
 * @param {Object} messageData - 消息数据
 */
export const sendMessage = (messageData) => {
  // 获取当前用户ID并添加到请求头
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request("/messages/send", {
    method: "POST",
    data: messageData,
    header: {
      bausertoken: token,
      userid: currentUserId, // 添加用户ID到请求头
    },
  });
};

/**
 * 获取会话列表
 * @param {Object} params - 查询参数
 */
export const getConversations = (params = {}) => {
  const { current = 1, size = 20 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/messages/conversations?current=${current}&size=${size}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取会话消息
 * @param {number} userId - 对方用户ID
 * @param {Object} params - 查询参数
 */
export const getConversationMessages = (userId, params = {}) => {
  const { current = 1, size = 20 } = params;
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(
    `/messages/conversation/${userId}?current=${current}&size=${size}`,
    {
      method: "GET",
      header: {
        bausertoken: token,
        userid: currentUserId,
      },
    }
  );
};

/**
 * 标记消息已读
 * @param {Object} params - 参数
 */
export const markMessageAsRead = (params) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request("/messages/read", {
    method: "PUT",
    data: params,
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取私信未读消息数
 */
export const getMessageUnreadCount = () => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request("/messages/unread-count", {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

// ==================== 用户相关API ====================

/**
 * 获取用户资料
 * @param {number} userId - 用户ID
 */
export const getUserProfile = (userId) => {
  return request(`/user/profile/${userId}`);
};

/**
 * 更新用户资料
 * @param {Object} profileData - 用户资料数据
 */
export const updateUserProfile = (profileData) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request("/user/profile", {
    method: "PUT",
    data: profileData,
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 获取用户作品统计信息
 * @param {number} userId - 用户ID
 */
export const getUserPostStats = (userId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/post/stats/${userId}`, {
    method: "GET",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 清空聊天记录（删除会话）
 * @param {number} chatId - 聊天对象ID
 */
export const clearChatHistory = (chatId) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request(`/messages/conversation/${chatId}`, {
    method: "DELETE",
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 举报用户
 * @param {Object} reportData - 举报数据
 */
export const reportUser = (reportData) => {
  const currentUserId = uni.getStorageSync("userid");
  const token = uni.getStorageSync("token");

  return request("/report/submit", {
    method: "POST",
    data: {
      ...reportData,
      reporterId: currentUserId,
    },
    header: {
      bausertoken: token,
      userid: currentUserId,
    },
  });
};

/**
 * 上传帖子图片到COS
 * @param {String} filePath - 图片文件路径
 * @param {String} name - 文件参数名（默认'file'）
 * @param {Object} formData - 额外的表单数据
 */
export const uploadPostImage = (filePath, name = "file", formData = {}) => {
  const token = uni.getStorageSync("bausertoken");
  const currentUserId = uni.getStorageSync("userid");

  console.log("🔥 帖子图片上传API调用:", { filePath, name, formData });

  return new Promise((resolve, reject) => {
    const baseUrl = getBaseUrl();
    uni.uploadFile({
      url: `${baseUrl}/api/upload/post-image`,
      filePath: filePath,
      name: name,
      formData: {
        driver: "cos",
        ...formData,
      },
      header: {
        bausertoken: token,
        userid: currentUserId,
      },
      success: (uploadFileRes) => {
        console.log("🔥 上传响应原始数据:", uploadFileRes);

        try {
          const result = JSON.parse(uploadFileRes.data);
          console.log("🔥 上传响应解析结果:", result);

          if (result.code === 0 && result.data) {
            // 兼容新的返回格式：{ imageUrl, thumbnailUrl }
            resolve({
              code: 0,
              data: result.data, // 可能是字符串URL或对象{imageUrl, thumbnailUrl}
              message: result.message || "success",
            });
          } else {
            console.error("❌ 上传失败，服务器返回错误:", result);
            reject(new Error(result.message || "上传失败"));
          }
        } catch (parseError) {
          console.error("❌ 解析上传响应失败:", parseError);
          reject(new Error("解析响应失败"));
        }
      },
      fail: (error) => {
        console.error("❌ 上传请求失败:", error);
        reject(new Error("上传请求失败"));
      },
    });
  });
};

/**
 * 批量上传帖子图片到COS（包含封面生成）
 * 注意：uni.uploadFile不支持真正的批量上传，这里逐个上传然后组合结果
 * @param {Array} filePaths - 图片文件路径数组
 */
export const uploadPostImages = async (filePaths) => {
  console.log("🔥 批量帖子图片上传API调用:", { filePaths });

  if (!filePaths || filePaths.length === 0) {
    throw new Error("文件路径数组不能为空");
  }

  try {
    const uploadPromises = filePaths.map((filePath, index) => {
      console.log(`🔥 开始上传第${index + 1}张图片:`, filePath);
      return uploadPostImage(filePath, "file", { driver: "cos" });
    });

    // 等待所有图片上传完成
    const results = await Promise.all(uploadPromises);
    console.log("🔥 所有图片上传完成:", results);

    // 处理上传结果
    const images = [];
    let coverImage = null;

    for (let i = 0; i < results.length; i++) {
      const result = results[i];

      if (result.code === 0 && result.data) {
        if (typeof result.data === "string") {
          // 旧格式：直接是URL字符串
          images.push(result.data);
          if (i === 0) {
            // 第一张图片作为封面，生成缩略图URL
            coverImage =
              result.data +
              "?imageMogr2/thumbnail/300x300>/format/webp/quality/85";
          }
        } else if (typeof result.data === "object") {
          // 新格式：包含imageUrl和thumbnailUrl
          images.push(result.data.imageUrl);
          if (i === 0) {
            coverImage = result.data.thumbnailUrl;
          }
        }
      } else {
        throw new Error(
          `第${i + 1}张图片上传失败: ${result.message || "未知错误"}`
        );
      }
    }

    const finalResult = {
      code: 0,
      data: {
        images: images,
        coverImage: coverImage,
      },
      message: "批量上传成功",
    };

    console.log("🔥 批量上传最终结果:", finalResult);
    return finalResult;
  } catch (error) {
    console.error("❌ 批量上传失败:", error);
    throw error;
  }
};

// ==================== 测试相关API ====================

/**
 * 健康检查
 */
export const healthCheck = () => {
  return request("/social/test/health");
};

/**
 * 测试阶段三功能
 */
export const testStageThree = () => {
  return request("/social/test/stage-three");
};

// ==================== 评论相关API ====================

/**
 * 获取评论列表
 * @param {Object} params - 参数对象
 * @param {number} params.userId - 用户ID
 * @param {string} params.contentId - 内容ID
 * @param {string} params.filter - 排序方式 (hot/time)
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 */
export const getCommentList = (params = {}) => {
  const { userId, contentId, filter = "hot", page = 1, size = 10 } = params;
  return request(
    `/comments?userId=${userId}&contentId=${contentId}&filter=${filter}&page=${page}&size=${size}`
  );
};

/**
 * 发表评论
 * @param {Object} commentData - 评论数据
 */
export const createComment = (commentData) => {
  return request("/comments", {
    method: "POST",
    data: commentData,
  });
};

/**
 * 发表帖子评论
 * @param {Object} commentData - 帖子评论数据
 */
export const createPostComment = (commentData) => {
  return request("/comments/post", {
    method: "POST",
    data: commentData,
  });
};

/**
 * 获取帖子评论列表
 * @param {String} postId - 帖子ID
 * @param {Object} params - 查询参数
 */
export const getPostComments = (postId, params = {}) => {
  const { userId = 1, filter = "hot", current = 1, pageSize = 10 } = params;
  return request(`/comments/post/${postId}`, {
    method: "GET",
    params: {
      userId,
      filter,
      current,
      pageSize,
    },
  });
};

/**
 * 点赞评论
 * @param {number} commentId - 评论ID
 * @param {Object} params - 参数
 */
export const likeComment = (commentId, params = {}) => {
  return request(`/comments/${commentId}/like`, {
    method: "POST",
    data: params,
  });
};

/**
 * 回复评论
 * @param {number} commentId - 评论ID
 * @param {Object} replyData - 回复数据
 */
export const replyComment = (commentId, replyData) => {
  return request(`/comments/${commentId}/replies`, {
    method: "POST",
    data: replyData,
  });
};

export default {
  // 帖子相关
  getPostList,
  getPostDetail,
  likePost,
  unlikePost,
  createPost,

  // 搜索相关
  comprehensiveSearch,
  getHotKeywords,
  getSearchHistory,
  getSearchSuggestions,

  // 话题相关
  getHotTags,
  getHotTopics,
  getTagDetail,
  getTagPosts,

  // 发现页面相关
  getHotPosts,
  getRecommendUsers,
  getFeaturedContent,

  // 关注相关
  followUser,
  unfollowUser,
  checkFollowStatus,
  batchCheckFollowStatus,
  getFollowingList,
  getFollowersList,
  getNewFollowersNotifications,

  // 消息通知相关
  getUnreadCount,
  getNotificationList,
  markNotificationAsRead,

  // 私信相关
  sendMessage,
  getConversations,
  getConversationMessages,
  markMessageAsRead,
  getMessageUnreadCount,

  // 聊天管理相关
  clearChatHistory,
  reportUser,

  // 用户相关
  getUserProfile,
  updateUserProfile,
  getUserPostStats,

  // 文件上传相关
  uploadPostImages,

  // 测试相关
  healthCheck,
  testStageThree,

  // 评论相关
  getCommentList,
  createComment,
  createPostComment,
  getPostComments,
  likeComment,
  replyComment,
};
