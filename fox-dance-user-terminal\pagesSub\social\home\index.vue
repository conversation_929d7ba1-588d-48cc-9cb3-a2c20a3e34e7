<template>
  <view class="home-container" :style="{ height: '100vh', overflow: 'hidden' }">
    <view class="header">
      <view class="header-content">
        <view class="logo">
          <image src="https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85" class="logo-text"></image>
        </view>
        <view class="header-actions">
          <u-icon name="search" size="24" color="#666" @click="goSearch"></u-icon>
        </view>
      </view>
    </view>

    <!-- 话题标签栏 -->
    <view class="topic-tabs-container">
      <u-tabs
        :list="topicList"
        :current="currentTopicIndex"
        @change="selectTopic"
        :scrollable="true"
        activeColor="#2979ff"
        inactiveColor="#666"
        fontSize="28"
        lineColor="#2979ff"
        lineWidth="40"
        lineHeight="6"
        height="80"
        itemStyle="padding: 0 32rpx;"
      ></u-tabs>
    </view>

    <!-- 帖子网格列表 -->
    <scroll-view
      class="post-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="post-grid">
        <PostCard
          v-for="post in postList"
          :key="post.id"
          :post="post"
          class="post-card-item"
          @click="goPostDetail"
          @user-click="goUserProfile"
          @like="onPostLike"
        />
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="loading && postList.length > 0">
        <u-loading mode="circle" size="24"></u-loading>
        <text class="load-text">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="load-more" v-if="!hasMore && postList.length > 0 && !loading">
        <text class="load-text no-more">没有更多帖子了</text>
      </view>

      <!-- 加载失败重试 -->
      <view class="load-more" v-if="loadError && postList.length > 0">
        <text class="load-text error" @click="retryLoad">加载失败，点击重试</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!postList.length && !loading" class="empty-state">
        <u-icon name="file-text" color="#ccc" size="120rpx"></u-icon>
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import PostCard from "../components/PostCard.vue";
import {
  getPostList,
  getHotTags,
  likePost,
  unlikePost
} from "@/utils/socialApi.js";

export default {
  name: "SocialHome",
  components: {
    PostCard
  },
  data() {
    return {
      postList: [],
      loading: false,
      refreshing: false,
      page: 1,
      pageSize: 10,
      currentTopic: "all",
      currentTopicIndex: 0,
      topicList: [
        { name: "全部", id: "all" }
      ],
      hasMore: true,
      loadError: false, // 加载错误状态
      isInitialized: false // 标记是否已初始化
    };
  },
  onLoad() {
    this.initializeData();
  },

  // 页面显示时重新加载数据
  onShow() {
    // 如果还没有初始化，或者数据为空，重新加载
    if (!this.isInitialized || !this.postList || this.postList.length === 0) {
      this.initializeData();
    }
  },

  // 组件激活时重新加载数据（用于keep-alive场景）
  activated() {
    // 如果还没有初始化，或者数据为空，重新加载
    if (!this.isInitialized || !this.postList || this.postList.length === 0) {
      this.initializeData();
    }
  },
  methods: {
    // 初始化数据
    async initializeData() {
      console.log("初始化首页数据...");
      try {
        await this.loadHotTags();
        await this.loadPosts(true);
        this.isInitialized = true;
      } catch (error) {
        console.error("初始化数据失败:", error);
      }
    },

    // 加载热门话题
    async loadHotTags() {
      try {
        const result = await getHotTags(10);
        console.log("热门标签API返回:", result);

        if (
          result &&
          result.code === 0 &&
          result.data &&
          result.data.length > 0
        ) {
          // 保留"全部"选项，添加热门话题
          const allOption = this.topicList[0];
          this.topicList = [
            allOption,
            ...result.data.map(tag => ({
              name: tag.name,
              id: tag.id
            }))
          ];
          console.log("话题列表更新:", this.topicList);
        }
      } catch (error) {
        console.error("加载热门话题失败:", error);
        // 使用默认话题列表
      }
    },

    async loadPosts(refresh = false) {
      if (this.loading) {
        console.log("正在加载中，跳过重复请求");
        return;
      }

      // 如果不是刷新且没有更多数据，直接返回
      if (!refresh && !this.hasMore) {
        console.log("没有更多数据，跳过加载");
        return;
      }

      console.log("开始加载帖子数据，refresh:", refresh, "page:", refresh ? 1 : this.page);
      this.loading = true;
      this.loadError = false; // 重置错误状态

      try {
        const params = {
          current: refresh ? 1 : this.page,
          size: this.pageSize,
          sortField: "createTime",
          sortOrder: "desc"
        };

        // 如果选择了特定话题，添加标签筛选
        if (this.currentTopic !== "all") {
          // 根据tagId获取标签名称
          const selectedTag = this.topicList.find(
            topic => topic.id === this.currentTopic
          );
          if (selectedTag) {
            params.tags = [selectedTag.name];
          }
        }

        console.log("API请求参数:", params);
        const result = await getPostList(params);
        console.log("API返回结果:", result);

        if (result.code == 0) {
          const posts = result.data.records.map(post => ({
            id: post.id,
            userId: post.userId, // 添加用户ID字段
            title: post.title || "",
            username: post.nickname || "无名氏",
            userAvatar:
              "https://file.foxdance.com.cn" + post.avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85'||
              "/static/images/toux.png",
            content: post.content,
            coverImage: post.coverImage,
            images: post.images || [],
            topics: post.tags || [],
            topicId: this.currentTopic,
            likeCount: post.likeCount || 0,
            commentCount: post.commentCount || 0,
            isLiked: post.isLiked || false,
            createTime: new Date(post.createTime)
          }));
          console.log("posts:", posts);

          if (refresh) {
            this.postList = posts;
            this.page = 2; // 下次加载第2页
          } else {
            this.postList = [...this.postList, ...posts];
            this.page++; // 页码递增
          }
          console.log("this.postList:", this.postList, "下次加载页码:", this.page);

          // 检查是否还有更多数据
          this.hasMore = posts.length >= this.pageSize;
          console.log("是否还有更多数据:", this.hasMore, "本次加载数量:", posts.length);

        } else {
          // API调用失败
          console.warn("获取帖子列表失败:", result);
          this.loadError = true;
          if (!refresh) {
            uni.showToast({
              title: result.message || "获取数据失败",
              icon: "none"
            });
          }
        }
      } catch (error) {
        console.error("加载帖子失败:", error);
        this.loadError = true;
        if (!refresh) {
          uni.showToast({
            title: "网络错误，请重试",
            icon: "none"
          });
        }
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    onRefresh() {
      console.log("刷新首页数据...");
      this.refreshing = true;
      this.page = 1;
      this.postList = [];
      this.hasMore = true;
      this.loadError = false;
      // 重新加载热门话题和帖子
      this.loadHotTags();
      this.loadPosts(true);
    },

    loadMore() {
      console.log("触发加载更多，当前状态 - loading:", this.loading, "hasMore:", this.hasMore, "page:", this.page);

      // 防止重复加载和无数据时加载
      if (this.loading || !this.hasMore || this.loadError) {
        console.log("跳过加载更多 - loading:", this.loading, "hasMore:", this.hasMore, "loadError:", this.loadError);
        return;
      }

      this.loadPosts(false);
    },

    // 重试加载
    retryLoad() {
      console.log("重试加载");
      this.loadError = false;
      this.loadPosts(false);
    },

    formatTime(time) {
      const now = new Date();
      const diff = now - new Date(time);
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);

      if (minutes < 60) return `${minutes}分钟前`;
      if (hours < 24) return `${hours}小时前`;
      return `${days}天前`;
    },

    async onPostLike(post) {
      try {
        if (post.isLiked) {
          // 取消点赞
          await unlikePost(post.id);
          post.isLiked = false;
          post.likeCount = Math.max(0, post.likeCount - 1);
        } else {
          // 点赞
          await likePost(post.id);
          post.isLiked = true;
          post.likeCount += 1;
        }

        // 更新帖子列表中的数据
        const index = this.postList.findIndex(p => p.id === post.id);
        if (index !== -1) {
          this.$set(this.postList, index, { ...post });
        }
      } catch (error) {
        console.error("点赞操作失败:", error);
        uni.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },

    goPostDetail(post) {
      uni.navigateTo({
        url: `/pagesSub/social/post/detail?id=${post.id}`
      });
    },

    goUserProfile(post) {
      if (!post.userId) {
        uni.showToast({
          title: "用户信息错误",
          icon: "none"
        });
        return;
      }

      console.log(
        "跳转到用户主页，用户ID:",
        post.userId,
        "用户名:",
        post.username
      );
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?id=${post.userId}`
      });
    },

    goSearch() {
      uni.navigateTo({
        url: "/pagesSub/social/search/index"
      });
    },

    selectTopic(index) {
      if (this.currentTopicIndex === index) return;

      console.log("切换话题:", this.topicList[index].name);
      this.currentTopicIndex = index;
      this.currentTopic = this.topicList[index].id;
      this.page = 1;
      this.postList = [];
      this.hasMore = true;
      this.loadError = false;

      // 重新加载帖子
      this.loadPosts(true);
    },

    // 格式化时间
    formatTime(dateString) {
      if (!dateString) return "";

      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;

      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (minutes < 1) return "刚刚";
      if (minutes < 60) return `${minutes}分钟前`;
      if (hours < 24) return `${hours}小时前`;
      if (days < 7) return `${days}天前`;

      return date.toLocaleDateString();
    },

    getEmptyText() {
      const currentTopicName =
        this.topicList.find(topic => topic.id === this.currentTopic)?.name ||
        "全部";
      return this.currentTopic === "all"
        ? "暂无帖子"
        : `暂无${currentTopicName}相关帖子`;
    },

    getEmptyDesc() {
      return this.currentTopic === "all"
        ? "快来发布第一条帖子吧"
        : "换个话题看看其他内容吧";
    },

    // 强制刷新数据（供父组件调用）
    forceRefresh() {
      console.log("强制刷新首页数据...");
      this.isInitialized = false;
      this.postList = [];
      this.page = 1;
      this.hasMore = true;
      this.loadError = false;
      this.initializeData();
    }
  }
};
</script>



<style lang="scss" scoped>
.home-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  margin: 0;
  padding: 0;
}

.header {
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
  flex-shrink: 0;
}

.header-content {
  height: 88rpx;

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.logo-text {
  width: 1328 * 0.11rpx;
  height: 625 * 0.11rpx;
  margin-left: -10rpx;
}

.topic-tabs-container {
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
  flex-shrink: 0;
}

/* uview tabs组件样式优化 */
.topic-tabs-container ::v-deep .u-tabs {
  background: #fff;
}

.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item {
  padding: 0 32rpx !important;
}

.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__item__text {
  font-size: 28rpx !important;
  font-weight: 500;
}

.topic-tabs-container ::v-deep .u-tabs__wrapper__nav__line {
  border-radius: 6rpx;
}

.post-list {
  flex: 1;
  height: 0; /* 重要：让flex子元素正确计算高度 */
  overflow: hidden;
}

/* scroll-view内部内容的样式 */
.post-list ::v-deep .uni-scroll-view {
  height: 100% !important;
  overflow-x: hidden !important;
}

.post-list ::v-deep .uni-scroll-view-content {
  min-height: 100%;
}

.post-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
  padding: 26rpx;
  padding-bottom: 100rpx; /* 底部留出更多空间 */
}

.post-card-item {
  width: calc(50% - 8rpx);
  margin-bottom: 16rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-text {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;

  &.no-more {
    color: #ccc;
    font-size: 26rpx;
    margin-left: 0;
  }

  &.error {
    color: #ff6b6b;
    margin-left: 0;
    padding: 16rpx 32rpx;
    border: 2rpx solid #ff6b6b;
    border-radius: 32rpx;
    background: rgba(255, 107, 107, 0.1);

    &:active {
      background: rgba(255, 107, 107, 0.2);
    }
  }
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .post-list {
    padding: 12rpx;
  }

  .post-grid {
    gap: 12rpx;
  }

  .post-card-item {
    width: calc(50% - 6rpx);
  }
}

@media screen and (min-width: 768px) {
  .post-grid {
    gap: 24rpx;
  }

  .post-card-item {
    width: calc(33.33% - 16rpx);
  }
}

@media screen and (min-width: 1024px) {
  .post-list {
    padding: 32rpx 64rpx;
  }

  .post-card-item {
    width: calc(25% - 18rpx);
  }
}

// 修改u-tab-itme默认样式
/deep/ .u-tab-item {
  padding: 0 25rpx;
}
</style>
