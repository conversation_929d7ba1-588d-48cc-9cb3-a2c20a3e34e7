package com.yupi.springbootinit.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索结果视图对象
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@ApiModel(description = "搜索结果视图对象")
public class SearchResultVO implements Serializable {

    /**
     * 搜索关键词
     */
    @ApiModelProperty(value = "搜索关键词", example = "街舞")
    private String keyword;

    /**
     * 总数量
     */
    @ApiModelProperty(value = "总数量", example = "100")
    private Long totalCount;

    /**
     * 帖子结果
     */
    @ApiModelProperty(value = "帖子结果")
    private List<PostSearchVO> posts;

    /**
     * 用户结果
     */
    @ApiModelProperty(value = "用户结果")
    private List<UserSearchVO> users;

    /**
     * 话题结果
     */
    @ApiModelProperty(value = "话题结果")
    private List<TagSearchVO> tags;

    /**
     * 是否有更多数据
     */
    @ApiModelProperty(value = "是否有更多数据", example = "true")
    private Boolean hasMore;

    /**
     * 搜索耗时（毫秒）
     */
    @ApiModelProperty(value = "搜索耗时", example = "50")
    private Long searchTime;

    private static final long serialVersionUID = 1L;

    // 手动添加getter/setter方法
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public List<PostSearchVO> getPosts() {
        return posts;
    }

    public void setPosts(List<PostSearchVO> posts) {
        this.posts = posts;
    }

    public List<UserSearchVO> getUsers() {
        return users;
    }

    public void setUsers(List<UserSearchVO> users) {
        this.users = users;
    }

    public List<TagSearchVO> getTags() {
        return tags;
    }

    public void setTags(List<TagSearchVO> tags) {
        this.tags = tags;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Long getSearchTime() {
        return searchTime;
    }

    public void setSearchTime(Long searchTime) {
        this.searchTime = searchTime;
    }

    /**
     * 帖子搜索结果
     */
    @ApiModel(description = "帖子搜索结果")
    public static class PostSearchVO implements Serializable {
        @ApiModelProperty(value = "帖子ID", example = "1")
        private Long id;

        @ApiModelProperty(value = "帖子内容", example = "今天学习了街舞")
        private String content;

        @ApiModelProperty(value = "封面图片", example = "https://example.com/image.jpg")
        private String coverImage;

        @ApiModelProperty(value = "用户昵称", example = "张小明")
        private String username;

        @ApiModelProperty(value = "用户头像", example = "https://example.com/avatar.jpg")
        private String userAvatar;

        @ApiModelProperty(value = "点赞数", example = "100")
        private Integer likeCount;

        @ApiModelProperty(value = "评论数", example = "20")
        private Integer commentCount;

        @ApiModelProperty(value = "创建时间", example = "2025-07-17 10:00:00")
        private String createTime;

        private static final long serialVersionUID = 1L;

        // getter/setter方法
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getCoverImage() {
            return coverImage;
        }

        public void setCoverImage(String coverImage) {
            this.coverImage = coverImage;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getUserAvatar() {
            return userAvatar;
        }

        public void setUserAvatar(String userAvatar) {
            this.userAvatar = userAvatar;
        }

        public Integer getLikeCount() {
            return likeCount;
        }

        public void setLikeCount(Integer likeCount) {
            this.likeCount = likeCount;
        }

        public Integer getCommentCount() {
            return commentCount;
        }

        public void setCommentCount(Integer commentCount) {
            this.commentCount = commentCount;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }
    }

    /**
     * 用户搜索结果
     */
    @ApiModel(description = "用户搜索结果")
    public static class UserSearchVO implements Serializable {
        @ApiModelProperty(value = "用户ID", example = "1")
        private Long userId;

        @ApiModelProperty(value = "用户昵称", example = "张小明")
        private String nickname;

        @ApiModelProperty(value = "用户头像", example = "https://example.com/avatar.jpg")
        private String avatar;

        @ApiModelProperty(value = "个人简介", example = "热爱街舞的舞者")
        private String bio;

        @ApiModelProperty(value = "舞种", example = "街舞")
        private String danceType;

        @ApiModelProperty(value = "帖子数", example = "50")
        private Integer postCount;

        @ApiModelProperty(value = "粉丝数", example = "1000")
        private Integer followerCount;

        @ApiModelProperty(value = "关注数", example = "500")
        private Integer followingCount;

        @ApiModelProperty(value = "是否已关注", example = "false")
        private Boolean isFollowed;

        private static final long serialVersionUID = 1L;

        // getter/setter方法
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getBio() {
            return bio;
        }

        public void setBio(String bio) {
            this.bio = bio;
        }

        public String getDanceType() {
            return danceType;
        }

        public void setDanceType(String danceType) {
            this.danceType = danceType;
        }

        public Integer getFollowerCount() {
            return followerCount;
        }

        public void setFollowerCount(Integer followerCount) {
            this.followerCount = followerCount;
        }

        public Integer getPostCount() {
            return postCount;
        }

        public void setPostCount(Integer postCount) {
            this.postCount = postCount;
        }

        public Integer getFollowingCount() {
            return followingCount;
        }

        public void setFollowingCount(Integer followingCount) {
            this.followingCount = followingCount;
        }

        public Boolean getIsFollowed() {
            return isFollowed;
        }

        public void setIsFollowed(Boolean isFollowed) {
            this.isFollowed = isFollowed;
        }
    }

    /**
     * 话题搜索结果
     */
    @ApiModel(description = "话题搜索结果")
    public static class TagSearchVO implements Serializable {
        @ApiModelProperty(value = "话题ID", example = "1")
        private Long tagId;

        @ApiModelProperty(value = "话题名称", example = "街舞")
        private String tagName;

        @ApiModelProperty(value = "话题描述", example = "街舞相关内容")
        private String description;

        @ApiModelProperty(value = "封面图片", example = "https://example.com/cover.jpg")
        private String coverImage;

        @ApiModelProperty(value = "帖子数量", example = "500")
        private Integer postCount;

        @ApiModelProperty(value = "关注数", example = "1000")
        private Integer followCount;

        @ApiModelProperty(value = "是否已关注", example = "false")
        private Boolean isFollowed;

        private static final long serialVersionUID = 1L;

        // getter/setter方法
        public Long getTagId() {
            return tagId;
        }

        public void setTagId(Long tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getCoverImage() {
            return coverImage;
        }

        public void setCoverImage(String coverImage) {
            this.coverImage = coverImage;
        }

        public Integer getPostCount() {
            return postCount;
        }

        public void setPostCount(Integer postCount) {
            this.postCount = postCount;
        }

        public Integer getFollowCount() {
            return followCount;
        }

        public void setFollowCount(Integer followCount) {
            this.followCount = followCount;
        }

        public Boolean getIsFollowed() {
            return isFollowed;
        }

        public void setIsFollowed(Boolean isFollowed) {
            this.isFollowed = isFollowed;
        }
    }
}
