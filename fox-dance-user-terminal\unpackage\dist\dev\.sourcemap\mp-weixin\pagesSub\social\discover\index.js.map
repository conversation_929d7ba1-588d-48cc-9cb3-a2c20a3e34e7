{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?de44", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?4e32", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?ead9", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?4ea8", "uni-app:///pagesSub/social/discover/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?659a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/discover/index.vue?132d", "uni-app:///main.js"], "names": ["name", "components", "FollowButton", "data", "hotTopics", "recommendUsers", "hotPosts", "<PERSON><PERSON><PERSON><PERSON>", "loading", "page", "topics", "users", "posts", "featured", "onLoad", "onShow", "console", "activated", "methods", "loadDiscoverData", "Promise", "uni", "title", "icon", "loadHotTopics", "limit", "result", "id", "cover", "tag", "postCount", "description", "getDefaultTopics", "loadRecommendUsers", "nickname", "avatar", "isFollowed", "followersCount", "getDefaultRecommendUsers", "loadHotPosts", "timeRange", "username", "userAvatar", "content", "coverImage", "likeCount", "commentCount", "createTime", "formatAvatarUrl", "truncate<PERSON><PERSON><PERSON>", "getDefaultPosts", "loadFeaturedContent", "subtitle", "onUserFollow", "user", "url", "topic", "post", "item", "currentUserId", "userIds", "map", "filter", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoKlwB;AAMA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EAEAC;IACA;IACA;MACAC;MACA;IACA;EACA;EAEA;EACAC;IACA;MACAD;MACA;IACA;EACA;EACAE;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;kBACAV;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACAO,aACA,uBACA,4BACA,sBACA,4BACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAJ;gBACA;gBAEAK;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAEAR;gBAAA;gBAAA,OACA;kBAAAS;gBAAA;cAAA;gBAAAC;gBACAV;gBAEA;kBACAA;kBACAU;oBACAV;kBACA;kBAEA;oBAAA;sBACAW;sBACA3B;sBACA4B,OACAC,iBACA;sBACAC;sBACAC;oBACA;kBAAA;kBACAf;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgB;MACA,QACA;QACAL;QACA3B;QACA4B;QACAE;MACA,GACA;QACAH;QACA3B;QACA4B;QACAE;MACA,GACA;QACAH;QACA3B;QACA4B;QACAE;MACA,GACA;QACAH;QACA3B;QACA4B;QACAE;MACA,EACA;IACA;IAEAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAEAjB;gBAAA;gBAAA,OACA;kBAAAS;gBAAA;cAAA;gBAAAC;gBACAV;gBAEA;kBACA;oBAAA;sBACAW;sBACAO;sBACAC;sBACAJ;sBACAK;sBAAA;sBACAC;sBAAA;sBACAP;oBACA;kBAAA;kBACAd;;kBAEA;kBACA;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MACA,QACA;QACAX;QACAO;QACAC;QACAJ;QACAK;QACAC;QACAP;MACA,EACA;IACA;IAEAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAEAvB;gBAAA;gBAAA,OACA;kBAAAS;kBAAAe;gBAAA;cAAA;gBAAAd;gBACAV;gBAEA;kBACAJ,qCACAc,cACAA;kBACA;oBAAA;sBACAC;sBACAc;sBACAC;sBACApB;sBAAA;sBACAqB;sBAAA;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBAAA;kBACA/B;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgC;MACA;QACA;MACA;MACA,OACA,iCACAb,SACA;IAEA;IAEA;IACAc;MACA;MACA;IACA;IAEA;IACAC;MACA,QACA;QACAvB;QACAc;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAnB;QACAc;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAK;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAEA;gBACA,0BACA;kBACAxB;kBACAL;kBACA8B;kBACAxB;gBACA,GACA;kBACAD;kBACAL;kBACA8B;kBACAxB;gBACA,EACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAqC;MACArC;MACA;IACA;EAAA,iFAEAb;IACAa;IACA;IACA;MAAA;IAAA;IACA;MACAsC;MACA;MACA;QACAA;MACA;IACA;EACA,sFAEAnD;IACAa;IACA;IACA;MAAA;IAAA;IACA;MACAsC;MACA;MACA;QACAA;MACA;IACA;EACA,sFAEAnD;IACA;IACA;MAAA;IAAA;IACA;MACAmD;IACA;IACAtC;EACA,4EAEA;IACAK;MACAkC;IACA;EACA,wEAEAC;IACAnC;MACAkC;IACA;EACA,kFAEA;IACAlC;MACAkC;IACA;EACA,oFAGA;IACAvC;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACAP;MACAC;MACAC;MACAC;MACAC;IACA;;IAEA;IACA;EACA,oFAEAyC;IACAjC;MACAkC;IACA;EACA,kFAEAE;IACApC;MACAkC;IACA;EACA,0FAEAG;IACArC;MACAkC;IACA;EACA,wGAGA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,MACA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAEAI;cAAA,IACAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAGAC,gCACAC;gBAAA;cAAA,GACAC;gBAAA;cAAA;cAAA,MACAF;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAAA,OAEA;YAAA;cAAAlC;cACAV;cAEA;gBACA;gBACA;kBACA;oBACAsC;kBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAtC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AAIA;AAAA,2B;;;;;;;;;;;;;AChmBA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACA+C,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pagesSub/social/discover/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1fcbd0ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/discover/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1fcbd0ae&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"discover-container\">\n    <!-- 页面加载动画 -->\n    <view v-if=\"loading.page\" class=\"page-loading\">\n      <view class=\"loading-container\">\n        <view class=\"loading-spinner\">\n          <u-loading mode=\"circle\" size=\"40\" color=\"#ff6b87\"></u-loading>\n        </view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </view>\n\n    <!-- 主要内容 -->\n    <view v-else>\n      <!-- 顶部搜索 -->\n      <view class=\"header\">\n        <view class=\"search-bar\" @click=\"goSearch\">\n          <u-icon name=\"search\" size=\"18\" color=\"#999\"></u-icon>\n          <text class=\"search-placeholder\">搜索话题、用户...</text>\n        </view>\n      </view>\n\n    <scroll-view class=\"content\" scroll-y>\n      <!-- 热门话题 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门话题</text>\n          <text class=\"more-btn\" @click=\"goTopicList\">更多</text>\n        </view>\n\n        <!-- 话题加载状态 -->\n        <view v-if=\"loading.topics\" class=\"section-loading\">\n          <view class=\"loading-container\">\n            <u-loading mode=\"circle\" size=\"32\" color=\"#ff6b87\"></u-loading>\n            <text class=\"loading-text\">加载话题中...</text>\n          </view>\n        </view>\n\n        <!-- 话题实际内容 -->\n        <view v-else class=\"topic-grid\">\n          <view\n            v-for=\"topic in hotTopics\"\n            :key=\"topic.id\"\n            class=\"topic-card\"\n            @click=\"goTopic(topic)\"\n          >\n            <image :src=\"topic.cover\" class=\"topic-cover\" mode=\"aspectFill\" />\n            <view class=\"topic-info\">\n              <text class=\"topic-name\">#{{ topic.name }}</text>\n              <text class=\"topic-count\">{{ topic.postCount }}条帖子</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 推荐用户 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">推荐关注</text>\n        </view>\n\n        <!-- 用户加载状态 -->\n        <view v-if=\"loading.users\" class=\"section-loading\">\n          <view class=\"loading-container\">\n            <u-loading mode=\"circle\" size=\"32\" color=\"#ff6b87\"></u-loading>\n            <text class=\"loading-text\">加载用户中...</text>\n          </view>\n        </view>\n\n        <!-- 用户实际内容 -->\n        <scroll-view v-else class=\"user-scroll\" scroll-x>\n          <view class=\"user-list\">\n            <view\n              v-for=\"user in recommendUsers\"\n              :key=\"user.id\"\n              class=\"user-card\"\n              @click=\"goUserProfile(user)\"\n            >\n              <u-avatar :src=\"user.avatar\" size=\"60\"></u-avatar>\n              <text class=\"user-name\">{{ user.nickname }}</text>\n              <text class=\"user-desc\">{{ user.description }}</text>\n              <FollowButton\n                :user=\"user\"\n                :followed=\"user.isFollowed\"\n                size=\"mini\"\n                @follow=\"onUserFollow\"\n                @change=\"onFollowChange\"\n                @click.stop\n              />\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 热门帖子 -->\n      <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门帖子</text>\n        </view>\n\n        <!-- 帖子加载状态 -->\n        <view v-if=\"loading.posts\" class=\"section-loading\">\n          <view class=\"loading-container\">\n            <u-loading mode=\"circle\" size=\"32\" color=\"#ff6b87\"></u-loading>\n            <text class=\"loading-text\">加载帖子中...</text>\n          </view>\n        </view>\n\n        <!-- 帖子实际内容 -->\n        <view v-else class=\"hot-posts\">\n          <view\n            v-for=\"post in hotPosts\"\n            :key=\"post.id\"\n            class=\"hot-post-item\"\n            @click=\"goPostDetail(post)\"\n          >\n            <view class=\"post-content\">\n              <view class=\"user-info\">\n                <u-avatar :src=\"post.userAvatar\" size=\"32\"></u-avatar>\n                <text class=\"username\">{{ post.username }}</text>\n              </view>\n              <text class=\"post-text\">{{ post.title || post.content || '无标题' }}</text>\n              <view class=\"post-stats\">\n                <text class=\"stat-item\">{{ post.likeCount }}赞</text>\n                <text class=\"stat-item\">{{ post.commentCount }}评论</text>\n              </view>\n            </view>\n            <image\n              v-if=\"post.coverImage\"\n              :src=\"post.coverImage\"\n              class=\"post-cover\"\n              mode=\"aspectFill\"\n            />\n          </view>\n        </view>\n      </view>\n\n      <!-- 精选内容 -->\n      <!-- <view class=\"section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">精选内容</text>\n        </view>\n        <view class=\"featured-grid\">\n          <view\n            v-for=\"item in featuredContent\"\n            :key=\"item.id\"\n            class=\"featured-item\"\n            @click=\"goFeaturedDetail(item)\"\n          >\n            <image :src=\"item.cover\" class=\"featured-cover\" mode=\"aspectFill\" />\n            <view class=\"featured-overlay\">\n              <text class=\"featured-title\">{{ item.title }}</text>\n              <text class=\"featured-subtitle\">{{ item.subtitle }}</text>\n            </view>\n          </view>\n        </view>\n      </view>-->\n    </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport FollowButton from \"../components/FollowButton.vue\";\nimport {\n  getHotTopics,\n  getHotPosts,\n  getRecommendUsers,\n  getFeaturedContent,\n  batchCheckFollowStatus\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"SocialDiscover\",\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      hotTopics: [],\n      recommendUsers: [],\n      hotPosts: [],\n      featuredContent: [],\n      loading: {\n        page: true,        // 页面整体加载\n        topics: true,      // 热门话题加载\n        users: true,       // 推荐用户加载\n        posts: true,       // 热门帖子加载\n        featured: true     // 精选内容加载\n      }\n    };\n  },\n  onLoad() {\n    this.loadDiscoverData();\n  },\n\n  onShow() {\n    // 页面显示时检查是否需要加载数据\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log(\"发现页显示时重新加载数据\");\n      this.loadDiscoverData();\n    }\n  },\n\n  // 组件激活时重新加载数据（用于keep-alive场景）\n  activated() {\n    if (!this.hotTopics || this.hotTopics.length === 0) {\n      console.log(\"发现页激活时重新加载数据\");\n      this.loadDiscoverData();\n    }\n  },\n  methods: {\n    // 初始化页面数据\n    async loadDiscoverData() {\n      try {\n        // 设置加载状态\n        this.loading = {\n          page: true,\n          topics: true,\n          users: true,\n          posts: true,\n          featured: true\n        }\n\n        // 并行加载数据\n        await Promise.all([\n          this.loadHotTopics(),\n          this.loadRecommendUsers(),\n          this.loadHotPosts(),\n          this.loadFeaturedContent()\n        ])\n\n        // 所有数据加载完成，关闭页面加载状态\n        this.loading.page = false\n\n      } catch (error) {\n        console.error('页面数据初始化失败:', error)\n        this.loading.page = false\n\n        uni.showToast({\n          title: '页面加载失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    async loadHotTopics() {\n      try {\n        this.loading.topics = true\n\n        console.log(\"开始加载热门话题...\");\n        const result = await getHotTopics({ limit: 4 });\n        console.log(\"热门话题API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          console.log(\"🔥 API返回的原始标签数据:\", result.data);\n          result.data.forEach(tag => {\n            console.log(`🔥 标签 ${tag.name} - coverImage: ${tag.coverImage}`);\n          });\n\n          this.hotTopics = result.data.map(tag => ({\n            id: tag.id,\n            name: tag.name,\n            cover:\n              tag.coverImage +\n              \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\",\n            postCount: tag.useCount || 0,\n            description: tag.description\n          }));\n          console.log(\"🔥 处理后的热门话题:\", this.hotTopics);\n        } else {\n          console.error(\"热门话题API返回格式不正确:\", result);\n          // 使用默认数据作为后备\n          this.hotTopics = this.getDefaultTopics();\n        }\n\n        this.loading.topics = false\n      } catch (error) {\n        console.error(\"加载热门话题失败:\", error);\n        this.loading.topics = false\n        // 使用默认数据作为后备\n        this.hotTopics = this.getDefaultTopics();\n      }\n    },\n\n    // 默认话题数据（后备方案）\n    getDefaultTopics() {\n      return [\n        {\n          id: 1,\n          name: \"街舞\",\n          cover: \"https://picsum.photos/200/120?random=1\",\n          postCount: 1234\n        },\n        {\n          id: 2,\n          name: \"爵士舞\",\n          cover: \"https://picsum.photos/200/120?random=2\",\n          postCount: 856\n        },\n        {\n          id: 3,\n          name: \"芭蕾\",\n          cover: \"https://picsum.photos/200/120?random=3\",\n          postCount: 642\n        },\n        {\n          id: 4,\n          name: \"现代舞\",\n          cover: \"https://picsum.photos/200/120?random=4\",\n          postCount: 789\n        }\n      ];\n    },\n\n    async loadRecommendUsers() {\n      try {\n        this.loading.users = true\n\n        console.log(\"开始加载推荐用户...\");\n        const result = await getRecommendUsers({ limit: 10 });\n        console.log(\"推荐用户API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          this.recommendUsers = result.data.map(user => ({\n            id: user.userId || user.id,\n            nickname: user.nickname || \"用户\",\n            avatar: this.formatAvatarUrl(user.avatar),\n            description: user.bio || \"暂无简介\",\n            isFollowed: user.isFollowed || false, // 使用后端返回的关注状态\n            followersCount: user.followerCount || 0, // 修正字段名：followerCount\n            postCount: user.postCount || 0\n          }));\n          console.log(\"推荐用户加载成功:\", this.recommendUsers);\n\n          // 批量检查关注状态\n          this.batchCheckFollowStatus();\n        } else {\n          console.error(\"推荐用户API返回格式不正确:\", result);\n          // 使用默认数据作为后备\n          this.recommendUsers = this.getDefaultRecommendUsers();\n        }\n\n        this.loading.users = false\n      } catch (error) {\n        console.error(\"加载推荐用户失败:\", error);\n        this.loading.users = false\n        // 使用默认数据作为后备\n        this.recommendUsers = this.getDefaultRecommendUsers();\n      }\n    },\n\n    // 默认推荐用户数据（后备方案）\n    getDefaultRecommendUsers() {\n      return [\n        {\n          id: 18,\n          nickname: \"舞蹈达人\",\n          avatar: \"https://picsum.photos/100/100?random=18\",\n          description: \"分享舞蹈技巧和心得\",\n          isFollowed: false,\n          followersCount: 1024,\n          postCount: 128\n        }\n      ];\n    },\n\n    async loadHotPosts() {\n      try {\n        this.loading.posts = true\n\n        console.log(\"开始加载热门帖子...\");\n        const result = await getHotPosts({ limit: 4, timeRange: \"7d\" });\n        console.log(\"热门帖子API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const posts = Array.isArray(result.data)\n            ? result.data\n            : result.data.records || [];\n          this.hotPosts = posts.map(post => ({\n            id: post.id,\n            username: post.nickname || post.username || \"用户\",\n            userAvatar: this.formatAvatarUrl(post.avatar || post.userAvatar),\n            title: post.title || \"\", // 添加标题字段\n            content: this.truncateContent(post.title || post.content || \"\"), // 优先显示标题，没有标题则显示内容\n            coverImage: post.coverImage,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            createTime: post.createTime\n          }));\n          console.log(\"热门帖子加载成功:\", this.hotPosts);\n        } else {\n          console.error(\"热门帖子API返回格式不正确:\", result);\n          // 使用默认数据作为后备\n          this.hotPosts = this.getDefaultPosts();\n        }\n\n        this.loading.posts = false\n      } catch (error) {\n        console.error(\"加载热门帖子失败:\", error);\n        this.loading.posts = false\n        // 使用默认数据作为后备\n        this.hotPosts = this.getDefaultPosts();\n      }\n    },\n\n    // 格式化头像URL\n    formatAvatarUrl(avatar) {\n      if (!avatar) {\n        return \"/static/images/toux.png\";\n      }\n      return (\n        \"https://file.foxdance.com.cn\" +\n        avatar +\n        \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\"\n      );\n    },\n\n    // 截断内容\n    truncateContent(content) {\n      if (!content) return \"暂无内容\";\n      return content.length > 50 ? content.substring(0, 50) + \"...\" : content;\n    },\n\n    // 默认帖子数据（后备方案）\n    getDefaultPosts() {\n      return [\n        {\n          id: 1,\n          username: \"舞蹈达人\",\n          userAvatar: \"https://picsum.photos/100/100?random=20\",\n          content: \"今天的舞蹈练习分享，基础动作很重要...\",\n          coverImage: \"https://picsum.photos/120/120?random=30\",\n          likeCount: 234,\n          commentCount: 45\n        },\n        {\n          id: 2,\n          username: \"街舞教练\",\n          userAvatar: \"https://picsum.photos/100/100?random=21\",\n          content: \"新手入门街舞的几个要点，记得收藏！\",\n          coverImage: \"https://picsum.photos/120/120?random=31\",\n          likeCount: 189,\n          commentCount: 32\n        }\n      ];\n    },\n\n    async loadFeaturedContent() {\n      try {\n        this.loading.featured = true\n\n        // 模拟异步加载延迟\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n        // 模拟精选内容数据\n        this.featuredContent = [\n          {\n            id: 1,\n            title: \"春日穿搭指南\",\n            subtitle: \"时尚达人教你搭配\",\n            cover: \"https://picsum.photos/300/200?random=40\"\n          },\n          {\n            id: 2,\n            title: \"周末好去处\",\n            subtitle: \"城市探索攻略\",\n            cover: \"https://picsum.photos/300/200?random=41\"\n          }\n        ];\n\n        this.loading.featured = false\n      } catch (error) {\n        console.error(\"加载精选内容失败:\", error);\n        this.loading.featured = false\n      }\n    },\n\n    onUserFollow(data) {\n      console.log(\"关注操作:\", data);\n      // 这里可以调用API进行关注/取消关注操作\n    },\n\n    onUserFollow(data) {\n      console.log(\"用户关注成功:\", data);\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id);\n      if (user) {\n        user.isFollowed = true;\n        // 可以增加粉丝数\n        if (user.followersCount !== undefined) {\n          user.followersCount += 1;\n        }\n      }\n    },\n\n    onUserUnfollow(data) {\n      console.log(\"用户取消关注成功:\", data);\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id);\n      if (user) {\n        user.isFollowed = false;\n        // 可以减少粉丝数\n        if (user.followersCount !== undefined && user.followersCount > 0) {\n          user.followersCount -= 1;\n        }\n      }\n    },\n\n    onFollowChange(data) {\n      // 更新本地数据\n      const user = this.recommendUsers.find(u => u.id === data.user.id);\n      if (user) {\n        user.isFollowed = data.isFollowed;\n      }\n      console.log(\"关注状态变化:\", data);\n    },\n\n    goSearch() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/search/index\"\n      });\n    },\n\n    goTopic(topic) {\n      uni.navigateTo({\n        url: `/pagesSub/social/topic/detail?id=${topic.id}`\n      });\n    },\n\n    goTopicList() {\n      uni.navigateTo({\n        url: \"/pagesSub/social/topic/list\"\n      });\n    },\n\n    // 强制刷新数据（供父组件调用）\n    forceRefresh() {\n      console.log(\"强制刷新发现页数据...\");\n\n      // 重置数据\n      this.hotTopics = [];\n      this.recommendUsers = [];\n      this.hotPosts = [];\n      this.featuredContent = [];\n\n      // 重置加载状态\n      this.loading = {\n        page: true,\n        topics: true,\n        users: true,\n        posts: true,\n        featured: true\n      }\n\n      // 重新加载数据\n      this.loadDiscoverData();\n    },\n\n    goUserProfile(user) {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${user.id}`\n      });\n    },\n\n    goPostDetail(post) {\n      uni.navigateTo({\n        url: `/pagesSub/social/post/detail?id=${post.id}`\n      });\n    },\n\n    goFeaturedDetail(item) {\n      uni.navigateTo({\n        url: `/pagesSub/social/featured/detail?id=${item.id}`\n      });\n    },\n\n    // 批量检查关注状态\n    async batchCheckFollowStatus() {\n      if (!this.recommendUsers || this.recommendUsers.length === 0) return;\n\n      const currentUserId = uni.getStorageSync(\"userid\");\n      if (!currentUserId) return;\n\n      try {\n        const userIds = this.recommendUsers\n          .map(user => user.id)\n          .filter(id => id != currentUserId);\n        if (userIds.length === 0) return;\n\n        const result = await batchCheckFollowStatus(userIds);\n        console.log(\"批量检查关注状态结果:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          // 更新关注状态\n          this.recommendUsers.forEach(user => {\n            if (result.data[user.id] !== undefined) {\n              user.isFollowed = result.data[user.id];\n            }\n          });\n        }\n      } catch (error) {\n        console.error(\"批量检查关注状态失败:\", error);\n      }\n    },\n\n\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n/* 页面加载动画 */\n.page-loading {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #f8f9fa;\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-spinner {\n  margin-bottom: 32rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999;\n  margin-top: 16rpx;\n}\n\n.discover-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 200rpx;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  padding: var(--status-bar-height) 32rpx 24rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.search-bar {\n  height: 72rpx;\n  background: #f5f5f5;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 32rpx;\n}\n\n.search-placeholder {\n  margin-left: 16rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.content {\n  margin-top: calc(125rpx + var(--status-bar-height));\n  padding: 0 32rpx;\n  width: auto;\n}\n\n.section {\n  margin-bottom: 48rpx;\n}\n\n.section-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 24rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.more-btn {\n  font-size: 28rpx;\n  color: #2979ff;\n}\n\n.topic-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 22rpx;\n}\n\n.topic-card {\n  width: calc(50% - 12rpx);\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.topic-cover {\n  width: 100%;\n  height: 160rpx;\n}\n\n.topic-info {\n  padding: 24rpx;\n}\n\n.topic-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n}\n\n.topic-count {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n.user-scroll {\n  white-space: nowrap;\n}\n\n.user-list {\n  display: flex;\n  gap: 32rpx;\n  padding-bottom: 16rpx;\n}\n\n.user-card {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 32rpx 24rpx;\n  min-width: 200rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.user-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin: 16rpx 0 8rpx;\n  text-align: center;\n}\n\n.user-desc {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 24rpx;\n  text-align: center;\n}\n\n.hot-posts {\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.hot-post-item {\n  display: flex;\n  padding: 32rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n}\n\n.hot-post-item:last-child {\n  border-bottom: none;\n}\n\n.post-content {\n  flex: 1;\n  margin-right: 24rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.username {\n  margin-left: 16rpx;\n  font-size: 26rpx;\n  color: #666;\n}\n\n.post-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.4;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.post-stats {\n  display: flex;\n  gap: 32rpx;\n}\n\n.stat-item {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.post-cover {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 16rpx;\n}\n\n.featured-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n}\n\n.featured-item {\n  position: relative;\n  height: 240rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.featured-cover {\n  width: 100%;\n  height: 100%;\n}\n\n.featured-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n  padding: 40rpx 32rpx 32rpx;\n}\n\n.featured-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #fff;\n  display: block;\n}\n\n.featured-subtitle {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 8rpx;\n  display: block;\n}\n\n/* 分段加载动画样式 */\n.section-loading {\n  padding: 80rpx 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.section-loading .loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.section-loading .loading-text {\n  font-size: 28rpx;\n  color: #909399;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1fcbd0ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751618263\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/discover/index.vue'\ncreatePage(Page)"], "sourceRoot": ""}