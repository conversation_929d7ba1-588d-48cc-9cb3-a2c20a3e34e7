{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?ebfe", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?c66b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?bb26", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?5b03", "uni-app:///pagesSub/social/search/index.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?597a", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/search/index.vue?d3bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "PostCard", "UserCard", "TagCard", "data", "keyword", "searched", "searchHistory", "hotSearches", "results", "loading", "searchType", "pagination", "current", "size", "hasMore", "searchResults", "posts", "users", "tags", "totalCount", "currentUserId", "computed", "hasSearchResults", "onLoad", "methods", "loadInitialData", "Promise", "hotKeywordsResult", "historyResult", "console", "loadHotKeywords", "result", "loadSearchHistory", "localHistory", "loadCurrentUserId", "onSearch", "uni", "title", "icon", "performSearch", "loadMore", "params", "type", "userId", "searchData", "total", "updateResultsByType", "convertPostsForDisplay", "id", "content", "username", "userAvatar", "coverImage", "likeCount", "commentCount", "createTime", "isLiked", "isFavorited", "convertUsersForDisplay", "nickname", "avatar", "bio", "postCount", "followerCount", "followingCount", "isFollowed", "isVerified", "convertTagsForDisplay", "tagId", "tagName", "description", "viewCount", "isHot", "formatAvatarUrl", "updateHistory", "clearHistory", "success", "onTagClick", "generateMockResults", "mockPosts", "loadMoreResults", "changeSearchType", "backToHome"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,kOAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9JA;AAAA;AAAA;AAAA;AAA8uB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsOlwB;AAKA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAC;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA,0EACA,mEACA;MACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC,aACA,yBACA,0BACA;cAAA;gBAAA;gBAAA;gBAHAC;gBAAAC;gBAKAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACAF;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAD;gBACA;kBACA;kBACAF;gBACA;kBACA;kBACAI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACA;gBACAI;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;gBACA;kBACAL;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAlC;gBACA;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;kBACAY;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAU;gBACAO;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAEAC;kBACArC;kBACAQ;kBACAC;kBACA6B;kBACAC;gBACA;gBAEAd;gBAAA;gBAAA,OACA;cAAA;gBAAAE;gBACAF;gBAEA;kBACAe;kBAEA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;oBACA;oBACA;sBACA5B;sBACAC;sBACAC;sBACAC;oBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;kBAEAU;oBACAb;oBACAC;oBACAC;oBACA2B;kBACA;gBAEA;kBACAhB;kBACA;oBACAb;oBACAC;oBACAC;oBACAC;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAU;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAiB;MACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;QAAA;UACAC;UACAX;UACAY;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;UACAV;UACAL;UACAO;UACAS;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAC;MACA;QAAA;UACAnB;UACAoB;UACAtE;UACAuE;UACAC;UACAlB;UACAU;UACAS;UACAN;UACAO;QACA;MAAA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;QACA;;QAEA;QACAtC;QAEAP;MACA;QACAA;MACA;IACA;IACA;IACA8C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAvC;oBACAC;oBACAY;oBACA2B;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACA;kCACA;kCACAxC;kCAEAA;oCACAC;oCACAC;kCACA;kCAEAT;gCACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;kBACA;gBACA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAgD;MACA;MACA;IACA;IACAC;MACA;MACA;QACAC;UACA/B;UACAX;UACAa;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEA;IACA0B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnD;gBACA;gBACAO;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2C;MACA;MAEA;MACApD;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAqD;MACA;MACA;MACA;MACA;QACAlE;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACppBA;AAAA;AAAA;AAAA;AAAi6C,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACAr7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/search/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/search/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2813d7e8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2813d7e8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2813d7e8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/search/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2813d7e8&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-search/u-search\" */ \"@/components/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tag/u-tag\" */ \"@/components/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-empty/u-empty\" */ \"@/components/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.searched ? _vm.loading && _vm.results.length === 0 : null\n  var g1 =\n    _vm.searched && !g0 && _vm.hasSearchResults && false\n      ? _vm.results.length\n      : null\n  var g2 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    false &&\n    _vm.searchType === \"tag\"\n      ? JSON.stringify(_vm.searchResults.tags)\n      : null\n  var g3 =\n    _vm.searched && !g0 && _vm.hasSearchResults && _vm.searchType === \"all\"\n      ? _vm.searchResults.users && _vm.searchResults.users.length > 0\n      : null\n  var l0 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g3\n      ? _vm.convertUsersForDisplay(_vm.searchResults.users.slice(0, 3))\n      : null\n  var g4 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g3\n      ? _vm.searchResults.users.length\n      : null\n  var g5 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g3 &&\n    g4 > 3\n      ? _vm.searchResults.users.length\n      : null\n  var g6 =\n    _vm.searched && !g0 && _vm.hasSearchResults && _vm.searchType === \"all\"\n      ? _vm.searchResults.tags && _vm.searchResults.tags.length > 0\n      : null\n  var l1 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g6\n      ? _vm.convertTagsForDisplay(_vm.searchResults.tags.slice(0, 3))\n      : null\n  var g7 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g6\n      ? _vm.searchResults.tags.length\n      : null\n  var g8 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g6 &&\n    g7 > 3\n      ? _vm.searchResults.tags.length\n      : null\n  var g9 =\n    _vm.searched && !g0 && _vm.hasSearchResults && _vm.searchType === \"all\"\n      ? _vm.searchResults.posts && _vm.searchResults.posts.length > 0\n      : null\n  var l2 =\n    _vm.searched &&\n    !g0 &&\n    _vm.hasSearchResults &&\n    _vm.searchType === \"all\" &&\n    g9\n      ? _vm.convertPostsForDisplay(_vm.searchResults.posts)\n      : null\n  var g10 = !_vm.searched ? _vm.searchHistory.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        l1: l1,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        l2: l2,\n        g10: g10,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"search-container\">\r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-bar\">\r\n      <u-search\r\n        v-model=\"keyword\"\r\n        placeholder=\"搜索帖子、用户或话题\"\r\n        :show-action=\"true\"\r\n        action-text=\"搜索\"\r\n        @search=\"onSearch\"\r\n        @custom=\"onSearch\"\r\n      ></u-search>\r\n    </view>\r\n\r\n    <!-- 搜索结果 -->\r\n    <view v-if=\"searched\" class=\"search-results\">\r\n      <!-- 搜索类型切换 -->\r\n      <view class=\"search-tabs\">\r\n        <u-tag\r\n          text=\"全部\"\r\n          :bg-color=\"searchType === 'all' ? '#ff6b87' : ''\"\r\n          :color=\"searchType === 'all' ? '#ffffff' : '#909399'\"\r\n          :border-color=\"searchType === 'all' ? '#ff6b87' : '#e4e7ed'\"\r\n          :mode=\"searchType === 'all' ? 'dark' : 'plain'\"\r\n          size=\"default\"\r\n          shape=\"circle\"\r\n          @click=\"changeSearchType('all')\"\r\n        ></u-tag>\r\n        <u-tag\r\n          text=\"帖子\"\r\n          :bg-color=\"searchType === 'post' ? '#ff6b87' : ''\"\r\n          :color=\"searchType === 'post' ? '#ffffff' : '#909399'\"\r\n          :border-color=\"searchType === 'post' ? '#ff6b87' : '#e4e7ed'\"\r\n          :mode=\"searchType === 'post' ? 'dark' : 'plain'\"\r\n          size=\"default\"\r\n          shape=\"circle\"\r\n          @click=\"changeSearchType('post')\"\r\n        ></u-tag>\r\n        <u-tag\r\n          text=\"用户\"\r\n          :bg-color=\"searchType === 'user' ? '#ff6b87' : ''\"\r\n          :color=\"searchType === 'user' ? '#ffffff' : '#909399'\"\r\n          :border-color=\"searchType === 'user' ? '#ff6b87' : '#e4e7ed'\"\r\n          :mode=\"searchType === 'user' ? 'dark' : 'plain'\"\r\n          size=\"default\"\r\n          shape=\"circle\"\r\n          @click=\"changeSearchType('user')\"\r\n        ></u-tag>\r\n        <u-tag\r\n          text=\"话题\"\r\n          :bg-color=\"searchType === 'tag' ? '#ff6b87' : ''\"\r\n          :color=\"searchType === 'tag' ? '#ffffff' : '#909399'\"\r\n          :border-color=\"searchType === 'tag' ? '#ff6b87' : '#e4e7ed'\"\r\n          :mode=\"searchType === 'tag' ? 'dark' : 'plain'\"\r\n          size=\"default\"\r\n          shape=\"circle\"\r\n          @click=\"changeSearchType('tag')\"\r\n        ></u-tag>\r\n      </view>\r\n\r\n      <!-- 搜索结果内容 -->\r\n      <scroll-view\r\n        class=\"results-content\"\r\n        scroll-y\r\n        enable-flex\r\n        @scrolltolower=\"loadMoreResults\"\r\n      >\r\n        <!-- 加载状态 -->\r\n        <view v-if=\"loading && results.length === 0\" class=\"loading-container\">\r\n          <u-loading mode=\"circle\" size=\"40\" color=\"#ff6b87\"></u-loading>\r\n          <text class=\"loading-text\">搜索中...</text>\r\n        </view>\r\n\r\n        <!-- 搜索结果列表 -->\r\n        <view v-else-if=\"hasSearchResults\" class=\"results-list\">\r\n          <!-- 调试信息 -->\r\n          <view class=\"debug-info\" v-if=\"false\">\r\n            <text>搜索类型: {{ searchType }}, 结果数量: {{ results.length }}</text>\r\n            <text v-if=\"searchType === 'tag'\">话题数据: {{ JSON.stringify(searchResults.tags) }}</text>\r\n          </view>\r\n\r\n          <!-- 全部搜索结果 - 混合显示 -->\r\n          <template v-if=\"searchType === 'all'\">\r\n            <!-- 用户结果 -->\r\n            <template v-if=\"searchResults.users && searchResults.users.length > 0\">\r\n              <view class=\"result-section\">\r\n                <view class=\"section-title\">用户</view>\r\n                <UserCard\r\n                  v-for=\"(user, index) in convertUsersForDisplay(searchResults.users.slice(0, 3))\"\r\n                  :key=\"user.id || user.userId || index\"\r\n                  :user=\"user\"\r\n                  class=\"user-card-item\"\r\n                />\r\n                <view v-if=\"searchResults.users.length > 3\" class=\"view-more\" @click=\"changeSearchType('user')\">\r\n                  查看更多用户 ({{ searchResults.users.length }})\r\n                </view>\r\n              </view>\r\n            </template>\r\n\r\n            <!-- 话题结果 -->\r\n            <template v-if=\"searchResults.tags && searchResults.tags.length > 0\">\r\n              <view class=\"result-section\">\r\n                <view class=\"section-title\">话题</view>\r\n                <TagCard\r\n                  v-for=\"(tag, index) in convertTagsForDisplay(searchResults.tags.slice(0, 3))\"\r\n                  :key=\"tag.id || tag.tagId || index\"\r\n                  :tag=\"tag\"\r\n                  class=\"tag-card-item\"\r\n                />\r\n                <view v-if=\"searchResults.tags.length > 3\" class=\"view-more\" @click=\"changeSearchType('tag')\">\r\n                  查看更多话题 ({{ searchResults.tags.length }})\r\n                </view>\r\n              </view>\r\n            </template>\r\n\r\n            <!-- 帖子结果 -->\r\n            <template v-if=\"searchResults.posts && searchResults.posts.length > 0\">\r\n              <view class=\"result-section\">\r\n                <view class=\"section-title\">帖子</view>\r\n                <PostCard\r\n                  v-for=\"(post, index) in convertPostsForDisplay(searchResults.posts)\"\r\n                  :key=\"post.id || index\"\r\n                  :post=\"post\"\r\n                  class=\"post-card-item\"\r\n                />\r\n              </view>\r\n            </template>\r\n          </template>\r\n\r\n          <!-- 帖子结果 -->\r\n          <template v-else-if=\"searchType === 'post'\">\r\n            <PostCard\r\n              v-for=\"(post, index) in results\"\r\n              :key=\"post.id || index\"\r\n              :post=\"post\"\r\n              class=\"post-card-item\"\r\n            />\r\n          </template>\r\n\r\n          <!-- 用户结果 -->\r\n          <template v-else-if=\"searchType === 'user'\">\r\n            <UserCard\r\n              v-for=\"(user, index) in results\"\r\n              :key=\"user.id || user.userId || index\"\r\n              :user=\"user\"\r\n              class=\"user-card-item\"\r\n            />\r\n          </template>\r\n\r\n          <!-- 话题结果 -->\r\n          <template v-else-if=\"searchType === 'tag'\">\r\n            <TagCard\r\n              v-for=\"(tag, index) in results\"\r\n              :key=\"tag.id || tag.tagId || index\"\r\n              :tag=\"tag\"\r\n              class=\"tag-card-item\"\r\n            />\r\n          </template>\r\n\r\n          <!-- 加载更多状态 - 仅非全部搜索显示 -->\r\n          <template v-if=\"searchType !== 'all'\">\r\n            <view v-if=\"pagination.hasMore\" class=\"load-more\">\r\n              <view v-if=\"loading\" class=\"loading-more\">\r\n                <u-loading mode=\"circle\" size=\"24\" color=\"#ff6b87\"></u-loading>\r\n                <text class=\"loading-text\">加载更多...</text>\r\n              </view>\r\n              <view v-else class=\"load-more-tip\">上拉加载更多</view>\r\n            </view>\r\n            <view v-else class=\"no-more\">没有更多内容了</view>\r\n          </template>\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view v-else class=\"empty-container\">\r\n          <u-empty mode=\"search\" text=\"没有找到相关内容\">\r\n            <template #icon>\r\n              <u-icon name=\"search\" size=\"80\" color=\"#d0d0d0\"></u-icon>\r\n            </template>\r\n          </u-empty>\r\n          <view class=\"empty-tips\">\r\n            <text>试试其他关键词或</text>\r\n            <text class=\"back-link\" @click=\"backToHome\">返回首页</text>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <!-- 搜索历史和热门搜索 -->\r\n    <view v-else class=\"discovery-section\">\r\n      <!-- 搜索历史 -->\r\n      <view v-if=\"searchHistory.length > 0\" class=\"history-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">搜索历史</text>\r\n          <u-icon name=\"trash\" color=\"#999\" size=\"20\" @click=\"clearHistory\"></u-icon>\r\n        </view>\r\n        <view class=\"tags-container\">\r\n          <u-tag\r\n            v-for=\"(item, index) in searchHistory\"\r\n            :key=\"index\"\r\n            :text=\"item\"\r\n            type=\"info\"\r\n            @click=\"onTagClick(item)\"\r\n          ></u-tag>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 热门搜索 -->\r\n      <view class=\"hot-searches-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">热门搜索</text>\r\n        </view>\r\n        <view class=\"tags-container\">\r\n          <u-tag\r\n            v-for=\"(item, index) in hotSearches\"\r\n            :key=\"index\"\r\n            :text=\"item\"\r\n            type=\"warning\"\r\n            plain\r\n            @click=\"onTagClick(item)\"\r\n          ></u-tag>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport PostCard from \"../components/PostCard.vue\";\r\nimport UserCard from \"../components/UserCard.vue\";\r\nimport TagCard from \"../components/TagCard.vue\";\r\nimport {\r\n  comprehensiveSearch,\r\n  getHotKeywords,\r\n  getSearchHistory,\r\n  getSearchSuggestions\r\n} from \"@/utils/socialApi.js\";\r\n\r\nexport default {\r\n  name: \"SearchPage\",\r\n  components: {\r\n    PostCard,\r\n    UserCard,\r\n    TagCard\r\n  },\r\n  data() {\r\n    return {\r\n      keyword: \"\",\r\n      searched: false,\r\n      searchHistory: [],\r\n      hotSearches: [],\r\n      results: [],\r\n      loading: false,\r\n      searchType: 'all', // all, post, user, tag\r\n      pagination: {\r\n        current: 1,\r\n        size: 10,\r\n        hasMore: true\r\n      },\r\n      searchResults: {\r\n        posts: [],\r\n        users: [],\r\n        tags: [],\r\n        totalCount: 0\r\n      },\r\n      currentUserId: 0\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 判断是否有搜索结果\r\n    hasSearchResults() {\r\n      if (this.searchType === 'all') {\r\n        return (this.searchResults.posts && this.searchResults.posts.length > 0) ||\r\n               (this.searchResults.users && this.searchResults.users.length > 0) ||\r\n               (this.searchResults.tags && this.searchResults.tags.length > 0);\r\n      } else {\r\n        return this.results.length > 0;\r\n      }\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.loadInitialData();\r\n    this.loadCurrentUserId();\r\n  },\r\n\r\n  methods: {\r\n    // 加载初始数据\r\n    async loadInitialData() {\r\n      try {\r\n        // 并行加载热门搜索词和搜索历史\r\n        const [hotKeywordsResult, historyResult] = await Promise.all([\r\n          this.loadHotKeywords(),\r\n          this.loadSearchHistory()\r\n        ]);\r\n\r\n        console.log('初始数据加载完成');\r\n      } catch (error) {\r\n        console.error('初始数据加载失败:', error);\r\n      }\r\n    },\r\n\r\n    // 加载热门搜索词\r\n    async loadHotKeywords() {\r\n      try {\r\n        const result = await getHotKeywords(10);\r\n        if (result && result.code === 0 && result.data) {\r\n          this.hotSearches = result.data;\r\n          console.log('热门搜索词加载成功:', this.hotSearches);\r\n        } else {\r\n          // 使用默认热门搜索词\r\n          this.hotSearches = [\"街舞\", \"现代舞\", \"芭蕾\", \"拉丁舞\", \"爵士舞\", \"民族舞\", \"古典舞\", \"舞蹈教学\"];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载热门搜索词失败:', error);\r\n        // 使用默认热门搜索词\r\n        this.hotSearches = [\"街舞\", \"现代舞\", \"芭蕾\", \"拉丁舞\", \"爵士舞\", \"民族舞\", \"古典舞\", \"舞蹈教学\"];\r\n      }\r\n    },\r\n\r\n    // 加载搜索历史\r\n    async loadSearchHistory() {\r\n      try {\r\n        const result = await getSearchHistory(20);\r\n        if (result && result.code === 0 && result.data) {\r\n          this.searchHistory = result.data;\r\n          console.log('搜索历史加载成功:', this.searchHistory);\r\n        } else {\r\n          // 从本地存储获取搜索历史\r\n          const localHistory = uni.getStorageSync('searchHistory') || [];\r\n          this.searchHistory = localHistory;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载搜索历史失败:', error);\r\n        // 从本地存储获取搜索历史\r\n        const localHistory = uni.getStorageSync('searchHistory') || [];\r\n        this.searchHistory = localHistory;\r\n      }\r\n    },\r\n    // 加载当前用户userId\r\n    async loadCurrentUserId() {\r\n      try {\r\n        this.currentUserId = uni.getStorageSync('userid') || 0;\r\n      } catch (error) {\r\n        console.error('加载当前用户ID失败:', error);\r\n        this.currentUserId = 0;\r\n      }\r\n    },\r\n\r\n    // 执行搜索\r\n    async onSearch(value) {\r\n      if (!value || !value.trim()) {\r\n        uni.showToast({\r\n          title: '请输入搜索关键词',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      const keyword = value.trim();\r\n      this.keyword = keyword;\r\n      this.searched = true;\r\n      this.loading = true;\r\n\r\n      // 重置分页和结果\r\n      this.pagination.current = 1;\r\n      this.pagination.hasMore = true;\r\n      this.searchResults = {\r\n        posts: [],\r\n        users: [],\r\n        tags: [],\r\n        totalCount: 0\r\n      };\r\n\r\n      try {\r\n        // 更新搜索历史\r\n        this.updateHistory(keyword);\r\n\r\n        // 执行搜索\r\n        await this.performSearch(keyword);\r\n\r\n      } catch (error) {\r\n        console.error('搜索失败:', error);\r\n        uni.showToast({\r\n          title: '搜索失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 执行实际搜索\r\n    async performSearch(keyword, loadMore = false) {\r\n      try {\r\n        const params = {\r\n          keyword: keyword,\r\n          current: this.pagination.current,\r\n          size: this.pagination.size,\r\n          type: this.searchType,\r\n          userId: this.currentUserId\r\n        };\r\n\r\n        console.log('搜索参数:', params);\r\n        const result = await comprehensiveSearch(params);\r\n        console.log('搜索结果:', result);\r\n\r\n        if (result && result.code === 0 && result.data) {\r\n          const searchData = result.data;\r\n\r\n          if (loadMore) {\r\n            // 加载更多，追加结果\r\n            if (searchData.posts) {\r\n              this.searchResults.posts = [...this.searchResults.posts, ...searchData.posts];\r\n            }\r\n            if (searchData.users) {\r\n              this.searchResults.users = [...this.searchResults.users, ...searchData.users];\r\n            }\r\n            if (searchData.tags) {\r\n              this.searchResults.tags = [...this.searchResults.tags, ...searchData.tags];\r\n            }\r\n          } else {\r\n            // 新搜索，替换结果\r\n            this.searchResults = {\r\n              posts: searchData.posts || [],\r\n              users: searchData.users || [],\r\n              tags: searchData.tags || [],\r\n              totalCount: searchData.totalCount || 0\r\n            };\r\n          }\r\n\r\n          // 更新分页信息\r\n          this.pagination.hasMore = searchData.hasMore || false;\r\n\r\n          // 根据搜索类型设置结果数据\r\n          this.updateResultsByType();\r\n\r\n          console.log('搜索完成，结果数量:', {\r\n            posts: this.searchResults.posts.length,\r\n            users: this.searchResults.users.length,\r\n            tags: this.searchResults.tags.length,\r\n            total: this.searchResults.totalCount\r\n          });\r\n\r\n        } else {\r\n          console.error('搜索API返回格式不正确:', result);\r\n          this.searchResults = {\r\n            posts: [],\r\n            users: [],\r\n            tags: [],\r\n            totalCount: 0\r\n          };\r\n          this.results = [];\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('执行搜索失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    // 根据搜索类型更新结果数据\r\n    updateResultsByType() {\r\n      switch (this.searchType) {\r\n        case 'all':\r\n          // 全部搜索直接使用searchResults，不需要设置results\r\n          this.results = [];\r\n          break;\r\n        case 'post':\r\n          this.results = this.convertPostsForDisplay(this.searchResults.posts);\r\n          break;\r\n        case 'user':\r\n          this.results = this.convertUsersForDisplay(this.searchResults.users);\r\n          break;\r\n        case 'tag':\r\n          this.results = this.convertTagsForDisplay(this.searchResults.tags);\r\n          break;\r\n        default:\r\n          this.results = [];\r\n      }\r\n    },\r\n\r\n    // 转换帖子数据格式\r\n    convertPostsForDisplay(posts) {\r\n      return posts.map(post => ({\r\n        id: post.id,\r\n        title: post.title || '',\r\n        content: post.content || '',\r\n        username: post.username || '无名氏',\r\n        userAvatar: this.formatAvatarUrl(post.userAvatar),\r\n        coverImage: post.coverImage,\r\n        likeCount: post.likeCount || 0,\r\n        commentCount: post.commentCount || 0,\r\n        createTime: post.createTime,\r\n        isLiked: post.isLiked || false,\r\n        isFavorited: post.isFavorited || false\r\n      }));\r\n    },\r\n\r\n    // 转换用户数据格式\r\n    convertUsersForDisplay(users) {\r\n      return users.map(user => ({\r\n        id: user.id || user.userId,\r\n        userId: user.id || user.userId,\r\n        username: user.username,\r\n        nickname: user.nickname,\r\n        avatar: this.formatAvatarUrl(user.avatar),\r\n        bio: user.bio || user.description,\r\n        postCount: user.postCount || 0,\r\n        followerCount: user.followerCount || 0,\r\n        followingCount: user.followingCount || 0,\r\n        isFollowed: user.isFollowed,\r\n        isVerified: user.isVerified || false\r\n      }));\r\n    },\r\n\r\n    // 转换话题数据格式\r\n    convertTagsForDisplay(tags) {\r\n      return tags.map(tag => ({\r\n        id: tag.id || tag.tagId,\r\n        tagId: tag.id || tag.tagId,\r\n        name: tag.name || tag.tagName,\r\n        tagName: tag.name || tag.tagName,\r\n        description: tag.description,\r\n        coverImage: tag.coverImage,\r\n        postCount: tag.postCount || 0,\r\n        viewCount: tag.viewCount || 0,\r\n        isFollowed: tag.isFollowed || false,\r\n        isHot: tag.isHot || false\r\n      }));\r\n    },\r\n\r\n    // 格式化头像URL\r\n    formatAvatarUrl(avatar) {\r\n      if (!avatar) {\r\n        return '/static/images/toux.png';\r\n      }\r\n      return 'https://file.foxdance.com.cn' + avatar + '?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85';\r\n    },\r\n    // 更新搜索历史\r\n    updateHistory(keyword) {\r\n      try {\r\n        // 更新内存中的搜索历史\r\n        const index = this.searchHistory.indexOf(keyword);\r\n        if (index > -1) {\r\n          this.searchHistory.splice(index, 1);\r\n        }\r\n        this.searchHistory.unshift(keyword);\r\n        if (this.searchHistory.length > 20) {\r\n          this.searchHistory.pop();\r\n        }\r\n\r\n        // 保存到本地存储\r\n        uni.setStorageSync('searchHistory', this.searchHistory);\r\n\r\n        console.log('搜索历史已更新:', this.searchHistory);\r\n      } catch (error) {\r\n        console.error('更新搜索历史失败:', error);\r\n      }\r\n    },\r\n    // 清空搜索历史\r\n    async clearHistory() {\r\n      try {\r\n        uni.showModal({\r\n          title: '确认清空',\r\n          content: '确定要清空所有搜索历史吗？',\r\n          success: async (res) => {\r\n            if (res.confirm) {\r\n              this.searchHistory = [];\r\n              uni.removeStorageSync('searchHistory');\r\n\r\n              uni.showToast({\r\n                title: '已清空搜索历史',\r\n                icon: 'success'\r\n              });\r\n\r\n              console.log('搜索历史已清空');\r\n            }\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error('清空搜索历史失败:', error);\r\n      }\r\n    },\r\n    onTagClick(tag) {\r\n      this.keyword = tag;\r\n      this.onSearch(tag);\r\n    },\r\n    generateMockResults(keyword) {\r\n      const mockPosts = [];\r\n      for (let i = 0; i < 5; i++) {\r\n        mockPosts.push({\r\n          id: `search-${i}`,\r\n          title: `关于“${keyword}”的帖子标题 ${i + 1}`,\r\n          username: `用户${Math.floor(Math.random() * 1000)}`,\r\n          userAvatar: `https://picsum.photos/100/100?random=${i}`,\r\n          coverImage: `https://picsum.photos/300/400?random=${i}`,\r\n          likeCount: Math.floor(Math.random() * 100),\r\n          commentCount: Math.floor(Math.random() * 20)\r\n        });\r\n      }\r\n      return mockPosts;\r\n    },\r\n\r\n    // 加载更多搜索结果\r\n    async loadMoreResults() {\r\n      // 全部搜索不支持加载更多\r\n      if (this.searchType === 'all') {\r\n        return;\r\n      }\r\n\r\n      if (!this.pagination.hasMore || this.loading) {\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      this.pagination.current += 1;\r\n\r\n      try {\r\n        await this.performSearch(this.keyword, true);\r\n      } catch (error) {\r\n        console.error('加载更多失败:', error);\r\n        this.pagination.current -= 1; // 回退页码\r\n        uni.showToast({\r\n          title: '加载失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 切换搜索类型\r\n    changeSearchType(type) {\r\n      if (this.searchType === type) return;\r\n\r\n      this.searchType = type;\r\n      console.log('切换搜索类型:', type);\r\n\r\n      // 如果已经有搜索结果，根据新类型更新显示\r\n      if (this.searched && this.searchResults) {\r\n        this.updateResultsByType();\r\n      }\r\n    },\r\n\r\n    // 返回搜索首页\r\n    backToHome() {\r\n      this.searched = false;\r\n      this.keyword = '';\r\n      this.results = [];\r\n      this.searchResults = {\r\n        posts: [],\r\n        users: [],\r\n        tags: [],\r\n        totalCount: 0\r\n      };\r\n      this.pagination.current = 1;\r\n      this.pagination.hasMore = true;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.search-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #f8f9fa;\r\n}\r\n.search-bar {\r\n  padding: 16rpx;\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #e4e7ed;\r\n}\r\n.discovery-section {\r\n  padding: 32rpx;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.search-results {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 0; /* 配合flex: 1使用，确保高度计算正确 */\r\n  box-sizing: border-box;\r\n}\r\n.history-section,\r\n.hot-searches-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24rpx;\r\n}\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n.tags-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20rpx;\r\n}\r\n.results-list {\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.post-card-item,\r\n.user-card-item,\r\n.tag-card-item {\r\n  width: 100%;\r\n  margin-bottom: 24rpx;\r\n  box-sizing: border-box;\r\n  flex-shrink: 0; /* 防止被压缩 */\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n/* 调试信息样式 */\r\n.debug-info {\r\n  padding: 16rpx;\r\n  background-color: #f0f0f0;\r\n  border-radius: 8rpx;\r\n  margin-bottom: 16rpx;\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 搜索标签页 */\r\n.search-tabs {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #e4e7ed;\r\n  padding: 24rpx 32rpx;\r\n  gap: 24rpx;\r\n}\r\n\r\n/* 自定义标签样式 */\r\n.search-tabs /deep/ .u-tag {\r\n  min-width: 120rpx;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n/* 搜索结果内容 */\r\n.results-content {\r\n  flex: 1;\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 32rpx;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 0;\r\n}\r\n\r\n.loading-text {\r\n  margin-top: 16rpx;\r\n  font-size: 28rpx;\r\n  color: #909399;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n  padding: 40rpx 0;\r\n  text-align: center;\r\n}\r\n\r\n.loading-more {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.load-more-tip {\r\n  font-size: 28rpx;\r\n  color: #909399;\r\n}\r\n\r\n.no-more {\r\n  padding: 40rpx 0;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  color: #c0c4cc;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 0;\r\n}\r\n\r\n.empty-tips {\r\n  margin-top: 32rpx;\r\n  font-size: 28rpx;\r\n  color: #909399;\r\n}\r\n\r\n.back-link {\r\n  color: #ff6b87;\r\n  text-decoration: underline;\r\n  margin-left: 8rpx;\r\n}\r\n\r\n/* 全部搜索结果分组样式 */\r\n.result-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 24rpx;\r\n  padding-left: 8rpx;\r\n  border-left: 6rpx solid #ff6b87;\r\n}\r\n\r\n.view-more {\r\n  margin-top: 24rpx;\r\n  padding: 20rpx;\r\n  text-align: center;\r\n  background-color: #f8f9fa;\r\n  border-radius: 16rpx;\r\n  font-size: 28rpx;\r\n  color: #ff6b87;\r\n  border: 2rpx solid #ff6b87;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.view-more:active {\r\n  background-color: #ff6b87;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 卡片间距调整 */\r\n.user-card-item,\r\n.tag-card-item,\r\n.post-card-item {\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.user-card-item:last-child,\r\n.tag-card-item:last-child,\r\n.post-card-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2813d7e8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2813d7e8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760722138\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}