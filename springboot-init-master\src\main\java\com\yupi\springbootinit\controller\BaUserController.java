package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.mapper.BaUserMapper;
import com.yupi.springbootinit.model.entity.BaUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户接口
 */
@RestController
@RequestMapping("/ba-user")
@Slf4j
@Api(tags = "用户接口")
public class BaUserController {

    @Resource
    private BaUserMapper baUserMapper;

    /**
     * 获取用户投票次数
     */
    @GetMapping("/remaining-votes/{userId}")
    @ApiOperation(value = "获取用户投票次数")
    public BaseResponse<Integer> getRemainingVotes(@PathVariable Integer userId) {
        if (userId == null || userId <= 0) {
            log.warn("获取用户投票次数请求参数错误 - userId无效: {}", userId);
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "无效的用户ID");
        }

        // 查询用户
        BaUser user = baUserMapper.selectById(userId);
        log.info("从数据库查询用户信息 - userId: {}", userId);

        // 用户不存在
        if (user == null) {
            log.warn("用户不存在 - userId: {}", userId);
            return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "用户不存在");
        }

        // 返回投票次数，如果为null则默认为0
        Integer remainingVotes = user.getRemainingVotes() != null ? user.getRemainingVotes() : 0;
        log.info("用户投票次数 - userId: {}, remainingVotes: {}", userId, remainingVotes);

        return ResultUtils.success(remainingVotes);
    }
}