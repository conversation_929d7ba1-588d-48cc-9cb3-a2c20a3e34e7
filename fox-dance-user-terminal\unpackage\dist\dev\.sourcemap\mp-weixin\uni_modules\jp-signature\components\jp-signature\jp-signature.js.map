{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature/jp-signature.vue?ed35", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature/jp-signature.vue?5ffa", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature/jp-signature.vue?165b", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature/jp-signature.vue?d807", "uni-app:///uni_modules/jp-signature/components/jp-signature/jp-signature.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature/jp-signature.vue?60c2", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/uni_modules/jp-signature/components/jp-signature/jp-signature.vue?81eb"], "names": ["props", "data", "canvasWidth", "canvasHeight", "offscreenWidth", "offscreenHeight", "useCanvas2d", "show", "offscreenStyles", "showMask", "isPC", "computed", "canvasId", "offscreenId", "offscreenSize", "canvasStyle", "backgroundColor", "width", "height", "background", "param", "penColor", "penSize", "backgroundImage", "landscape", "boundingBox", "openSmooth", "minLineWidth", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "minSpeed", "maxWidthDiffRate", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableScroll", "disabled", "created", "platform", "mounted", "config", "immediate", "<PERSON><PERSON><PERSON><PERSON>", "methods", "redo", "restore", "undo", "clear", "isEmpty", "cosnole", "canvasToMaskPath", "pixelRatio", "uni", "x", "y", "fail", "success", "context", "tempFile<PERSON>ath", "canvasToTempFilePath", "console", "canvas", "type", "offCanvas", "img", "preferToDataURL", "next", "getContext", "left", "top", "right", "createImage", "toDataURL", "requestAnimationFrame", "getTouch", "e", "item", "touchStart", "touchMove", "touchEnd"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqR;AACrR;AACgE;AACL;AACc;;;AAGzE;AACkM;AAClM,gBAAgB,6LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mPAAM;AACR,EAAE,4PAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uPAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAowB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC0CxxB;AACA;AAEA;AAEA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAuBA;EACA;EACAC;IACAC;MAEA;IAKA;IACAC;MACA;IACA;IACAC;MACA;QAAAT;MACA;IACA;IACAU;MACA;QAAAZ;QAAAa;MACA;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA,IACAC,WAcA,KAdAA;QACAC,UAaA,KAbAA;QACAN,kBAYA,KAZAA;QACAO,kBAWA,KAXAA;QACAC,YAUA,KAVAA;QACAC,cASA,KATAA;QACAC,aAQA,KARAA;QACAC,eAOA,KAPAA;QACAC,eAMA,KANAA;QACAC,WAKA,KALAA;QACAC,mBAIA,KAJAA;QACAC,mBAGA,KAHAA;QACAC,gBAEA,KAFAA;QACAC,WACA,KADAA;MAEA;QACAZ;QACAC;QACAN;QACAO;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EASAC;IACA;MAAAC;IACA;IACA;IAEA;EAGA;EAGAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,KACA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OAEA;YAAA;cAAAC;cACA;cACA;cACA;cACA;cAEA;gBACA;cACA;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAeAC;IACA;IACA;IACA;IACA;EAIA;EAGAC;IAgDAC;MAQA,oBACA;IAEA;IACAC;MACA;IACA;IACAC;MAQA,oBACA;IAEA;IACAC;MAQA,oBACA;IAEA;IACAC;MAQA;MACAC;IAEA;IACAC;MAAA;MAAA;MACA;MAkCA;MACA;MAEA;QAAAC;MACA;QACA;QACA;MACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MAEA;QACAC;UACArC;UACAsC;UACAC;UACAlC;UACAC;UACAjB;UACAmD;YACAA;UACA;UACAC;YACA;cACA;cACAC;cACAA;cACA;cACA;cACAD;gBACAE;gBACAV;cACA;YACA;UACA;QACA;MACA;IAEA;IACAW;MAAA;MAAA;MACAC;MACA;MAkCA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAAC;MACA,IACA1C,kBAGA,KAHAA;QACAQ,YAEA,KAFAA;QACAC,cACA,KADAA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAMA;QACA;UACA;UACA;YACA;cAAAkC;YAAA;YACAC;YACAA;YACA;YACA;UACA;YACA;YACA;UACA;QACA;QAEA;UAAA;UAAAN;UAAAM;QACAN;QACAA;QACA;UACAA;UACAA;QACA;QACA;UACAA;UACAA;QACA;QACA;UACA;UACAO;UACAA;YACAP;YACA;YACAD;cACAE;cACAV;YACA;UACA;QAEA;UACAS;UACAA;YACA;cACA;cACAA;cACAA;cACAD;gBACAE;gBACAV;cACA;YACA;UACA;QACA;MAEA;MACA;QAAA;UAAA;UAAA;YAAA;cAAA;gBAAA;kBAAA,MACA;oBAAA;oBAAA;kBAAA;kBACA;kBACA;kBAAA;kBAAA,OACA;gBAAA;kBAQAzB;oBAAA8B;oBAAAC;oBAAAlC;oBAAAC;oBAAAwC;oBAAAI;kBAAA;kBAEA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CACA;QAAA,gBAfAC;UAAA;QAAA;MAAA,GAeA;MACA;MACA;QACA;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA;oBACA;oBAEAb;oBACAC;oBACAY;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;QAAA;MACA;QACAA;MACA;IAEA;IAEAC;MAAA;MACA;QACAV;QACAK;MACA;QACA;UACA;YAAAzC;YAAAwC;YAAAO;YAAAC;YAAAC;UACA;YAAAnB;UACA;UACA;YACAM;YACAI;YACAA;UACA;YACAV;YACAM;YACAI;cACAM;gBAAA;cAAA;cACAI;cACAC;gBAAA;cAAA;cACAC;YACA;UACA;UACA;UACAhB;UACA;YACAW;YACAC;YACAC;YACAlD;YACAC;YACAoC;YACAI;YACAV;UACA;QACA;MACA;IACA;IACAuB;MAAA;MACA;QACAC;UACA,uCACAC;YACAvB;YACAC;UAAA;QAEA;MACA;MACA;IACA;IACAuB;MAAA;MACA;MACA;MACA;MACA;QACA;UAAApB;QAAA;UACA;UACA;QACA;QACA;MACA;MACA;IACA;IACAqB;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACvmBA;AAAA;AAAA;AAAA;AAA26C,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACA/7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/jp-signature/components/jp-signature/jp-signature.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./jp-signature.vue?vue&type=template&id=241dcc82&filter-modules=eyJzaWduIjp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MTc2ODksImF0dHJzIjp7Im1vZHVsZSI6InNpZ24iLCJsYW5nIjoianMifSwiZW5kIjoxNzc0M319&\"\nvar renderjs\nimport script from \"./jp-signature.vue?vue&type=script&lang=js&\"\nexport * from \"./jp-signature.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jp-signature.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/jp-signature/components/jp-signature/jp-signature.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature.vue?vue&type=template&id=241dcc82&filter-modules=eyJzaWduIjp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MTc2ODksImF0dHJzIjp7Im1vZHVsZSI6InNpZ24iLCJsYW5nIjoianMifSwiZW5kIjoxNzc0M319&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show ? _vm.__get_style([_vm.canvasStyle, _vm.styles]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"lime-signature\" v-if=\"show\" :style=\"[canvasStyle, styles]\" ref=\"limeSignature\">\r\n\t\t<!-- #ifndef APP-VUE || APP-NVUE -->\r\n\t\t<canvas v-if=\"useCanvas2d\" class=\"lime-signature__canvas\" :id=\"canvasId\" type=\"2d\"\r\n\t\t\t:disableScroll=\"disableScroll\" @touchstart=\"touchStart\" @touchmove=\"touchMove\"\r\n\t\t\t@touchend=\"touchEnd\"></canvas>\r\n\t\t<canvas v-else :disableScroll=\"disableScroll\" class=\"lime-signature__canvas\" :canvas-id=\"canvasId\"\r\n\t\t\t:id=\"canvasId\" :width=\"canvasWidth\" :height=\"canvasHeight\" @touchstart=\"touchStart\" @touchmove=\"touchMove\"\r\n\t\t\t@touchend=\"touchEnd\" @mousedown=\"touchStart\" @mousemove=\"touchMove\" @mouseup=\"touchEnd\"></canvas>\r\n\t\t<canvas class=\"offscreen\" canvas-id=\"offscreen\" id=\"offscreen\"\r\n\t\t\t:style=\"'width:' + offscreenSize[0] + 'px;height:' + offscreenSize[1] + 'px'\" :width=\"offscreenSize[0]\"\r\n\t\t\t:height=\"offscreenSize[1]\">\r\n\t\t</canvas>\r\n\t\t<view v-if=\"showMask\" class=\"mask\" @touchstart=\"touchStart\" @touchmove.stop.prevent=\"touchMove\" @touchend=\"touchEnd\"></view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef APP-VUE -->\r\n\t\t<view :id=\"canvasId\" :disableScroll=\"disableScroll\" :rparam=\"param\" :change:rparam=\"sign.update\"\r\n\t\t\t:rclear=\"rclear\" \r\n\t\t\t:change:rclear=\"sign.clear\" \r\n\t\t\t:rundo=\"rundo\" \r\n\t\t\t:rredo=\"rredo\" \r\n\t\t\t:change:rredo=\"sign.redo\" \r\n\t\t\t:change:rundo=\"sign.undo\" \r\n\t\t\t:rsave=\"rsave\"\r\n\t\t\t:rmask=\"rmask\"\r\n\t\t\t:change:rsave=\"sign.save\" \r\n\t\t\t:change:rmask=\"sign.mask\" \r\n\t\t\t:rdestroy=\"rdestroy\"\r\n\t\t\t:change:rdestroy=\"sign.destroy\" \r\n\t\t\t:rempty=\"rempty\" \r\n\t\t\t:change:rempty=\"sign.isEmpty\">\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t<web-view src=\"/uni_modules/lime-signature/hybrid/html/index.html\" class=\"lime-signature__canvas\" ref=\"webview\"\r\n\t\t\t@pagefinish=\"onPageFinish\" @error=\"onError\" @onPostMessage=\"onMessage\"></web-view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifndef APP-NVUE\r\n\timport { canIUseCanvas2d, wrapEvent, requestAnimationFrame, sleep, isTransparent} from './utils'\r\n\timport {Signature} from './signature.js'\r\n\t// import {Signature} from '@signature';\r\n\timport { uniContext, createImage, toDataURL } from './context'\r\n\t// #endif\r\n\timport props from './props';\r\n\timport { base64ToPath, getRect } from './utils'\r\n\r\n\t/**\r\n\t * LimeSignature 手写板签名\r\n\t * @description 手写板签名插件：一款能跑在uniapp各端中的签名插件，支持横屏、背景色、笔画颜色、笔画大小等功能,可生成有内容的区域，减小图片尺寸，节省空间。\r\n\t * @property {Number} penSize 画笔大小\r\n\t * @property {Number} minLineWidth 线条最小宽\r\n\t * @property {Number} maxLineWidth 线条最大宽 \r\n\t * @property {String} penColor 画笔颜色 \r\n\t * @property {String} backgroundColor 背景颜色,不填则为透明\r\n\t * @property {type} 指定 canvas 类型\r\n\t * @value 2d canvas 2d \r\n\t * @value '' 非 canvas 2d 旧接口，微信不再维护\r\n\t * @property {Boolean} openSmooth 模拟笔锋 \r\n\t * @property {Number} beforeDelay 延时初始化，在放在弹窗里可以使用 （毫秒）  \r\n\t * @property {Number} maxHistoryLength 限制历史记录数，即最大可撤销数，传入0则关闭历史记录功能 \r\n\t * @property {Boolean} landscape 横屏，使用后在最后生成图片时会图片旋转90度\r\n\t * @property {Boolean} disableScroll 当在写字时，禁止屏幕滚动以及下拉刷新，nvue无效\r\n\t * @property {Boolean} boundingBox 只生成内容区域，即未画部分不生成，有性能的损耗\r\n\t */\r\n\texport default {\r\n\t\tprops,\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcanvasWidth: null,\r\n\t\t\t\tcanvasHeight: null,\r\n\t\t\t\toffscreenWidth: null,\r\n\t\t\t\toffscreenHeight: null,\r\n\t\t\t\tuseCanvas2d: true,\r\n\t\t\t\tshow: true,\r\n\t\t\t\toffscreenStyles: '',\r\n\t\t\t\tshowMask: false,\r\n\t\t\t\tisPC: false,\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\trclear: 0,\r\n\t\t\t\trdestroy: 0,\r\n\t\t\t\trundo: 0,\r\n\t\t\t\trredo: 0,\r\n\t\t\t\trsave: JSON.stringify({\r\n\t\t\t\t\tn: 0,\r\n\t\t\t\t\tfileType: 'png',\r\n\t\t\t\t\tquality: 1,\r\n\t\t\t\t\tdestWidth: 0,\r\n\t\t\t\t\tdestHeight: 0,\r\n\t\t\t\t}),\r\n\t\t\t\trmask: JSON.stringify({\r\n\t\t\t\t\tn: 0,\r\n\t\t\t\t\tdestWidth: 0,\r\n\t\t\t\t\tdestHeight: 0,\r\n\t\t\t\t}),\r\n\t\t\t\trempty: 0,\r\n\t\t\t\trisEmpty: true,\r\n\t\t\t\ttoDataURL: null,\r\n\t\t\t\ttempFilePath: [],\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcanvasId() {\r\n\t\t\t\t// #ifdef VUE2\r\n\t\t\t\treturn `lime-signature${this._uid}`\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\treturn `lime-signature${this._.uid}`\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\toffscreenId() {\r\n\t\t\t\treturn this.canvasId + 'offscreen'\r\n\t\t\t},\r\n\t\t\toffscreenSize() {\r\n\t\t\t\tconst {offscreenWidth,offscreenHeight} = this\r\n\t\t\t\treturn this.landscape ? [offscreenHeight, offscreenWidth] : [offscreenWidth, offscreenHeight]\r\n\t\t\t},\r\n\t\t\tcanvasStyle() {\r\n\t\t\t\tconst { canvasWidth, canvasHeight, backgroundColor } = this\r\n\t\t\t\treturn {\r\n\t\t\t\t\twidth: canvasWidth && (canvasWidth + 'px'),\r\n\t\t\t\t\theight: canvasHeight && (canvasHeight + 'px'),\r\n\t\t\t\t\tbackground: backgroundColor\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tparam() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tpenColor,\r\n\t\t\t\t\tpenSize,\r\n\t\t\t\t\tbackgroundColor,\r\n\t\t\t\t\tbackgroundImage,\r\n\t\t\t\t\tlandscape,\r\n\t\t\t\t\tboundingBox,\r\n\t\t\t\t\topenSmooth,\r\n\t\t\t\t\tminLineWidth,\r\n\t\t\t\t\tmaxLineWidth,\r\n\t\t\t\t\tminSpeed,\r\n\t\t\t\t\tmaxWidthDiffRate,\r\n\t\t\t\t\tmaxHistoryLength,\r\n\t\t\t\t\tdisableScroll,\r\n\t\t\t\t\tdisabled\r\n\t\t\t\t} = this\r\n\t\t\t\treturn JSON.parse(JSON.stringify({\r\n\t\t\t\t\tpenColor,\r\n\t\t\t\t\tpenSize,\r\n\t\t\t\t\tbackgroundColor,\r\n\t\t\t\t\tbackgroundImage,\r\n\t\t\t\t\tlandscape,\r\n\t\t\t\t\tboundingBox,\r\n\t\t\t\t\topenSmooth,\r\n\t\t\t\t\tminLineWidth,\r\n\t\t\t\t\tmaxLineWidth,\r\n\t\t\t\t\tminSpeed,\r\n\t\t\t\t\tmaxWidthDiffRate,\r\n\t\t\t\t\tmaxHistoryLength,\r\n\t\t\t\t\tdisableScroll,\r\n\t\t\t\t\tdisabled\r\n\t\t\t\t}))\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef APP-NVUE\r\n\t\twatch: {\r\n\t\t\tparam(v) {\r\n\t\t\t\tthis.$refs.webview.evalJS(`update(${JSON.stringify(v)})`)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifndef APP-PLUS\r\n\t\tcreated() {\r\n\t\t\tconst {platform} = uni.getSystemInfoSync() \r\n\t\t\tthis.isPC = /windows|mac/.test(platform)\r\n\t\t\tthis.useCanvas2d = this.type == '2d' && canIUseCanvas2d() && !this.isPC\r\n\t\t\t// #ifndef H5\r\n\t\t\tthis.showMask = this.isPC\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifndef APP-PLUS\r\n\t\tasync mounted() {\r\n\t\t\tif (this.beforeDelay) {\r\n\t\t\t\tawait sleep(this.beforeDelay)\r\n\t\t\t}\r\n\t\t\tconst config = await this.getContext()\r\n\t\t\tthis.signature = new Signature(config)\r\n\t\t\tthis.canvasEl = this.signature.canvas.get('el')\r\n\t\t\tthis.offscreenWidth = this.canvasWidth = this.signature.canvas.get('width')\r\n\t\t\tthis.offscreenHeight = this.canvasHeight = this.signature.canvas.get('height')\r\n\r\n\t\t\tthis.stopWatch = this.$watch('param', (v) => {\r\n\t\t\t\tthis.signature.pen.setOption(v)\r\n\t\t\t}, {\r\n\t\t\t\timmediate: true\r\n\t\t\t})\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifndef APP-PLUS\r\n\t\t// #ifdef VUE3\r\n\t\tbeforeUnmount() {\r\n\t\t\tthis.stopWatch && this.stopWatch()\r\n\t\t\tthis.signature.destroy()\r\n\t\t\tthis.signature = null\r\n\t\t\tthis.show = false;\r\n\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\tthis.rdestroy++\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE2\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.stopWatch && this.stopWatch()\r\n\t\t\tthis.signature.destroy()\r\n\t\t\tthis.show = false;\r\n\t\t\tthis.signature = null\r\n\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\tthis.rdestroy++\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t// #ifdef MP-QQ\r\n\t\t\t// toJSON() { return this },\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tonPageFinish() {\r\n\t\t\t\tthis.$refs.webview.evalJS(`update(${JSON.stringify(this.param)})`)\r\n\t\t\t},\r\n\t\t\tonMessage(e = {}) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdetail: {\r\n\t\t\t\t\t\tdata: [res]\r\n\t\t\t\t\t}\r\n\t\t\t\t} = e\r\n\t\t\t\tif (res.event?.save) {\r\n\t\t\t\t\tthis.toDataURL = res.event.save\r\n\t\t\t\t}\r\n\t\t\t\tif (res.event?.changeSize) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\twidth,\r\n\t\t\t\t\t\theight\r\n\t\t\t\t\t} = res.event.changeSize\r\n\t\t\t\t}\r\n\t\t\t\tif (res.event.hasOwnProperty('isEmpty')) {\r\n\t\t\t\t\tthis.risEmpty = res.event.isEmpty\r\n\t\t\t\t}\r\n\t\t\t\tif (res.event?.file) {\r\n\t\t\t\t\tthis.tempFilePath.push(res.event.file)\r\n\t\t\t\t\tif (this.tempFilePath.length > 7) {\r\n\t\t\t\t\t\tthis.tempFilePath.shift()\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (res.event?.success) {\r\n\t\t\t\t\tif (res.event.success) {\r\n\t\t\t\t\t\tthis.tempFilePath.push(res.event.success)\r\n\t\t\t\t\t\tif (this.tempFilePath.length > 8) {\r\n\t\t\t\t\t\t\tthis.tempFilePath.shift()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.toDataURL = this.tempFilePath.join('')\r\n\t\t\t\t\t\tthis.tempFilePath = []\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$emit('fail', 'canvas no data')\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tredo(){\r\n\t\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\t\tthis.rredo += 1\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$refs.webview.evalJS(`redo()`)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-VUE\r\n\t\t\t\tif (this.signature)\r\n\t\t\t\t\tthis.signature.redo()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\trestore() {\r\n\t\t\t\tthis.redo()\r\n\t\t\t},\r\n\t\t\tundo() {\r\n\t\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\t\tthis.rundo += 1\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$refs.webview.evalJS(`undo()`)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-VUE\r\n\t\t\t\tif (this.signature)\r\n\t\t\t\t\tthis.signature.undo()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tclear() {\r\n\t\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\t\tthis.rclear += 1\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$refs.webview.evalJS(`clear()`)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-VUE\r\n\t\t\t\tif (this.signature)\r\n\t\t\t\t\tthis.signature.clear()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tisEmpty() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$refs.webview.evalJS(`isEmpty()`)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\t\tthis.rempty += 1\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-VUE || APP-NVUE\r\n\t\t\t\treturn this.signature.isEmpty()\r\n\t\t\t\tcosnole.log('订单1')\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tcanvasToMaskPath(param = {}){\r\n\t\t\t\tconst isEmpty = this.isEmpty()\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$refs.webview.evalJS(`mask(${JSON.stringify(param)})`)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\t\tconst stopURLWatch = this.$watch('toDataURL', (v, n) => {\r\n\t\t\t\t\tif (v && v !== n) {\r\n\t\t\t\t\t\t// if(param.pathType == 'url') {\r\n\t\t\t\t\t\tbase64ToPath(v).then(res => {\r\n\t\t\t\t\t\t\tparam.success({\r\n\t\t\t\t\t\t\t\ttempFilePath: res,\r\n\t\t\t\t\t\t\t\tisEmpty: this.risEmpty\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// } else {\r\n\t\t\t\t\t\t// \tparam.success({tempFilePath: v,isEmpty: this.risEmpty })\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\tthis.toDataURL = ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\tstopURLWatch && stopURLWatch()\r\n\t\t\t\t})\r\n\t\t\t\tconst {\r\n\t\t\t\t\tfileType,\r\n\t\t\t\t\tquality\r\n\t\t\t\t} = param\r\n\t\t\t\tconst rmask = JSON.parse(this.rmask)\r\n\t\t\t\trmask.n++\r\n\t\t\t\trmask.destWidth = param.destWidth??0\r\n\t\t\t\trmask.destHeight = param.destHeight??0\r\n\t\t\t\t// rmask.fileType = fileType\r\n\t\t\t\t// rmask.quality = quality\r\n\t\t\t\tthis.rmask = JSON.stringify(rmask)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-VUE || APP-NVUE\r\n\t\t\t\tlet width = this.signature.canvas.get('width')\r\n\t\t\t\tlet height = this.signature.canvas.get('height')\r\n\t\t\t\t\r\n\t\t\t\tlet {pixelRatio} = uni.getSystemInfoSync()\r\n\t\t\t\tif(this.useCanvas2d){\r\n\t\t\t\t\tthis.offscreenWidth = width * pixelRatio\r\n\t\t\t\t\tthis.offscreenHeight = height * pixelRatio\r\n\t\t\t\t}\r\n\t\t\t\tconst context = uni.createCanvasContext('offscreen', this)\r\n\t\t\t\tconst success = (success) => param.success && param.success(success)\r\n\t\t\t\tconst fail = (fail) => param.fail && param.fail(fail)\r\n\t\t\t\t\r\n\t\t\t\tthis.signature.pen.getMaskedImageData((imageData)=>{\r\n\t\t\t\t\tuni.canvasPutImageData({\r\n\t\t\t\t\t\tcanvasId: 'offscreen',\r\n\t\t\t\t\t\tx: 0,\r\n\t\t\t\t\t\ty: 0,\r\n\t\t\t\t\t\twidth:Math.floor(this.offscreenWidth),\r\n\t\t\t\t\t\theight:Math.floor(this.offscreenHeight),\r\n\t\t\t\t\t\tdata: imageData,\r\n\t\t\t\t\t\tfail(err){\r\n\t\t\t\t\t\t\tfail(err)\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\ttoDataURL('offscreen', this, param).then((res) => {\r\n\t\t\t\t\t\t\t\tconst size = Math.max(this.offscreenWidth, this.offscreenHeight)\r\n\t\t\t\t\t\t\t\tcontext.restore()\r\n\t\t\t\t\t\t\t\tcontext.clearRect(0, 0, size, size)\r\n\t\t\t\t\t\t\t\tthis.offscreenWidth = width\r\n\t\t\t\t\t\t\t\tthis.offscreenHeight = height\r\n\t\t\t\t\t\t\t\tsuccess({\r\n\t\t\t\t\t\t\t\t\ttempFilePath: res,\r\n\t\t\t\t\t\t\t\t\tisEmpty\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, this)\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tcanvasToTempFilePath(param = {}) {\r\n\t\t\t\tconsole.log('啊实打实1ssss')\r\n\t\t\t\tconst isEmpty = this.isEmpty()\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$refs.webview.evalJS(`save(${JSON.stringify(param)})`)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-VUE || APP-NVUE\r\n\t\t\t\tconst stopURLWatch = this.$watch('toDataURL', (v, n) => {\r\n\t\t\t\t\tif (v && v !== n) {\r\n\t\t\t\t\t\tif(this.preferToDataURL){\r\n\t\t\t\t\t\t\tparam.success({tempFilePath: v,isEmpty: this.risEmpty })\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tbase64ToPath(v).then(res => {\r\n\t\t\t\t\t\t\t\tparam.success({\r\n\t\t\t\t\t\t\t\t\ttempFilePath: res,\r\n\t\t\t\t\t\t\t\t\tisEmpty: this.risEmpty\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.toDataURL = ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\tstopURLWatch && stopURLWatch()\r\n\t\t\t\t})\r\n\t\t\t\tconst {\r\n\t\t\t\t\tfileType,\r\n\t\t\t\t\tquality\r\n\t\t\t\t} = param\r\n\t\t\t\tconst rsave = JSON.parse(this.rsave)\r\n\t\t\t\trsave.n++\r\n\t\t\t\trsave.fileType = fileType\r\n\t\t\t\trsave.quality = quality\r\n\t\t\t\trsave.destWidth = param.destWidth??0\r\n\t\t\t\trsave.destHeight = param.destHeight??0\r\n\t\t\t\tthis.rsave = JSON.stringify(rsave)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-VUE || APP-NVUE\r\n\t\t\t\tconst useCanvas2d = this.useCanvas2d\r\n\t\t\t\tconst success = (success) => param.success && param.success(success)\r\n\t\t\t\tconst fail = (err) => param.fail && param.fail(err)\r\n\t\t\t\tconst { canvas } = this.signature.canvas.get('el')\r\n\t\t\t\tconst {\r\n\t\t\t\t\tbackgroundColor,\r\n\t\t\t\t\tlandscape,\r\n\t\t\t\t\tboundingBox\r\n\t\t\t\t} = this\r\n\t\t\t\tlet width = this.signature.canvas.get('width')\r\n\t\t\t\tlet height = this.signature.canvas.get('height')\r\n\t\t\t\tlet x = 0\r\n\t\t\t\tlet y = 0\r\n\t\t\t\tconst devtools = uni.getSystemInfoSync().platform == 'devtools'\r\n\t\t\t\tlet preferToDataURL = this.preferToDataURL\r\n\t\t\t\tlet scale = 1\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tscale =  devtools ? uni.getSystemInfoSync().pixelRatio : scale\r\n\t\t\t\t// 由于抖音不支持canvasToTempFilePath故优先使用createOffscreenCanvas\r\n\t\t\t\tpreferToDataURL = true\r\n\t\t\t\t// #endif\r\n\t\t\t\tconst canvasToTempFilePath = (image) => {\r\n\t\t\t\t\tconst createCanvasContext = ()=>{\r\n\t\t\t\t\t\tconst useOffscreen = (useCanvas2d && !!uni.createOffscreenCanvas && preferToDataURL) \r\n\t\t\t\t\t\tif(useOffscreen && !devtools){\r\n\t\t\t\t\t\t\tconst offCanvas = uni.createOffscreenCanvas({type: '2d'});\r\n\t\t\t\t\t\t\toffCanvas.width = this.offscreenSize[0]*scale\r\n\t\t\t\t\t\t\toffCanvas.height = this.offscreenSize[1]*scale\r\n\t\t\t\t\t\t\tconst context = offCanvas.getContext(\"2d\");\r\n\t\t\t\t\t\t\treturn [context, offCanvas]\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconst context = uni.createCanvasContext('offscreen', this)\r\n\t\t\t\t\t\t\treturn [context]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst [context, offCanvas] = createCanvasContext()\r\n\t\t\t\t\tcontext.save()\r\n\t\t\t\t\tcontext.setTransform(1, 0, 0, 1, 0, 0)\r\n\t\t\t\t\tif (landscape) {\r\n\t\t\t\t\t\tcontext.translate(0, width*scale)\r\n\t\t\t\t\t\tcontext.rotate(-Math.PI / 2)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (backgroundColor && !isTransparent(backgroundColor)) {\r\n\t\t\t\t\t\tcontext.fillStyle = backgroundColor\r\n\t\t\t\t\t\tcontext.fillRect(0, 0, width, height)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(offCanvas){\r\n\t\t\t\t\t\tconst img = canvas.createImage();\r\n\t\t\t\t\t\timg.src = image\r\n\t\t\t\t\t\timg.onload = () => {\r\n\t\t\t\t\t\t\tcontext.drawImage(img, 0, 0, width*scale, height*scale);\r\n\t\t\t\t\t\t\tconst tempFilePath = offCanvas.toDataURL()\r\n\t\t\t\t\t\t\tsuccess({\r\n\t\t\t\t\t\t\t\ttempFilePath,\r\n\t\t\t\t\t\t\t\tisEmpty\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tcontext.drawImage(image, 0, 0, width*scale, height*scale);\r\n\t\t\t\t\t\tcontext.draw(false, () => {\r\n\t\t\t\t\t\t\ttoDataURL('offscreen', this, param).then((res) => {\r\n\t\t\t\t\t\t\t\tconst size = Math.max(width, height)\r\n\t\t\t\t\t\t\t\tcontext.restore()\r\n\t\t\t\t\t\t\t\tcontext.clearRect(0, 0, size, size)\r\n\t\t\t\t\t\t\t\tsuccess({\r\n\t\t\t\t\t\t\t\t\ttempFilePath: res,\r\n\t\t\t\t\t\t\t\t\tisEmpty\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\tconst next = async () => {\r\n\t\t\t\t\tif(this.offscreenWidth != width || this.offscreenHeight != height) {\r\n\t\t\t\t\t\tthis.offscreenWidth = width\r\n\t\t\t\t\t\tthis.offscreenHeight = height\r\n\t\t\t\t\t\tawait sleep(100)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tconst param = { x, y, width, height, canvas, preferToDataURL }\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tconst param = { x, y, width, height, canvas: useCanvas2d ? canvas : null, preferToDataURL }\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\ttoDataURL(this.canvasId, this, param).then(canvasToTempFilePath).catch(fail)\r\n\t\t\t\t}\r\n\t\t\t\t// PC端小程序获取不到 ImageData 数据，长度为0\r\n\t\t\t\tif (boundingBox && !this.isPC) {\r\n\t\t\t\t\tthis.signature.getContentBoundingBox(async res => {\r\n\t\t\t\t\t\tthis.offscreenWidth = width = res.width\r\n\t\t\t\t\t\tthis.offscreenHeight = height = res.height\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tx = res.startX\r\n\t\t\t\t\t\ty = res.startY\r\n\t\t\t\t\t\tnext()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnext()\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tgetContext() {\r\n\t\t\t\treturn getRect(`#${this.canvasId}`, {\r\n\t\t\t\t\tcontext: this,\r\n\t\t\t\t\ttype: this.useCanvas2d ? 'fields' : 'boundingClientRect'\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\tlet { width, height, node: canvas, left, top, right} = res\r\n\t\t\t\t\t\tlet {pixelRatio} = uni.getSystemInfoSync()\r\n\t\t\t\t\t\tlet context;\r\n\t\t\t\t\t\tif (canvas) {\r\n\t\t\t\t\t\t\tcontext = canvas.getContext('2d')\r\n\t\t\t\t\t\t\tcanvas.width = width * pixelRatio;\r\n\t\t\t\t\t\t\tcanvas.height = height * pixelRatio;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tpixelRatio = 1\r\n\t\t\t\t\t\t\tcontext = uniContext(this.canvasId, this)\r\n\t\t\t\t\t\t\tcanvas = {\r\n\t\t\t\t\t\t\t\tgetContext: (type)=> type=='2d' ? context : null,\r\n\t\t\t\t\t\t\t\tcreateImage,\r\n\t\t\t\t\t\t\t\ttoDataURL: () => toDataURL(this.canvasId, this),\r\n\t\t\t\t\t\t\t\trequestAnimationFrame\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 支付宝小程序 使用stroke有个默认背景色\r\n\t\t\t\t\t\tcontext.clearRect(0, 0, width, height)\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tleft,\r\n\t\t\t\t\t\t\ttop,\r\n\t\t\t\t\t\t\tright,\r\n\t\t\t\t\t\t\twidth,\r\n\t\t\t\t\t\t\theight,\r\n\t\t\t\t\t\t\tcontext,\r\n\t\t\t\t\t\t\tcanvas,\r\n\t\t\t\t\t\t\tpixelRatio\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetTouch(e) {\r\n\t\t\t\tif(this.isPC && this.canvasRect) {\r\n\t\t\t\t\te.touches = e.touches.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tx: item.clientX - this.canvasRect.left,\r\n\t\t\t\t\t\t\ty: item.clientY - this.canvasRect.top,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\treturn e\r\n\t\t\t},\r\n\t\t\ttouchStart(e) {\r\n\t\t\t\tif (!this.canvasEl ) return\r\n\t\t\t\tthis.isStart = true\r\n\t\t\t\t// 微信小程序PC端不支持事件，使用这方法模拟一下\r\n\t\t\t\tif(this.isPC) {\r\n\t\t\t\t\tgetRect(`#${this.canvasId}`, {context: this}).then(res => {\r\n\t\t\t\t\t\tthis.canvasRect = res\r\n\t\t\t\t\t\tthis.canvasEl.dispatchEvent('touchstart', wrapEvent(this.getTouch(e)))\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.canvasEl.dispatchEvent('touchstart', wrapEvent(e))\r\n\t\t\t},\r\n\t\t\ttouchMove(e) {\r\n\t\t\t\tif (!this.canvasEl || !this.isStart && this.canvasEl ) return\r\n\t\t\t\tthis.canvasEl.dispatchEvent('touchmove', wrapEvent(this.getTouch(e)))\r\n\t\t\t},\r\n\t\t\ttouchEnd(e) {\r\n\t\t\t\tif (!this.canvasEl ) return\r\n\t\t\t\tthis.isStart = false\r\n\t\t\t\tthis.canvasEl.dispatchEvent('touchend', wrapEvent(e))\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n<!-- #ifdef APP-VUE -->\r\n<script module=\"sign\" lang=\"renderjs\">\r\n\timport sign from './render'\r\n\texport default sign\r\n</script>\r\n<!-- #endif -->\r\n<style lang=\"scss\">\r\n\t.lime-signature,\r\n\t.lime-signature__canvas {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tflex: 1;\r\n\t\t/* #endif */\r\n\t}\r\n\t.mask {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\ttop: 0;\r\n\t}\r\n\t.offscreen {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 9999px;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jp-signature.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760725544\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}