package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yupi.springbootinit.model.entity.BaCardRecord;
import com.yupi.springbootinit.model.entity.BaUser;
import com.yupi.springbootinit.mapper.BaCardRecordMapper;
import com.yupi.springbootinit.mapper.BaUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * VIP会员定时任务服务实现
 */
@Service
@Slf4j
public class VipMemberScheduleServiceImpl {

    @Resource
    private BaCardRecordMapper baCardRecordMapper;

    @Resource
    private BaUserMapper baUserMapper;

    /**
     * 每天0点执行VIP会员状态更新
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void updateVipMemberStatus() {
        log.info("开始执行VIP会员状态更新定时任务");

        try {
            // 1. 查找所有剩余次数大于0的用户ID（这些是VIP用户）
            QueryWrapper<BaCardRecord> vipQueryWrapper = new QueryWrapper<>();
            vipQueryWrapper.gt("surplus_frequency", 0);
            List<BaCardRecord> vipCardRecords = baCardRecordMapper.selectList(vipQueryWrapper);

            // 获取应该是VIP的用户ID集合
            Set<Integer> shouldBeVipUserIds = new HashSet<>();
            for (BaCardRecord record : vipCardRecords) {
                shouldBeVipUserIds.add(record.getUid());
            }

            log.info("找到{}个应该是VIP的用户", shouldBeVipUserIds.size());

            // 2. 查询所有当前是VIP的用户
            QueryWrapper<BaUser> currentVipQuery = new QueryWrapper<>();
            currentVipQuery.eq("is_member", 1);
            List<BaUser> currentVipUsers = baUserMapper.selectList(currentVipQuery);

            // 获取当前VIP用户ID集合
            Set<Integer> currentVipUserIds = new HashSet<>();
            for (BaUser user : currentVipUsers) {
                currentVipUserIds.add(user.getId());
            }

            log.info("找到{}个当前是VIP的用户", currentVipUserIds.size());

            // 3. 找出需要从非VIP变为VIP的用户（新VIP）
            List<Integer> newVipUserIds = new ArrayList<>();
            for (Integer userId : shouldBeVipUserIds) {
                if (!currentVipUserIds.contains(userId)) {
                    newVipUserIds.add(userId);
                }
            }

            // 4. 更新新VIP用户状态并增加投票权
            for (Integer userId : newVipUserIds) {
                // 获取用户当前状态
                BaUser user = baUserMapper.selectById(userId);
                if (user != null) {
                    // 使用LambdaUpdateWrapper只更新特定字段
                    LambdaUpdateWrapper<BaUser> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(BaUser::getId, userId)
                            .set(BaUser::getIsMember, 1);

                    // 如果剩余投票次数小于等于1，增加一次投票权
                    if (user.getRemainingVotes() != null && user.getRemainingVotes() <= 1) {
                        updateWrapper.set(BaUser::getRemainingVotes, user.getRemainingVotes() + 1);
                        log.info("用户ID: {} 成为VIP，投票次数从 {} 增加到 {}",
                                userId, user.getRemainingVotes(), user.getRemainingVotes() + 1);
                    } else {
                        log.info("用户ID: {} 成为VIP，当前投票次数: {} (无需增加)",
                                userId, user.getRemainingVotes());
                    }

                    int updateCount = baUserMapper.update(null, updateWrapper);
                    if (updateCount > 0) {
                        log.info("成功更新用户ID: {} 为VIP状态", userId);
                    } else {
                        log.warn("更新用户ID: {} VIP状态失败", userId);
                    }
                }
            }

            log.info("更新了{}个新VIP用户", newVipUserIds.size());

            // 5. 找出需要从VIP变为非VIP的用户（失去VIP资格）
            List<Integer> lostVipUserIds = new ArrayList<>();
            for (Integer userId : currentVipUserIds) {
                if (!shouldBeVipUserIds.contains(userId)) {
                    lostVipUserIds.add(userId);
                }
            }

            // 6. 更新失去VIP资格的用户状态并减少投票权
            for (Integer userId : lostVipUserIds) {
                // 获取用户当前状态
                BaUser user = baUserMapper.selectById(userId);
                if (user != null) {
                    // 使用LambdaUpdateWrapper只更新特定字段
                    LambdaUpdateWrapper<BaUser> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(BaUser::getId, userId)
                            .set(BaUser::getIsMember, 0);

                    // 减少投票权
                    if (user.getRemainingVotes() != null && user.getRemainingVotes() > 0) {
                        updateWrapper.set(BaUser::getRemainingVotes, user.getRemainingVotes() - 1);
                        log.info("用户ID: {} 失去VIP资格，投票次数从 {} 减少到 {}",
                                userId, user.getRemainingVotes(), user.getRemainingVotes() - 1);
                    } else {
                        log.info("用户ID: {} 失去VIP资格，当前投票次数: {} (无需减少)",
                                userId, user.getRemainingVotes());
                    }

                    int updateCount = baUserMapper.update(null, updateWrapper);
                    if (updateCount > 0) {
                        log.info("成功更新用户ID: {} 为非VIP状态", userId);
                    } else {
                        log.warn("更新用户ID: {} 非VIP状态失败", userId);
                    }
                }
            }

            log.info("更新了{}个失去VIP资格的用户", lostVipUserIds.size());
            log.info("VIP会员状态更新定时任务执行完成");

        } catch (Exception e) {
            log.error("VIP会员状态更新定时任务执行失败", e);
            throw e;
        }
    }
}