{"version": 3, "sources": ["webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/FollowButton.vue?058d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/FollowButton.vue?a16d", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/FollowButton.vue?862e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/FollowButton.vue?4001", "uni-app:///pagesSub/social/components/FollowButton.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/FollowButton.vue?3a7f", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/components/FollowButton.vue?59dc"], "names": ["name", "props", "user", "type", "required", "default", "followed", "size", "validator", "disabled", "customStyle", "showLoading", "data", "isFollowed", "loading", "computed", "buttonStyle", "fontSize", "height", "min<PERSON><PERSON><PERSON>", "borderRadius", "watch", "immediate", "handler", "methods", "handleFollow", "currentUserId", "uni", "title", "icon", "targetUserId", "result", "console", "error", "setTimeout", "getFontSize", "large", "normal", "mini", "getHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getBorderRadius", "objectToStyle", "setFollowStatus", "getFollowStatus"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACczwB;AAAA;AAAA;AAAA,gBAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACAH;MACAE;IACA;IACA;IACAE;MACAJ;MACAE;MACAG;QAAA;MAAA;IACA;IACA;IACAC;MACAN;MACAE;IACA;IACA;IACAK;MACAP;MACAE;IACA;IACA;IACAM;MACAR;MACAE;IACA;EACA;EACAO;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;QACA;MACA;QACA;MACA;MAEA;IACA;EACA;EACAC;IACAf;MACAgB;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;kBACA;gBACA;gBAAA;gBAGAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIAH;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAE;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAJ;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACA3B;kBACAW;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAkB;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAJ;kBACAC;kBACAC;gBACA;gBACA;gBACA;kBACA3B;kBACAW;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAIA;gBACA;kBACAX;kBACAW;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAmB;gBAEAL;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACA3B;kBACA+B;gBACA;cAAA;gBAAA;gBAEA;kBACAC;oBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEAC;MACA;QACAH;QACAC;QACAC;MACA;MACA;IACA;IAEAE;MACA;QACAJ;QACAC;QACAC;MACA;MACA;IACA;IAEAG;MACA;QACAL;QACAC;QACAC;MACA;MACA;IACA;IAEAI;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7OA;AAAA;AAAA;AAAA;AAAw6C,CAAgB,6wCAAG,EAAC,C;;;;;;;;;;;ACA57C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/components/FollowButton.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./FollowButton.vue?vue&type=template&id=80f509e2&scoped=true&\"\nvar renderjs\nimport script from \"./FollowButton.vue?vue&type=script&lang=js&\"\nexport * from \"./FollowButton.vue?vue&type=script&lang=js&\"\nimport style0 from \"./FollowButton.vue?vue&type=style&index=0&id=80f509e2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80f509e2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/components/FollowButton.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./FollowButton.vue?vue&type=template&id=80f509e2&scoped=true&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./FollowButton.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./FollowButton.vue?vue&type=script&lang=js&\"", "<template>\n  <u-button\n    :type=\"isFollowed ? 'default' : 'primary'\"\n    :size=\"size\"\n    :loading=\"loading\"\n    :disabled=\"disabled\"\n    :customStyle=\"buttonStyle\"\n    @click=\"handleFollow\"\n  >\n    {{ isFollowed ? '已关注' : '关注' }}\n  </u-button>\n</template>\n\n<script>\nimport { followUser, unfollowUser, checkFollowStatus } from '@/utils/socialApi.js'\n\nexport default {\n  name: 'FollowButton',\n  props: {\n    // 用户信息\n    user: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 关注状态\n    followed: {\n      type: Boolean,\n      default: false\n    },\n    // 按钮尺寸\n    size: {\n      type: String,\n      default: 'mini',\n      validator: (value) => ['large', 'normal', 'mini'].includes(value)\n    },\n    // 是否禁用\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    // 自定义样式\n    customStyle: {\n      type: [String, Object],\n      default: ''\n    },\n    // 是否显示加载状态\n    showLoading: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      isFollowed: false,\n      loading: false\n    }\n  },\n  computed: {\n    buttonStyle() {\n      const baseStyle = {\n        fontSize: this.getFontSize(),\n        height: this.getHeight(),\n        minWidth: this.getMinWidth(),\n        borderRadius: this.getBorderRadius()\n      }\n      \n      if (typeof this.customStyle === 'string') {\n        return `${this.customStyle}; ${this.objectToStyle(baseStyle)}`\n      } else if (typeof this.customStyle === 'object') {\n        return { ...baseStyle, ...this.customStyle }\n      }\n      \n      return baseStyle\n    }\n  },\n  watch: {\n    followed: {\n      immediate: true,\n      handler(newVal) {\n        this.isFollowed = newVal\n      }\n    }\n  },\n  methods: {\n    async handleFollow() {\n      if (this.disabled || this.loading) return\n      \n      if (this.showLoading) {\n        this.loading = true\n      }\n      \n      try {\n        const currentUserId = uni.getStorageSync('userid')\n        if (!currentUserId) {\n          uni.showToast({\n            title: '请先登录',\n            icon: 'none'\n          })\n          return\n        }\n\n        const targetUserId = this.user.id || this.user.userId\n        if (!targetUserId) {\n          uni.showToast({\n            title: '用户信息错误',\n            icon: 'none'\n          })\n          return\n        }\n\n        if (currentUserId == targetUserId) {\n          uni.showToast({\n            title: '不能关注自己',\n            icon: 'none'\n          })\n          return\n        }\n\n        let result\n        if (this.isFollowed) {\n          // 取消关注\n          result = await unfollowUser(targetUserId)\n          if (result && result.code === 0) {\n            this.isFollowed = false\n            uni.showToast({\n              title: '已取消关注',\n              icon: 'success'\n            })\n            // 触发事件通知父组件\n            this.$emit('unfollow', {\n              user: this.user,\n              isFollowed: false\n            })\n          } else {\n            throw new Error(result?.message || '取消关注失败')\n          }\n        } else {\n          // 关注\n          result = await followUser(targetUserId)\n          if (result && result.code === 0) {\n            this.isFollowed = true\n            uni.showToast({\n              title: '关注成功',\n              icon: 'success'\n            })\n            // 触发事件通知父组件\n            this.$emit('follow', {\n              user: this.user,\n              isFollowed: true\n            })\n          } else {\n            throw new Error(result?.message || '关注失败')\n          }\n        }\n\n        // 触发状态变化事件\n        this.$emit('change', {\n          user: this.user,\n          isFollowed: this.isFollowed\n        })\n\n      } catch (error) {\n        console.error('关注操作失败:', error)\n\n        uni.showToast({\n          title: error.message || '操作失败，请重试',\n          icon: 'none'\n        })\n\n        // 触发错误事件\n        this.$emit('error', {\n          user: this.user,\n          error: error\n        })\n      } finally {\n        if (this.showLoading) {\n          setTimeout(() => {\n            this.loading = false\n          }, 500) // 延迟500ms隐藏加载状态，提供更好的用户体验\n        }\n      }\n    },\n    \n    getFontSize() {\n      const sizeMap = {\n        large: '32rpx',\n        normal: '28rpx',\n        mini: '24rpx'\n      }\n      return sizeMap[this.size] || sizeMap.mini\n    },\n    \n    getHeight() {\n      const heightMap = {\n        large: '80rpx',\n        normal: '64rpx',\n        mini: '56rpx'\n      }\n      return heightMap[this.size] || heightMap.mini\n    },\n    \n    getMinWidth() {\n      const widthMap = {\n        large: '120rpx',\n        normal: '100rpx',\n        mini: '80rpx'\n      }\n      return widthMap[this.size] || widthMap.mini\n    },\n    \n    getBorderRadius() {\n      const radiusMap = {\n        large: '40rpx',\n        normal: '32rpx',\n        mini: '28rpx'\n      }\n      return radiusMap[this.size] || radiusMap.mini\n    },\n    \n    objectToStyle(obj) {\n      return Object.keys(obj).map(key => {\n        const kebabKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()\n        return `${kebabKey}: ${obj[key]}`\n      }).join('; ')\n    },\n    \n    // 外部调用方法：手动设置关注状态\n    setFollowStatus(status) {\n      this.isFollowed = status\n    },\n    \n    // 外部调用方法：获取当前关注状态\n    getFollowStatus() {\n      return this.isFollowed\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 组件内部不需要额外样式，所有样式通过uview组件和customStyle处理\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./FollowButton.vue?vue&type=style&index=0&id=80f509e2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./FollowButton.vue?vue&type=style&index=0&id=80f509e2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753751621068\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}