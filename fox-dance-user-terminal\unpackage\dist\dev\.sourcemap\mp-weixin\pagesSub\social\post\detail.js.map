{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?6591", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?d87c", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?988b", "uni-app:///pagesSub/social/post/detail.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?4486", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/post/detail.vue?d523"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "FollowButton", "data", "postId", "postData", "commentList", "commentText", "sortType", "currentTab", "tabList", "currentUser", "id", "avatar", "nickname", "loading", "page", "post", "comments", "user", "replyingTo", "isReplyMode", "currentReply", "inputPlaceholder", "showMorePopup", "currentMoreComment", "showPostActionsPopup", "onLoad", "console", "computed", "isOwnPost", "String", "methods", "initPageData", "Promise", "uni", "title", "icon", "loadCurrentUser", "userId", "result", "loadPostDetail", "username", "userAvatar", "content", "images", "topics", "location", "locationAddress", "locationLatitude", "locationLongitude", "likeCount", "commentCount", "shareCount", "isLiked", "isFollowed", "createTime", "setTimeout", "loadComments", "filter", "current", "pageSize", "comment", "level", "replies", "replyTo", "replyCount", "formatTime", "goBack", "goUserProfile", "url", "goCommentUserProfile", "goReplyUserProfile", "reply", "goReplyToUserProfile", "replyToUser", "onUserFollow", "onFollowChange", "imgUrl", "img", "toggleLike", "duration", "originalLiked", "originalCount", "Math", "toggleCommentLike", "previewImage", "urls", "sharePost", "itemList", "success", "goTopic", "openLocation", "latitude", "longitude", "address", "fail", "showCancel", "changeSortType", "args", "focusComment", "onInputFocus", "onInputBlur", "replyComment", "sendComment", "sendNormalComment", "commentData", "newComment", "sendReplyComment", "replyData", "replyToUserId", "replyToUsername", "handleCommentSuccess", "action", "method", "sort", "response", "targetComment", "permission", "permissionNames", "public", "followers", "private", "confirmColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAA+uB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgWnwB;AAUA;AAIA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QAAAV;MAAA;QAAAA;MAAA;MACAW;QACAC;QACAC;QAAA;QACAC;MACA;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACA;;IAEAA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA,OACA,uBACA,wBACAC;IAEA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;kBACAjB;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACAe,aACA,yBACA,wBACA,qBACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACA;gBAEAO;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAEAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAX;gBACA;gBAAA;cAAA;gBAIAA;gBAAA;gBAAA,OACA;cAAA;gBAAAY;gBACAZ;gBAEA;kBACAT;kBACA;oBACAP;oBACAC;oBACAC;kBACA;kBACAc;gBACA;kBACAA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAEAb;gBACAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAY;gBACAZ;gBAAA,MAEAY;kBAAA;kBAAA;gBAAA;gBACAvB;gBACAW;gBACAA,YACA,sBACAX,cACA,6BACAA,cACA;gBAEA;kBACAL;kBACA2B;kBACAG;kBACAC,YACA,iCACA1B,cACA,6FACA;kBACAmB;kBACAQ;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACA5B;gBAAA;gBAAA;cAAA;gBAEAA;gBAAA,MACA;cAAA;gBAGA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBAEAO;kBACAC;kBACAC;gBACA;gBACA;gBACAoB;kBACAtB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAEA9B;gBAAA;gBAAA,OACA;kBACAW;kBAAA;kBACAoB;kBACAC;kBACAC;gBACA;cAAA;gBALArB;gBAMAZ;gBAEA;kBACA;kBACAV;kBACA;oBACAA;kBACA;oBACAA;kBACA;kBAEA;oBAAA;sBACAN;sBACA2B;sBACAG;sBACAC,YACA,iCACAmB,iBACA,6FACA;sBACAlB;sBACAO;sBACAG;sBACAS;sBACAP;sBACA;sBACAQ;wBAAA;0BACApD;0BACA2B;0BACAG;0BACAC,YACA,iDACA;0BACAC;0BACAO;0BACAG;0BACAW,yBACA;4BACA1B;4BACAG;0BACA,IACA;0BACAc;wBACA;sBAAA;sBACAU;oBACA;kBAAA;kBAEAtC;gBACA;kBACAA;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBAEAO;kBACAC;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8B;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;IACA;IAEAC;MACAjC;IACA;IAEAkC;MACA;QACAlC;UACAC;UACAC;QACA;QACA;MACA;MAEAT;MACAO;QACAmC;MACA;IACA;IAEA;IACAC;MACA;QACApC;UACAC;UACAC;QACA;QACA;MACA;MAEAT,YACA,mBACAkC,gBACA,QACAA,iBACA;MACA3B;QACAmC;MACA;IACA;IAEA;IACAE;MACA;QACArC;UACAC;UACAC;QACA;QACA;MACA;MAEAT,YACA,mBACA6C,cACA,QACAA,eACA;MACAtC;QACAmC;MACA;IACA;IAEA;IACAI;MACA;QACAvC;UACAC;UACAC;QACA;QACA;MACA;MAEAT,YACA,oBACA+C,oBACA,QACAA,qBACA;MACAxC;QACAmC;MACA;IACA;IAEAM;MACAhD;MACA;IACA;IAEAiD;MACA;MACA;MACAjD;IACA;IACAkD;MACA,OACAC,MACA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACApD;gBACAA;gBACAA;;gBAEA;gBACAO;kBACAC;kBACAC;kBACA4C;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACArD;gBACAO;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA6C;gBACAC;gBAAA;gBAGA;gBACA;gBACA,sDACAA,oBACAC;gBAEAxD,YACA,gBACA,oBACA,6BACA,qBACA,OACA,wCACA;;gBAEA;gBACAxB;gBACAmC;gBAEAX;gBACAA;gBACAA,YACA,qBACAsD,eACA,cACAC,cACA;gBACAvD,YACA,oBACA,yBACA,cACA,0BACA;gBAAA,IAEAW;kBAAA;kBAAA;gBAAA;gBACAX;gBACAO;kBACAC;kBACAC;gBACA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA,KAMA;kBAAA;kBAAA;gBAAA;gBACAT;gBAAA;gBAAA,OACA;cAAA;gBAAAY;gBAAA;gBAAA;cAAA;gBAEAZ;gBAAA;gBAAA,OACA;cAAA;gBAAAY;cAAA;gBAGAZ;gBAEA;kBACA;kBACAA;kBACAO;oBACAC;oBACAC;oBACA4C;kBACA;gBACA;kBACA;kBACArD;kBACA;kBACA;kBAEAO;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;;gBAEA;gBACA;gBACA;gBAEAO;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgD;MACAvB;MACAA;IACA;IAEAwB;MACAnD;QACAoD;QACA3B;MACA;IACA;IAEA4B;MAAA;MACArD;QACAsD;QACAC;UACA;UACA;QACA;MACA;IACA;IAEAC;MACAxD;QACAmC;MACA;IACA;IAEAsB;MAAA;MACA;MAEAhE;;MAEA;MACA;QACAO;UACA0D;UACAC;UACA9F;UACA+F;UACAL;YACA9D;UACA;UACAoE;YACApE;YACA;YACAO;cACAC;cACAQ;cACAqD;YACA;UACA;QACA;MACA;QACA;QACA9D;UACAC;UACAQ;UACAqD;QACA;MACA;IACA;IAEAC;MAAA;QAAAC;MAAA;MACAvE;MACA,YACA,8BACAuE,UACAA,yCACAA,gBACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;IAAA,CACA;IAEAC;MAAA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACArE;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKAT,YACA,cACA,eACA,OACA,oBACA,SACA,mBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAO;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAtG;kBACAwC;kBACAL;gBACA;;gBAEAX;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAY;gBACAZ;gBAAA,MAEAY;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAZ;cAAA;gBAGA;gBACAA;gBACA+E;kBACA/F;kBACA2B;kBACAG;kBACAC;kBACAC;kBACAO;kBACAG;kBACAS;kBACAP;kBACAU;kBACAF;gBACA,GAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA4C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAjE;kBACAL;kBAAA;kBACAuE;kBACAC;gBACA;gBAEAnF,YACA,aACAiF,WACA,WACA,wBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAArE;gBACAZ;gBAAA,MAEAY;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAZ;cAAA;gBAGA;gBACAA;gBACA;;gBAEA;gBACA6B;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAuD;MAAA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA7E;QACAC;QACAC;MACA;;MAEA;MACAoB;QACA;MACA;IACA;EAAA,2FAGAK;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,MACA;gBAAA;gBAAA;cAAA;cACA3B;gBACAC;gBACAC;cACA;cAAA;YAAA;cAIA6C;cACAC;cAAA;cAGA;cACArB;cACAA,sCACAqB,oBACAC;cAEAxD,YACA,gBACAkC,YACA,OACAA,gCACA;;cAEA;cACAvB;cAAA,IACAA;gBAAA;gBAAA;cAAA;cACAX;cACAO;gBACAC;gBACAC;cACA;cACA;cACAyB;cACAA;cAAA;YAAA;cAAA;cAAA,OAKA;gBACAvB;gBACA0E;cACA;YAAA;cAHAzE;cAKAZ;cAEA;gBACA;gBACAO;kBACAC;kBACAC;kBACA4C;gBACA;cACA;gBACA;gBACArD;gBACAkC;gBACAA;gBAEA3B;kBACAC;kBACAC;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAT;;cAEA;cACAkC;cACAA;cAEA3B;gBACAC;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,oFAGAyB;IACA;EACA,oFAGAC;IACA,cACA,QACA,WACA,WACA,WACA,WACA,UACA;IACA;EACA,wFAGAD;IACA;IACA;EACA,sFAGA;IAAA;IACA;MACA;MACAL;QACA;MACA;IACA;EACA,kFAGA;IACA;IAEAtB;MACAhC;MACAuF;QACAvD;UACAC;UACAC;QACA;MACA;IACA;IACA;EACA,sFAGA;IAAA;IACA;IAEAF;MACAC;MACAQ;MACA8C;QACA;UACA;UACA9D;;UAEA;UACA,0CACA;YAAA;UAAA,EACA;UACA;YACA;YACA;UACA;UAEAO;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;EACA,sFAGA;IACAF;MACAC;MACAC;IACA;IACA;EACA,sFAGAyB;IACA;EACA,0FAGA;IACA;IACA;IACA;IAEA3B;MACAC;MACAC;MACA4C;IACA;EACA,sFAGAnB;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAlC;cAAA;cAAA;cAAA,OAIAO;gBACAmC;gBACA4C;gBACA/G;kBACAoC;kBAAA;kBACA4E;gBACA;cACA;YAAA;cAPAC;cASA;gBACApD,oCAEA;gBACAqD;kBAAA;gBAAA;gBACA;kBACAA;oBAAA;sBACAzG;sBACA2B;sBACAG;sBACAC,2BACA8B,kCACAA,eACA,gDACA;sBACA7B;sBACAO;sBACAG;sBACAW,yBACA;wBACA1B;wBACAG;sBACA,IACA;sBACAc;oBACA;kBAAA;gBACA;gBAEArB;kBACAC;kBACAC;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAT;cACAO;gBACAC;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,0FAEA;IACAF;MACAsD;MACAC;QACA9D;MACA;IACA;EACA,0FAGA;IACAA;IACA;EACA,4EAGA;IACAA;IACA;;IAEA;IACAO;MACAmC;IACA;EACA,8FAGA;IAAA;IACA1C;IACA;;IAEA;IACAO;MACAsD;MACAC;QACA;QACA;QAEA9D;;QAEA;QACA;MACA;IACA;EACA,kGAGA0F;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA1F;;cAEA;cAAA;cAAA,OACA;gBAAA;cAAA;YAAA;cAEA2F;gBACAC;gBACAC;gBACAC;cACA;cAEAvF;gBACAC;gBACAC;cACA;;cAEA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAT;cACAO;gBACAC;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,gFAGA;IAAA;IACAT;IACA;IAEAO;MACAC;MACAQ;MACA+E;MACAjC;QACA;UACA;QACA;MACA;IACA;EACA,8FAGA;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA9D;;cAEA;cACAO;gBACAC;cACA;;cAEA;cACA;;cAEA;cAAA;cAAA,OACA;gBAAA;cAAA;YAAA;cAEAD;cAEAA;gBACAC;gBACAC;cACA;;cAEA;cACAoB;gBACAtB;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAP;cACAO;cAEAA;gBACAC;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;ACv9CA;AAAA;AAAA;AAAA;AAAk6C,CAAgB,uwCAAG,EAAC,C;;;;;;;;;;;ACAt7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/post/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/post/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=0722cd1a&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=0722cd1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0722cd1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/post/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=0722cd1a&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-tabs/u-tabs\" */ \"@/components/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading.page && !_vm.loading.post\n      ? _vm.formatTime(_vm.postData.createTime)\n      : null\n  var a0 =\n    !_vm.loading.page && !_vm.loading.post && !_vm.isOwnPost\n      ? {\n          id: _vm.postData.userId,\n          nickname: _vm.postData.username,\n        }\n      : null\n  var g0 = !_vm.loading.page\n    ? _vm.postData.topics && _vm.postData.topics.length\n    : null\n  var g1 = !_vm.loading.page\n    ? _vm.postData.images && _vm.postData.images.length\n    : null\n  var g2 = !_vm.loading.page && g1 ? _vm.postData.images.length : null\n  var l0 =\n    !_vm.loading.page && g1\n      ? _vm.__map(_vm.postData.images, function (img, index) {\n          var $orig = _vm.__get_orig(img)\n          var m1 = _vm.imgUrl(img)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var g3 =\n    !_vm.loading.page && !_vm.loading.comments ? _vm.commentList.length : null\n  var g4 =\n    !_vm.loading.page && !_vm.loading.comments ? _vm.commentList.length : null\n  var l2 =\n    !_vm.loading.page && !_vm.loading.comments && !(g4 === 0)\n      ? _vm.__map(_vm.commentList, function (comment, __i2__) {\n          var $orig = _vm.__get_orig(comment)\n          var m2 = comment.level >= 0 ? _vm.getLevelColor(comment.level) : null\n          var m3 = _vm.formatTime(comment.createTime)\n          var g5 = !comment.showFullContent ? comment.content.length : null\n          var g6 =\n            !comment.showFullContent && g5 > 100\n              ? comment.content.slice(0, 100)\n              : null\n          var g7 = comment.content.length\n          var g8 = comment.replies && comment.replies.length > 0\n          var l1 = g8\n            ? _vm.__map(comment.replies.slice(0, 2), function (reply, rIndex) {\n                var $orig = _vm.__get_orig(reply)\n                var g9 = reply.content.length\n                var g10 = g9 > 50 ? reply.content.slice(0, 50) : null\n                return {\n                  $orig: $orig,\n                  g9: g9,\n                  g10: g10,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            g5: g5,\n            g6: g6,\n            g7: g7,\n            g8: g8,\n            l1: l1,\n          }\n        })\n      : null\n  var g11 = _vm.commentText.trim()\n  var m4 = _vm.isCommentOwner(_vm.currentMoreComment)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        a0: a0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g3: g3,\n        g4: g4,\n        l2: l2,\n        g11: g11,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"detail-container\">\n    <!-- 页面加载动画 -->\n    <view v-if=\"loading.page\" class=\"page-loading\">\n      <view class=\"loading-container\">\n        <view class=\"loading-spinner\">\n          <u-loading mode=\"circle\" size=\"40\" color=\"#ff6b87\"></u-loading>\n        </view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </view>\n\n    <!-- 主要内容 -->\n    <scroll-view v-else class=\"content\" scroll-y>\n      <!-- 帖子内容 -->\n      <view class=\"post-detail\">\n        <!-- 帖子内容加载状态 -->\n        <view v-if=\"loading.post\" class=\"post-loading\">\n          <view class=\"skeleton-user\">\n            <view class=\"skeleton-avatar\"></view>\n            <view class=\"skeleton-user-info\">\n              <view class=\"skeleton-line skeleton-username\"></view>\n              <view class=\"skeleton-line skeleton-time\"></view>\n            </view>\n          </view>\n          <view class=\"skeleton-content\">\n            <view class=\"skeleton-line skeleton-title\"></view>\n            <view class=\"skeleton-line skeleton-text\"></view>\n            <view class=\"skeleton-line skeleton-text short\"></view>\n          </view>\n        </view>\n\n        <!-- 帖子实际内容 -->\n        <template v-else>\n          <!-- 用户信息 -->\n          <view class=\"user-info\">\n            <u-avatar :src=\"postData.userAvatar\" size=\"50\" @click=\"goUserProfile\"></u-avatar>\n            <view class=\"user-details\">\n              <text class=\"username\" @click=\"goUserProfile\">{{ postData.username }}</text>\n              <text class=\"time\">{{ formatTime(postData.createTime) }}</text>\n            </view>\n            <!-- 如果是本人帖子，显示操作按钮；否则显示关注按钮 -->\n            <view v-if=\"isOwnPost\" class=\"post-actions\">\n              <view class=\"action-btn\" @click=\"showPostActions\">\n                <u-icon name=\"more-dot-fill\" color=\"#666\" size=\"20\"></u-icon>\n              </view>\n            </view>\n            <FollowButton\n              v-else\n              :user=\"{ id: postData.userId, nickname: postData.username }\"\n              :followed=\"postData.isFollowed\"\n              size=\"mini\"\n              @follow=\"onUserFollow\"\n              @change=\"onFollowChange\"\n            />\n          </view>\n\n          <!-- 帖子标题 -->\n          <view class=\"post-title\" v-if=\"postData.title\">\n            <text class=\"title-text\">{{ postData.title }}</text>\n          </view>\n\n          <!-- 帖子文字内容 -->\n          <view class=\"post-content\">\n            <text class=\"content-text\">{{ postData.content }}</text>\n          </view>\n        </template>\n\n        <!-- 话题标签 -->\n        <view class=\"topic-tags\" v-if=\"postData.topics && postData.topics.length\">\n          <text\n            v-for=\"topic in postData.topics\"\n            :key=\"topic\"\n            class=\"topic-tag\"\n            @click=\"goTopic(topic)\"\n          >#{{ topic }}</text>\n        </view>\n\n        <!-- 帖子图片轮播 -->\n        <view class=\"post-images\" v-if=\"postData.images && postData.images.length\">\n          <swiper\n            class=\"image-swiper\"\n            :indicator-dots=\"postData.images.length > 1\"\n            :autoplay=\"false\"\n            :circular=\"true\"\n            indicator-color=\"rgba(255, 255, 255, 0.5)\"\n            indicator-active-color=\"#fff\"\n          >\n            <swiper-item v-for=\"(img, index) in postData.images\" :key=\"index\">\n              <image\n                :src=\"imgUrl(img)\"\n                class=\"swiper-image\"\n                mode=\"aspectFill\"\n                @click=\"previewImage(index)\"\n              />\n            </swiper-item>\n          </swiper>\n        </view>\n\n        <!-- 位置信息 -->\n        <view class=\"location-info\" v-if=\"postData.location\" @click=\"openLocation\">\n          <u-icon name=\"map\" color=\"#999\" size=\"16\"></u-icon>\n          <text class=\"location-text\">{{ postData.location }}</text>\n        </view>\n\n        <!-- 互动数据 -->\n        <view class=\"post-stats\">\n          <text class=\"stat-item\">{{ postData.likeCount }}人点赞</text>\n          <text class=\"stat-item\">{{ postData.commentCount }}条评论</text>\n          <text class=\"stat-item\">{{ postData.shareCount }}次分享</text>\n        </view>\n\n        <!-- 互动按钮 -->\n        <view class=\"action-bar\">\n          <view class=\"action-item\" @click=\"toggleLike\">\n            <u-icon\n              :name=\"postData.isLiked ? 'heart-fill' : 'heart'\"\n              :color=\"postData.isLiked ? '#ff4757' : '#666'\"\n              size=\"24\"\n            ></u-icon>\n            <text class=\"action-text\">点赞 ({{ postData.likeCount || 0 }})</text>\n          </view>\n          <view class=\"action-item\" @click=\"focusComment\">\n            <u-icon name=\"chat\" color=\"#666\" size=\"24\"></u-icon>\n            <text class=\"action-text\">评论</text>\n          </view>\n          <view class=\"action-item\" @click=\"sharePost\">\n            <u-icon name=\"share\" color=\"#666\" size=\"24\"></u-icon>\n            <text class=\"action-text\">分享</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 评论列表 -->\n      <view class=\"comments-section\">\n        <view class=\"comments-header\">\n          <text class=\"comments-title\">评论 {{ loading.comments ? '...' : commentList.length }}</text>\n\n          <u-tabs\n            :list=\"tabList\"\n            :current=\"currentTab\"\n            @change=\"changeSortType\"\n            :scrollable=\"false\"\n            activeColor=\"#2979ff\"\n            inactiveColor=\"#999\"\n            fontSize=\"28\"\n            lineColor=\"#2979ff\"\n            lineWidth=\"20\"\n            lineHeight=\"3\"\n            height=\"40\"\n          ></u-tabs>\n        </view>\n\n        <!-- 评论加载状态 -->\n        <view v-if=\"loading.comments\" class=\"comments-loading\">\n          <view v-for=\"n in 3\" :key=\"n\" class=\"skeleton-comment\">\n            <view class=\"skeleton-avatar\"></view>\n            <view class=\"skeleton-comment-content\">\n              <view class=\"skeleton-line skeleton-comment-user\"></view>\n              <view class=\"skeleton-line skeleton-comment-text\"></view>\n              <view class=\"skeleton-line skeleton-comment-text short\"></view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 评论实际内容 -->\n        <view v-else class=\"comment-list\">\n          <!-- 评论空状态 -->\n          <view v-if=\"commentList.length === 0\" class=\"comment-empty\">\n            <view class=\"empty-icon\">\n              <u-icon name=\"chat\" size=\"60\" color=\"#d0d0d0\"></u-icon>\n            </view>\n            <view class=\"empty-text\">暂无评论</view>\n            <view class=\"empty-desc\">快来发表第一条评论吧~</view>\n          </view>\n\n          <!-- 评论列表 -->\n          <view v-else v-for=\"comment in commentList\" :key=\"comment.id\" class=\"comment-item\">\n            <view class=\"user-avatar\" @click.stop=\"goCommentUserProfile(comment)\">\n              <u-avatar :src=\"comment.userAvatar\" size=\"40\"></u-avatar>\n            </view>\n            <view class=\"comment-content\">\n              <view class=\"user-info-row\">\n                <view class=\"user-info\">\n                  <view class=\"user-name\" @click.stop=\"goCommentUserProfile(comment)\">\n                    {{ comment.username }}\n                    <view\n                      v-if=\"comment.level >= 0\"\n                      class=\"user-level\"\n                      :style=\"{ backgroundColor: getLevelColor(comment.level) }\"\n                    >Lv.{{ comment.level }}</view>\n                  </view>\n                  <view class=\"time\">{{ formatTime(comment.createTime) }}</view>\n                </view>\n                <view\n                  class=\"like-btn\"\n                  :class=\"{ liked: comment.isLiked }\"\n                  @click.stop=\"toggleCommentLike(comment)\"\n                >\n                  <u-icon\n                    :name=\"comment.isLiked ? 'heart-fill' : 'heart'\"\n                    :color=\"comment.isLiked ? '#ff4757' : '#666'\"\n                    size=\"20\"\n                  ></u-icon>\n                  <text>{{ comment.likeCount || 0 }}</text>\n                </view>\n              </view>\n\n              <view class=\"text\">\n                <text>{{ comment.showFullContent ? comment.content : (comment.content.length > 100 ? comment.content.slice(0, 100) + '...' : comment.content) }}</text>\n                <view\n                  v-if=\"comment.content.length > 100\"\n                  class=\"expand-btn\"\n                  @click.stop=\"toggleContent(comment)\"\n                >{{ comment.showFullContent ? '收起' : '展开' }}</view>\n              </view>\n\n              <view class=\"actions\">\n                <view class=\"reply-btn\" @click.stop=\"replyComment(comment)\">\n                  <u-icon name=\"chat\" color=\"#666\" size=\"18\"></u-icon>\n                  <text>回复</text>\n                </view>\n                <view class=\"more-btn\" @click.stop=\"showMoreOptions(comment)\">\n                  <u-icon name=\"more-dot-fill\" color=\"#999\" size=\"18\"></u-icon>\n                </view>\n              </view>\n\n              <!-- 回复预览 -->\n              <view v-if=\"comment.replies && comment.replies.length > 0\" class=\"reply-preview\">\n                <view\n                  class=\"reply-item\"\n                  v-for=\"(reply, rIndex) in comment.replies.slice(0, 2)\"\n                  :key=\"rIndex\"\n                >\n                  <text\n                    class=\"reply-nickname\"\n                    @click.stop=\"goReplyUserProfile(reply)\"\n                  >{{ reply.username }}</text>\n                  <text\n                    v-if=\"reply.replyTo\"\n                    class=\"reply-to\"\n                    @click.stop=\"goReplyToUserProfile(reply.replyTo)\"\n                  >@{{ reply.replyTo.username }}</text>\n                  <text\n                    class=\"reply-content\"\n                  >: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>\n                </view>\n                <view\n                  v-if=\"comment.replyCount > 2\"\n                  class=\"view-more\"\n                  @click.stop=\"viewAllReplies(comment)\"\n                >查看全部{{ comment.replyCount }}条回复 ></view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 评论输入框 -->\n    <view class=\"comment-input-bar\">\n      <!-- 回复状态指示器 -->\n      <view v-if=\"isReplyMode\" class=\"reply-indicator\">\n        <view class=\"reply-info\">\n          <text\n            class=\"reply-text\"\n          >回复 @{{ currentReply && currentReply.username ? currentReply.username : '用户' }}</text>\n          <view class=\"cancel-reply-btn\" @click=\"cancelReplyMode\">\n            <text>✕</text>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"input-container\">\n        <u-avatar :src=\"currentUser.avatar\" size=\"32\"></u-avatar>\n        <input\n          ref=\"commentInput\"\n          v-model=\"commentText\"\n          class=\"comment-input\"\n          :placeholder=\"inputPlaceholder\"\n          @focus=\"onInputFocus\"\n          @blur=\"onInputBlur\"\n        />\n        <text class=\"send-btn\" :class=\"{ active: commentText.trim() }\" @click=\"sendComment\">发送</text>\n      </view>\n    </view>\n\n    <!-- 评论更多操作弹窗 -->\n    <u-popup v-model=\"showMorePopup\" mode=\"bottom\" border-radius=\"30\">\n      <view class=\"action-popup\">\n        <view class=\"action-item reply\" @click=\"replyFromMore\">\n          <view class=\"action-icon\">\n            <u-icon name=\"chat\" color=\"#ff6b87\" size=\"24\"></u-icon>\n          </view>\n          <text>回复</text>\n        </view>\n        <view class=\"action-item copy\" @click=\"copyComment\">\n          <view class=\"action-icon\">\n            <u-icon name=\"file-text\" color=\"#666\" size=\"24\"></u-icon>\n          </view>\n          <text>复制</text>\n        </view>\n        <view\n          v-if=\"isCommentOwner(currentMoreComment)\"\n          class=\"action-item delete\"\n          @click=\"deleteComment\"\n        >\n          <view class=\"action-icon\">\n            <u-icon name=\"trash\" color=\"#f56c6c\" size=\"24\"></u-icon>\n          </view>\n          <text>删除</text>\n        </view>\n        <view class=\"action-item report\" @click=\"reportComment\">\n          <view class=\"action-icon\">\n            <u-icon name=\"info-circle\" color=\"#999\" size=\"24\"></u-icon>\n          </view>\n          <text>举报</text>\n        </view>\n      </view>\n    </u-popup>\n\n    <!-- 帖子操作弹窗 -->\n    <u-popup v-model=\"showPostActionsPopup\" mode=\"bottom\" border-radius=\"30\">\n      <view class=\"action-popup\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">帖子操作</text>\n        </view>\n        <view class=\"action-item edit\" @click=\"editPost\">\n          <view class=\"action-icon\">\n            <u-icon name=\"edit-pen\" color=\"#2979ff\" size=\"24\"></u-icon>\n          </view>\n          <text>编辑帖子</text>\n        </view>\n        <view class=\"action-item permission\" @click=\"setPostPermission\">\n          <view class=\"action-icon\">\n            <u-icon name=\"lock\" color=\"#67C23A\" size=\"24\"></u-icon>\n          </view>\n          <text>权限设置</text>\n        </view>\n        <view class=\"action-item delete\" @click=\"deletePost\">\n          <view class=\"action-icon\">\n            <u-icon name=\"trash\" color=\"#f56c6c\" size=\"24\"></u-icon>\n          </view>\n          <text>删除帖子</text>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport FollowButton from \"../components/FollowButton.vue\";\nimport {\n  getPostDetail,\n  getPostComments,\n  createPostComment,\n  replyComment,\n  likePost,\n  unlikePost,\n  likeComment,\n  getUserProfile\n} from \"@/utils/socialApi.js\";\nimport {\n  imgUrlForWebp,\n  imgUrlForThumbnail,\n  imgUrlForWebpDetail\n} from \"@/utils/imgUrl.js\";\n\nexport default {\n  name: \"PostDetail\",\n  components: {\n    FollowButton\n  },\n  data() {\n    return {\n      postId: \"\",\n      postData: {},\n      commentList: [],\n      commentText: \"\",\n      sortType: \"time\", // time, hot\n      currentTab: 0,\n      tabList: [{ name: \"最新\" }, { name: \"最热\" }],\n      currentUser: {\n        id: null,\n        avatar: \"/static/images/toux.png\", // 默认头像\n        nickname: \"加载中...\"\n      },\n      // 加载状态\n      loading: {\n        page: true, // 页面整体加载\n        post: true, // 帖子详情加载\n        comments: true, // 评论列表加载\n        user: true // 用户信息加载\n      },\n      replyingTo: null,\n      // 回复相关状态\n      isReplyMode: false,\n      currentReply: null,\n      inputPlaceholder: \"写评论...\",\n      // 更多操作弹窗\n      showMorePopup: false,\n      currentMoreComment: null,\n      // 帖子操作弹窗\n      showPostActionsPopup: false\n    };\n  },\n  onLoad(options) {\n    console.log(\"详情页接收到的参数:\", options);\n\n    // 获取帖子ID，支持多种参数名\n    this.postId = options.id || options.postId || \"7\"; // 默认使用ID 7进行测试\n\n    console.log(\"最终使用的帖子ID:\", this.postId);\n\n    // 确保postId是字符串类型\n    this.postId = String(this.postId);\n\n    // 开始加载数据\n    this.initPageData();\n  },\n  computed: {\n    // 判断是否是当前用户的帖子\n    isOwnPost() {\n      return (\n        this.currentUser.id &&\n        this.postData.userId &&\n        String(this.currentUser.id) === String(this.postData.userId)\n      );\n    }\n  },\n  methods: {\n    // 初始化页面数据\n    async initPageData() {\n      try {\n        // 设置加载状态\n        this.loading = {\n          page: true,\n          post: true,\n          comments: true,\n          user: true\n        };\n\n        // 并行加载数据\n        await Promise.all([\n          this.loadCurrentUser(),\n          this.loadPostDetail(),\n          this.loadComments()\n        ]);\n\n        // 所有数据加载完成，关闭页面加载状态\n        this.loading.page = false;\n      } catch (error) {\n        console.error(\"页面数据初始化失败:\", error);\n        this.loading.page = false;\n\n        uni.showToast({\n          title: \"页面加载失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    async loadCurrentUser() {\n      try {\n        this.loading.user = true;\n\n        const userId = uni.getStorageSync(\"userid\");\n        if (!userId) {\n          console.log(\"用户未登录，使用默认头像\");\n          this.loading.user = false;\n          return;\n        }\n\n        console.log(\"加载当前用户信息，ID:\", userId);\n        const result = await getUserProfile(userId);\n        console.log(\"当前用户信息API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const user = result.data;\n          this.currentUser = {\n            id: user.userId,\n            avatar: imgUrlForThumbnail(user.avatar),\n            nickname: user.nickname || \"无名氏\"\n          };\n          console.log(\"当前用户信息加载成功:\", this.currentUser);\n        } else {\n          console.error(\"获取用户信息失败:\", result);\n        }\n\n        this.loading.user = false;\n      } catch (error) {\n        console.error(\"加载当前用户信息失败:\", error);\n        this.loading.user = false;\n      }\n    },\n\n    async loadPostDetail() {\n      try {\n        this.loading.post = true;\n\n        console.log(\"加载帖子详情，ID:\", this.postId);\n        console.log(\"postId类型:\", typeof this.postId);\n\n        if (!this.postId) {\n          throw new Error(\"帖子ID为空\");\n        }\n\n        const result = await getPostDetail(this.postId);\n        console.log(\"帖子详情API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          const post = result.data;\n          console.log(\"🔥 API返回的原始帖子数据:\", post);\n          console.log(\n            \"🔥 API返回的isLiked值:\",\n            post.isLiked,\n            \"类型:\",\n            typeof post.isLiked\n          );\n\n          this.postData = {\n            id: post.id,\n            userId: post.userId,\n            username: post.nickname || \"无名氏\",\n            userAvatar:\n              \"https://file.foxdance.com.cn\" +\n                post.avatar +\n                \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\" ||\n              \"/static/images/toux.png\",\n            title: post.title || \"\",\n            content: post.content,\n            images: post.images || [],\n            topics: post.tags || [],\n            location: post.locationName || \"\",\n            locationAddress: post.locationAddress || \"\",\n            locationLatitude: post.locationLatitude || null,\n            locationLongitude: post.locationLongitude || null,\n            likeCount: post.likeCount || 0,\n            commentCount: post.commentCount || 0,\n            shareCount: post.shareCount || 0,\n            isLiked: Boolean(post.isLiked),\n            isFollowed: post.isFollowed || false,\n            createTime: new Date(post.createTime)\n          };\n          console.log(\"帖子数据加载成功:\", this.postData);\n        } else {\n          console.error(\"API返回数据格式错误:\", result);\n          throw new Error(\"获取帖子详情失败 - API返回格式错误\");\n        }\n\n        this.loading.post = false;\n      } catch (error) {\n        console.error(\"加载帖子详情失败:\", error);\n        this.loading.post = false;\n\n        uni.showToast({\n          title: \"帖子加载失败\",\n          icon: \"none\"\n        });\n        // 加载失败，返回上一页\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }\n    },\n\n    async loadComments() {\n      try {\n        this.loading.comments = true;\n\n        console.log(\"加载帖子评论列表，帖子ID:\", this.postId);\n        const result = await getPostComments(this.postId, {\n          userId: 1, // 当前用户ID，实际应该从用户状态获取\n          filter: this.sortType === \"time\" ? \"latest\" : \"hot\",\n          current: 1,\n          pageSize: 50\n        });\n        console.log(\"帖子评论列表API返回:\", result);\n\n        if (result && result.code === 0 && result.data) {\n          // 处理API返回格式\n          let comments = [];\n          if (result.data.comments) {\n            comments = result.data.comments;\n          } else if (Array.isArray(result.data)) {\n            comments = result.data;\n          }\n\n          this.commentList = comments.map(comment => ({\n            id: comment.id,\n            userId: comment.userId,\n            username: comment.nickname || \"无名氏\",\n            userAvatar:\n              \"https://file.foxdance.com.cn\" +\n                comment.avatar +\n                \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85\" ||\n              \"/static/images/toux.png\",\n            content: comment.content,\n            likeCount: comment.likes || 0,\n            isLiked: comment.isLiked || false,\n            level: comment.level || 0,\n            createTime: new Date(comment.createdAt || comment.createTime),\n            // 后端返回的回复预览数据（来自comment_replies表）\n            replies: (comment.replies || []).map(reply => ({\n              id: reply.id,\n              userId: reply.userId,\n              username: reply.nickname || \"无名氏\",\n              userAvatar:\n                \"https://file.foxdance.com.cn\" + reply.avatar ||\n                \"/static/images/toux.png\",\n              content: reply.content,\n              likeCount: reply.likes || 0,\n              isLiked: reply.isLiked || false,\n              replyTo: reply.replyTo\n                ? {\n                    userId: reply.replyTo.id,\n                    username: reply.replyTo.nickname || \"用户\"\n                  }\n                : null,\n              createTime: new Date(reply.createdAt || reply.createTime)\n            })),\n            replyCount: comment.replyCount || 0\n          }));\n\n          console.log(\"评论列表加载成功:\", this.commentList.length);\n        } else {\n          console.log(\"评论API返回格式不正确或无数据\");\n          this.commentList = [];\n        }\n\n        this.loading.comments = false;\n      } catch (error) {\n        console.error(\"加载评论失败:\", error);\n        this.loading.comments = false;\n\n        uni.showToast({\n          title: \"评论加载失败\",\n          icon: \"none\"\n        });\n        this.commentList = [];\n      }\n    },\n\n    formatTime(time) {\n      const now = new Date();\n      const diff = now - new Date(time);\n      const minutes = Math.floor(diff / 60000);\n      const hours = Math.floor(diff / 3600000);\n      const days = Math.floor(diff / 86400000);\n\n      if (minutes < 60) return `${minutes}分钟前`;\n      if (hours < 24) return `${hours}小时前`;\n      return `${days}天前`;\n    },\n\n    goBack() {\n      uni.navigateBack();\n    },\n\n    goUserProfile() {\n      if (!this.postData.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\"跳转到帖子作者主页，用户ID:\", this.postData.userId);\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${this.postData.userId}`\n      });\n    },\n\n    // 跳转到评论用户主页\n    goCommentUserProfile(comment) {\n      if (!comment.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\n        \"跳转到评论用户主页，用户ID:\",\n        comment.userId,\n        \"用户名:\",\n        comment.username\n      );\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${comment.userId}`\n      });\n    },\n\n    // 跳转到回复用户主页\n    goReplyUserProfile(reply) {\n      if (!reply.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\n        \"跳转到回复用户主页，用户ID:\",\n        reply.userId,\n        \"用户名:\",\n        reply.username\n      );\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${reply.userId}`\n      });\n    },\n\n    // 跳转到被回复用户主页\n    goReplyToUserProfile(replyToUser) {\n      if (!replyToUser.userId) {\n        uni.showToast({\n          title: \"用户信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      console.log(\n        \"跳转到被回复用户主页，用户ID:\",\n        replyToUser.userId,\n        \"用户名:\",\n        replyToUser.username\n      );\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?id=${replyToUser.userId}`\n      });\n    },\n\n    onUserFollow(data) {\n      console.log(\"关注操作:\", data);\n      // 这里可以调用API进行关注/取消关注操作\n    },\n\n    onFollowChange(data) {\n      // 更新本地数据\n      this.postData.isFollowed = data.isFollowed;\n      console.log(\"关注状态变化:\", data);\n    },\n    imgUrl(img) {\n      return (\n        img +\n        \"?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/600x600>/format/webp\"\n      );\n    },\n\n    async toggleLike() {\n      console.log(\"=== 点赞功能开始 ===\");\n      console.log(\"点赞按钮被点击\");\n      console.log(\"当前帖子数据:\", this.postData);\n\n      // 先测试简单的UI更新\n      uni.showToast({\n        title: \"点赞功能被触发\",\n        icon: \"none\",\n        duration: 2000\n      });\n\n      if (!this.postData || !this.postData.id) {\n        console.error(\"帖子数据无效:\", this.postData);\n        uni.showToast({\n          title: \"帖子信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      const originalLiked = this.postData.isLiked;\n      const originalCount = this.postData.likeCount || 0;\n\n      try {\n        // 先更新UI，提供即时反馈\n        this.postData.isLiked = !this.postData.isLiked;\n        this.postData.likeCount = this.postData.isLiked\n          ? originalCount + 1\n          : Math.max(originalCount - 1, 0);\n\n        console.log(\n          \"点赞操作 - 帖子ID:\",\n          this.postData.id,\n          \"类型:\",\n          typeof this.postData.id,\n          \"操作:\",\n          this.postData.isLiked ? \"点赞\" : \"取消点赞\"\n        );\n\n        // 确保帖子ID是数字类型\n        const postId = Number(this.postData.id);\n        const userId = Number(uni.getStorageSync(\"userid\")); // 从缓存获取当前用户ID\n\n        console.log(\"🔥 点赞操作详情:\");\n        console.log(\"  - postId:\", postId, \"userId:\", userId);\n        console.log(\n          \"  - 原始状态 isLiked:\",\n          originalLiked,\n          \"likeCount:\",\n          originalCount\n        );\n        console.log(\n          \"  - 新状态 isLiked:\",\n          this.postData.isLiked,\n          \"likeCount:\",\n          this.postData.likeCount\n        );\n\n        if (!userId) {\n          console.error(\"用户未登录\");\n          uni.showToast({\n            title: \"请先登录\",\n            icon: \"none\"\n          });\n          // 回滚UI状态\n          this.postData.isLiked = originalLiked;\n          this.postData.likeCount = originalCount;\n          return;\n        }\n\n        // 调用对应的API\n        let result;\n        if (this.postData.isLiked) {\n          console.log(\"调用点赞API\");\n          result = await likePost(postId, userId);\n        } else {\n          console.log(\"调用取消点赞API\");\n          result = await unlikePost(postId, userId);\n        }\n\n        console.log(\"点赞API返回:\", result);\n\n        if (result && result.code === 0) {\n          // API调用成功\n          console.log(\"点赞操作成功\");\n          uni.showToast({\n            title: this.postData.isLiked ? \"点赞成功\" : \"取消点赞\",\n            icon: \"success\",\n            duration: 1000\n          });\n        } else {\n          // API调用失败，回滚UI状态\n          console.warn(\"点赞API调用失败:\", result);\n          this.postData.isLiked = originalLiked;\n          this.postData.likeCount = originalCount;\n\n          uni.showToast({\n            title: result?.message || \"操作失败，请重试\",\n            icon: \"none\"\n          });\n        }\n      } catch (error) {\n        console.error(\"点赞操作失败:\", error);\n\n        // 回滚UI状态\n        this.postData.isLiked = originalLiked;\n        this.postData.likeCount = originalCount;\n\n        uni.showToast({\n          title: \"网络错误，请重试\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    toggleCommentLike(comment) {\n      comment.isLiked = !comment.isLiked;\n      comment.likeCount += comment.isLiked ? 1 : -1;\n    },\n\n    previewImage(index) {\n      uni.previewImage({\n        urls: this.postData.images,\n        current: index\n      });\n    },\n\n    sharePost() {\n      uni.showActionSheet({\n        itemList: [\"分享到微信\", \"分享到朋友圈\", \"复制链接\"],\n        success: () => {\n          this.$u.toast(\"分享成功\");\n          this.postData.shareCount++;\n        }\n      });\n    },\n\n    goTopic(topic) {\n      uni.navigateTo({\n        url: `/pagesSub/social/topic/detail?name=${topic}`\n      });\n    },\n\n    openLocation() {\n      if (!this.postData.location) return;\n\n      console.log(\"打开位置信息:\", this.postData.location);\n\n      // 如果有经纬度信息，可以打开地图\n      if (this.postData.locationLatitude && this.postData.locationLongitude) {\n        uni.openLocation({\n          latitude: Number(this.postData.locationLatitude),\n          longitude: Number(this.postData.locationLongitude),\n          name: this.postData.location,\n          address: this.postData.locationAddress || this.postData.location,\n          success: () => {\n            console.log(\"打开地图成功\");\n          },\n          fail: err => {\n            console.error(\"打开地图失败:\", err);\n            // 如果打开地图失败，显示位置信息\n            uni.showModal({\n              title: \"位置信息\",\n              content: this.postData.location,\n              showCancel: false\n            });\n          }\n        });\n      } else {\n        // 没有经纬度信息，只显示位置名称\n        uni.showModal({\n          title: \"位置信息\",\n          content: this.postData.location,\n          showCancel: false\n        });\n      }\n    },\n\n    changeSortType(...args) {\n      console.log(\"tabs点击参数:\", args);\n      const index =\n        typeof args[0] === \"number\"\n          ? args[0]\n          : args[0] && args[0].index !== undefined\n          ? args[0].index\n          : 0;\n      this.currentTab = index;\n      this.sortType = index === 0 ? \"time\" : \"hot\";\n      // 重新加载评论\n      this.loadComments();\n      this.loadComments();\n    },\n\n    focusComment() {\n      this.$refs.commentInput.focus();\n    },\n\n    onInputFocus() {\n      // 输入框获得焦点\n    },\n\n    onInputBlur() {\n      // 输入框失去焦点\n    },\n\n    replyComment(comment) {\n      // 设置回复状态\n      this.isReplyMode = true;\n      this.currentReply = comment;\n      this.inputPlaceholder = `@${comment.username}`;\n\n      // 聚焦输入框\n      this.$nextTick(() => {\n        if (this.$refs.commentInput) {\n          this.$refs.commentInput.focus();\n        }\n      });\n    },\n\n    async sendComment() {\n      if (!this.commentText.trim()) {\n        uni.showToast({\n          title: \"请输入评论内容\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      try {\n        console.log(\n          \"发送评论，帖子ID:\",\n          this.postId,\n          \"内容:\",\n          this.commentText,\n          \"回复模式:\",\n          this.isReplyMode\n        );\n\n        // 根据是否是回复模式调用不同的API\n        if (this.isReplyMode && this.currentReply) {\n          // 回复评论\n          await this.sendReplyComment();\n        } else {\n          // 普通评论\n          await this.sendNormalComment();\n        }\n      } catch (error) {\n        console.error(\"发送评论失败:\", error);\n        uni.showToast({\n          title: \"发送失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 发送普通评论\n    async sendNormalComment() {\n      const commentData = {\n        postId: String(this.postId),\n        content: this.commentText.trim(),\n        userId: 1 // 当前用户ID，实际应该从用户状态获取\n      };\n\n      console.log(\"发送普通评论数据:\", commentData);\n\n      try {\n        const result = await createPostComment(commentData);\n        console.log(\"普通评论API返回:\", result);\n\n        if (result && result.code === 0) {\n          this.handleCommentSuccess();\n          return;\n        }\n      } catch (apiError) {\n        console.warn(\"普通评论API调用失败，使用临时方案:\", apiError);\n      }\n\n      // API失败时的临时处理：直接添加到本地列表\n      console.log(\"使用临时评论方案\");\n      const newComment = {\n        id: Date.now(),\n        userId: this.currentUser.id || 1,\n        username: this.currentUser.nickname || \"当前用户\",\n        userAvatar: this.currentUser.avatar || \"/static/images/toux.png\",\n        content: this.commentText.trim(),\n        likeCount: 0,\n        isLiked: false,\n        level: 0,\n        createTime: new Date(),\n        replyCount: 0,\n        replies: []\n      };\n\n      // 添加到评论列表顶部\n      this.commentList.unshift(newComment);\n      this.handleCommentSuccess();\n    },\n\n    // 发送回复评论\n    async sendReplyComment() {\n      const replyData = {\n        content: this.commentText.trim(),\n        userId: 1, // 当前用户ID，实际应该从用户状态获取\n        replyToUserId: this.currentReply.userId,\n        replyToUsername: this.currentReply.username\n      };\n\n      console.log(\n        \"发送回复评论数据:\",\n        replyData,\n        \"目标评论ID:\",\n        this.currentReply.id\n      );\n\n      try {\n        const result = await replyComment(this.currentReply.id, replyData);\n        console.log(\"回复评论API返回:\", result);\n\n        if (result && result.code === 0) {\n          this.handleCommentSuccess();\n          return;\n        }\n      } catch (apiError) {\n        console.warn(\"回复评论API调用失败，使用临时方案:\", apiError);\n      }\n\n      // API失败时的临时处理：重新加载评论列表\n      console.log(\"回复API失败，重新加载评论列表\");\n      this.handleCommentSuccess();\n\n      // 重新加载评论列表以获取最新的回复数据\n      setTimeout(() => {\n        this.loadComments();\n      }, 500);\n    },\n\n    // 处理评论成功的通用逻辑\n    handleCommentSuccess() {\n      const wasReplyMode = this.isReplyMode;\n\n      // 清空输入框和回复状态\n      this.commentText = \"\";\n      this.replyingTo = null;\n      this.cancelReplyMode();\n\n      // 更新帖子评论数（只有普通评论才增加评论数，回复不增加）\n      if (!wasReplyMode) {\n        this.postData.commentCount++;\n      }\n\n      // 显示成功提示\n      uni.showToast({\n        title: wasReplyMode ? \"回复成功\" : \"评论成功\",\n        icon: \"success\"\n      });\n\n      // 重新加载评论列表以获取最新数据（包括新的回复）\n      setTimeout(() => {\n        this.loadComments();\n      }, 500);\n    },\n\n    // 点赞评论\n    async toggleCommentLike(comment) {\n      if (!comment || !comment.id) {\n        uni.showToast({\n          title: \"评论信息错误\",\n          icon: \"none\"\n        });\n        return;\n      }\n\n      const originalLiked = comment.isLiked;\n      const originalCount = comment.likeCount || 0;\n\n      try {\n        // 先更新UI，提供即时反馈\n        comment.isLiked = !comment.isLiked;\n        comment.likeCount = comment.isLiked\n          ? originalCount + 1\n          : Math.max(originalCount - 1, 0);\n\n        console.log(\n          \"点赞评论 - 评论ID:\",\n          comment.id,\n          \"操作:\",\n          comment.isLiked ? \"点赞\" : \"取消点赞\"\n        );\n\n        // 获取当前用户ID\n        const userId = Number(uni.getStorageSync(\"userid\"));\n        if (!userId) {\n          console.error(\"用户未登录\");\n          uni.showToast({\n            title: \"请先登录\",\n            icon: \"none\"\n          });\n          // 回滚UI状态\n          comment.isLiked = originalLiked;\n          comment.likeCount = originalCount;\n          return;\n        }\n\n        // 调用评论点赞API\n        const result = await likeComment(comment.id, {\n          userId: userId,\n          action: comment.isLiked ? \"like\" : \"unlike\"\n        });\n\n        console.log(\"评论点赞API返回:\", result);\n\n        if (result && result.code === 0) {\n          // API调用成功\n          uni.showToast({\n            title: comment.isLiked ? \"点赞成功\" : \"取消点赞\",\n            icon: \"success\",\n            duration: 1000\n          });\n        } else {\n          // API调用失败，回滚UI状态\n          console.warn(\"评论点赞API调用失败:\", result);\n          comment.isLiked = originalLiked;\n          comment.likeCount = originalCount;\n\n          uni.showToast({\n            title: \"操作失败，请重试\",\n            icon: \"none\"\n          });\n        }\n      } catch (error) {\n        console.error(\"评论点赞操作失败:\", error);\n\n        // 回滚UI状态\n        comment.isLiked = originalLiked;\n        comment.likeCount = originalCount;\n\n        uni.showToast({\n          title: \"网络错误，请重试\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 展开/收起评论内容\n    toggleContent(comment) {\n      this.$set(comment, \"showFullContent\", !comment.showFullContent);\n    },\n\n    // 获取用户等级颜色\n    getLevelColor(level) {\n      const colors = [\n        \"#999\",\n        \"#2979ff\",\n        \"#67C23A\",\n        \"#E6A23C\",\n        \"#F56C6C\",\n        \"#9C27B0\"\n      ];\n      return colors[Math.min(level, colors.length - 1)] || \"#999\";\n    },\n\n    // 显示更多操作\n    showMoreOptions(comment) {\n      this.currentMoreComment = comment;\n      this.showMorePopup = true;\n    },\n\n    // 从更多操作中回复\n    replyFromMore() {\n      if (this.currentMoreComment) {\n        this.showMorePopup = false;\n        setTimeout(() => {\n          this.replyComment(this.currentMoreComment);\n        }, 300);\n      }\n    },\n\n    // 复制评论\n    copyComment() {\n      if (!this.currentMoreComment) return;\n\n      uni.setClipboardData({\n        data: this.currentMoreComment.content,\n        success: () => {\n          uni.showToast({\n            title: \"复制成功\",\n            icon: \"success\"\n          });\n        }\n      });\n      this.showMorePopup = false;\n    },\n\n    // 删除评论\n    deleteComment() {\n      if (!this.currentMoreComment) return;\n\n      uni.showModal({\n        title: \"确认删除\",\n        content: \"确定要删除这条评论吗？\",\n        success: res => {\n          if (res.confirm) {\n            // 这里应该调用删除API\n            console.log(\"删除评论:\", this.currentMoreComment.id);\n\n            // 临时从列表中移除\n            const index = this.commentList.findIndex(\n              item => item.id === this.currentMoreComment.id\n            );\n            if (index > -1) {\n              this.commentList.splice(index, 1);\n              this.postData.commentCount--;\n            }\n\n            uni.showToast({\n              title: \"删除成功\",\n              icon: \"success\"\n            });\n          }\n        }\n      });\n      this.showMorePopup = false;\n    },\n\n    // 举报评论\n    reportComment() {\n      uni.showToast({\n        title: \"举报成功\",\n        icon: \"success\"\n      });\n      this.showMorePopup = false;\n    },\n\n    // 判断是否是评论作者\n    isCommentOwner(comment) {\n      return comment && comment.userId === this.currentUser.id;\n    },\n\n    // 取消回复模式\n    cancelReplyMode() {\n      this.isReplyMode = false;\n      this.currentReply = null;\n      this.inputPlaceholder = \"写评论...\";\n\n      uni.showToast({\n        title: \"已取消回复\",\n        icon: \"none\",\n        duration: 1000\n      });\n    },\n\n    // 查看全部回复\n    async viewAllReplies(comment) {\n      console.log(\"查看全部回复:\", comment.id);\n\n      try {\n        // 调用获取回复列表的API\n        const response = await uni.request({\n          url: `${this.$http.vote_baseUrl}/comments/${comment.id}/replies`,\n          method: \"GET\",\n          data: {\n            userId: 1, // 当前用户ID\n            sort: \"latest\"\n          }\n        });\n\n        if (response.data && response.data.code === 0) {\n          const replies = response.data.data || [];\n\n          // 更新评论的回复列表\n          const targetComment = this.commentList.find(c => c.id === comment.id);\n          if (targetComment) {\n            targetComment.replies = replies.map(reply => ({\n              id: reply.id,\n              userId: reply.userId,\n              username: reply.nickname || \"无名氏\",\n              userAvatar: reply.avatar\n                ? reply.avatar.startsWith(\"http\")\n                  ? reply.avatar\n                  : \"https://file.foxdance.com.cn\" + reply.avatar\n                : \"/static/images/toux.png\",\n              content: reply.content,\n              likeCount: reply.likes || 0,\n              isLiked: reply.isLiked || false,\n              replyTo: reply.replyTo\n                ? {\n                    userId: reply.replyTo.id,\n                    username: reply.replyTo.nickname || \"用户\"\n                  }\n                : null,\n              createTime: new Date(reply.createdAt || reply.createTime)\n            }));\n          }\n\n          uni.showToast({\n            title: `加载了${replies.length}条回复`,\n            icon: \"success\"\n          });\n        }\n      } catch (error) {\n        console.error(\"获取回复列表失败:\", error);\n        uni.showToast({\n          title: \"加载回复失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: [\"举报\", \"不感兴趣\", \"屏蔽用户\"],\n        success: res => {\n          console.log(\"更多操作:\", res.tapIndex);\n        }\n      });\n    },\n\n    // 显示帖子操作菜单\n    showPostActions() {\n      console.log(\"显示帖子操作菜单\");\n      this.showPostActionsPopup = true;\n    },\n\n    // 编辑帖子\n    editPost() {\n      console.log(\"编辑帖子:\", this.postData.id);\n      this.showPostActionsPopup = false;\n\n      // 跳转到编辑页面，传递帖子数据\n      uni.navigateTo({\n        url: `/pagesSub/social/publish/index?mode=edit&postId=${this.postData.id}`\n      });\n    },\n\n    // 权限设置\n    setPostPermission() {\n      console.log(\"设置帖子权限:\", this.postData.id);\n      this.showPostActionsPopup = false;\n\n      // 显示权限设置选项\n      uni.showActionSheet({\n        itemList: [\"公开\", \"仅关注者可见\", \"私密\"],\n        success: res => {\n          const permissions = [\"public\", \"followers\", \"private\"];\n          const selectedPermission = permissions[res.tapIndex];\n\n          console.log(\"选择的权限:\", selectedPermission);\n\n          // 这里应该调用API更新帖子权限\n          this.updatePostPermission(selectedPermission);\n        }\n      });\n    },\n\n    // 更新帖子权限\n    async updatePostPermission(permission) {\n      try {\n        // 这里应该调用实际的API\n        console.log(\"更新帖子权限:\", this.postData.id, permission);\n\n        // 临时模拟API调用\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        const permissionNames = {\n          public: \"公开\",\n          followers: \"仅关注者可见\",\n          private: \"私密\"\n        };\n\n        uni.showToast({\n          title: `已设置为${permissionNames[permission]}`,\n          icon: \"success\"\n        });\n\n        // 更新本地数据\n        this.postData.permission = permission;\n      } catch (error) {\n        console.error(\"更新帖子权限失败:\", error);\n        uni.showToast({\n          title: \"设置失败，请重试\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 删除帖子\n    deletePost() {\n      console.log(\"删除帖子:\", this.postData.id);\n      this.showPostActionsPopup = false;\n\n      uni.showModal({\n        title: \"确认删除\",\n        content: \"删除后无法恢复，确定要删除这条帖子吗？\",\n        confirmColor: \"#f56c6c\",\n        success: res => {\n          if (res.confirm) {\n            this.confirmDeletePost();\n          }\n        }\n      });\n    },\n\n    // 确认删除帖子\n    async confirmDeletePost() {\n      try {\n        console.log(\"确认删除帖子:\", this.postData.id);\n\n        // 显示加载提示\n        uni.showLoading({\n          title: \"删除中...\"\n        });\n\n        // 这里应该调用实际的删除API\n        // const result = await deletePost(this.postData.id)\n\n        // 临时模拟API调用\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        uni.hideLoading();\n\n        uni.showToast({\n          title: \"删除成功\",\n          icon: \"success\"\n        });\n\n        // 删除成功后返回上一页\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      } catch (error) {\n        console.error(\"删除帖子失败:\", error);\n        uni.hideLoading();\n\n        uni.showToast({\n          title: \"删除失败，请重试\",\n          icon: \"none\"\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.detail-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-bottom: 60px;\n}\n\n/* 页面加载动画 */\n.page-loading {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #f8f9fa;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24rpx;\n}\n\n.loading-spinner {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #666;\n  font-weight: 400;\n}\n\n/* 骨架屏样式 */\n.post-loading,\n.comments-loading {\n  padding: 32rpx;\n}\n\n.skeleton-user {\n  display: flex;\n  align-items: center;\n  margin-bottom: 32rpx;\n}\n\n.skeleton-avatar {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n}\n\n.skeleton-user-info {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.skeleton-line {\n  height: 32rpx;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4rpx;\n  margin-bottom: 16rpx;\n}\n\n.skeleton-username {\n  width: 160rpx;\n}\n\n.skeleton-time {\n  width: 120rpx;\n}\n\n.skeleton-content {\n  margin-bottom: 32rpx;\n}\n\n.skeleton-title {\n  width: 80%;\n  height: 40rpx;\n  margin-bottom: 24rpx;\n}\n\n.skeleton-text {\n  width: 100%;\n  height: 32rpx;\n  margin-bottom: 16rpx;\n}\n\n.skeleton-text.short {\n  width: 60%;\n}\n\n.skeleton-comment {\n  display: flex;\n  padding: 24rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.skeleton-comment-content {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.skeleton-comment-user {\n  width: 140rpx;\n  height: 28rpx;\n}\n\n.skeleton-comment-text {\n  width: 100%;\n  height: 28rpx;\n  margin-bottom: 12rpx;\n}\n\n.skeleton-comment-text.short {\n  width: 70%;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* 评论空状态样式 */\n.comment-empty {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 32rpx 80rpx;\n  text-align: center;\n}\n\n.empty-icon {\n  margin-bottom: 32rpx;\n  opacity: 0.5;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #999;\n  font-weight: 500;\n  margin-bottom: 16rpx;\n}\n\n.empty-desc {\n  font-size: 28rpx;\n  color: #ccc;\n  line-height: 1.4;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.post-detail {\n  background: #fff;\n  padding: 40rpx 32rpx;\n  margin-bottom: 16rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 32rpx;\n}\n\n.user-details {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.post-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-btn {\n  width: 64rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f8f9fa;\n  border-radius: 50%;\n  border: 1rpx solid #e4e7ed;\n  transition: all 0.2s ease;\n}\n\n.action-btn:active {\n  background: #e4e7ed;\n  transform: scale(0.95);\n}\n\n.username {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  display: block;\n  cursor: pointer;\n  transition: color 0.2s ease;\n}\n\n.username:active {\n  color: #2979ff;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 4rpx;\n  display: block;\n}\n\n.location {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 4rpx;\n  display: block;\n}\n\n.post-title {\n  margin-bottom: 24rpx;\n}\n\n.title-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  line-height: 1.4;\n  color: #333;\n  display: block;\n}\n\n.content-text {\n  font-size: 32rpx;\n  line-height: 1.6;\n  color: #333;\n  margin-bottom: 24rpx;\n  display: block;\n}\n\n.topic-tags {\n  margin-bottom: 32rpx;\n}\n\n.topic-tag {\n  color: #2979ff;\n  font-size: 28rpx;\n  margin-right: 16rpx;\n}\n\n/* 位置信息样式 */\n.location-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 32rpx;\n  padding: 16rpx 20rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  border-left: 4rpx solid #2979ff;\n}\n\n.location-text {\n  font-size: 26rpx;\n  color: #666;\n  margin-left: 8rpx;\n  flex: 1;\n}\n\n.post-images {\n  margin-bottom: 32rpx;\n}\n\n.image-swiper {\n  width: 100%;\n  height: 600rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n}\n\n.swiper-image {\n  width: 100%;\n  height: 100%;\n}\n\n.post-stats {\n  padding: 24rpx 0;\n  border-top: 2rpx solid #f0f0f0;\n  border-bottom: 2rpx solid #f0f0f0;\n  margin-bottom: 24rpx;\n}\n\n.stat-item {\n  font-size: 26rpx;\n  color: #666;\n  margin-right: 32rpx;\n}\n\n.action-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n.comments-section {\n  background: #fff;\n  padding: 32rpx;\n}\n\n.comments-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 32rpx;\n}\n\n.comments-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.comment-item {\n  display: flex;\n  margin-bottom: 32rpx;\n}\n\n.comment-content {\n  flex: 1;\n  margin-left: 24rpx;\n}\n\n.comment-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.comment-username {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-right: 16rpx;\n}\n\n.comment-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.comment-text {\n  font-size: 28rpx;\n  line-height: 1.4;\n  color: #333;\n  margin-bottom: 16rpx;\n  display: block;\n}\n\n.replies {\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  padding: 16rpx 24rpx;\n  margin-bottom: 16rpx;\n}\n\n.reply-item {\n  margin-bottom: 8rpx;\n}\n\n.reply-user {\n  font-size: 26rpx;\n  color: #2979ff;\n  margin-right: 8rpx;\n}\n\n.reply-text {\n  font-size: 26rpx;\n  color: #333;\n}\n\n.comment-actions {\n  display: flex;\n  align-items: center;\n  gap: 32rpx;\n}\n\n.comment-action {\n  display: flex;\n  align-items: center;\n}\n\n.action-count,\n.action-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 8rpx;\n}\n\n.comment-input-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  border-top: 2rpx solid #e4e7ed;\n  padding: 16rpx 32rpx;\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n}\n\n.input-container {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  height: 104rpx;\n  position: relative;\n}\n\n.comment-input {\n  width: calc(100% - 128rpx);\n  height: 72rpx;\n  line-height: 72rpx;\n  background: #f5f5f5;\n  border: none;\n  border-radius: 36rpx;\n  padding: 0 32rpx;\n  font-size: 28rpx;\n  outline: none;\n  box-sizing: border-box;\n  -webkit-appearance: none;\n  appearance: none;\n  transition: none;\n  position: absolute;\n  left: 48rpx;\n  top: 16rpx;\n}\n\n.comment-input:focus {\n  background: #f5f5f5;\n  border: none;\n  outline: none;\n  box-shadow: none;\n  width: calc(100% - 128rpx);\n  height: 72rpx;\n  line-height: 72rpx;\n  padding: 0 32rpx;\n  left: 48rpx;\n  top: 16rpx;\n}\n\n.send-btn {\n  width: 80rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #ccc;\n  white-space: nowrap;\n  position: absolute;\n  right: 0;\n  top: 16rpx;\n}\n\n.send-btn.active {\n  color: #2979ff;\n}\n\n/* 重新设计的评论样式 - 符合帖子详情页风格 */\n.comment-item {\n  display: flex !important;\n  padding: 24rpx 0 !important;\n  margin-bottom: 0 !important;\n  border-bottom: 1rpx solid #f0f0f0 !important;\n  background: transparent !important;\n  border-radius: 0 !important;\n  box-shadow: none !important;\n  border: none !important;\n  transition: none !important;\n}\n\n.comment-item:last-child {\n  border-bottom: none;\n}\n\n.user-avatar {\n  margin-right: 16rpx;\n  flex-shrink: 0;\n  cursor: pointer;\n  transition: opacity 0.2s ease;\n}\n\n.user-avatar:active {\n  opacity: 0.8;\n}\n\n.user-info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12rpx;\n}\n\n.user-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.user-name {\n  display: flex;\n  align-items: center;\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4rpx;\n  cursor: pointer;\n  transition: color 0.2s ease;\n}\n\n.user-name:active {\n  color: #2979ff;\n}\n\n.user-level {\n  font-size: 18rpx;\n  color: white;\n  padding: 2rpx 8rpx;\n  border-radius: 12rpx;\n  margin-left: 12rpx;\n  font-weight: 500;\n  background: #2979ff;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.like-btn {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  background: #f8f9fa;\n  border: 1rpx solid #e4e7ed;\n  transition: all 0.2s ease;\n  min-width: 60rpx;\n  justify-content: center;\n}\n\n.like-btn:active {\n  background: #e4e7ed;\n}\n\n.like-btn text {\n  font-size: 24rpx;\n  color: #666;\n  margin-left: 6rpx;\n  font-weight: 400;\n}\n\n.like-btn.liked {\n  background: #fff0f0;\n  border-color: #ff4757;\n}\n\n.like-btn.liked text {\n  color: #ff4757;\n}\n\n.text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.5;\n  margin-bottom: 16rpx;\n  word-break: break-word;\n}\n\n.expand-btn {\n  color: #2979ff;\n  font-size: 26rpx;\n  margin-top: 8rpx;\n  padding: 4rpx 0;\n  display: inline-block;\n  transition: opacity 0.2s ease;\n}\n\n.expand-btn:active {\n  opacity: 0.7;\n}\n\n.actions {\n  display: flex;\n  align-items: center;\n  gap: 24rpx;\n  margin-top: 12rpx;\n}\n\n.reply-btn {\n  display: flex;\n  align-items: center;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  background: #f8f9fa;\n  border: 1rpx solid #e4e7ed;\n  transition: all 0.2s ease;\n}\n\n.reply-btn:active {\n  background: #e4e7ed;\n}\n\n.reply-btn text {\n  font-size: 24rpx;\n  color: #666;\n  margin-left: 6rpx;\n  font-weight: 400;\n}\n\n.more-btn {\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background: #f8f9fa;\n  transition: all 0.2s ease;\n}\n\n.more-btn:active {\n  background: #e4e7ed;\n}\n\n.reply-preview {\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  padding: 16rpx;\n  margin-top: 16rpx;\n  border: 1rpx solid #f0f0f0;\n}\n\n.reply-item {\n  font-size: 26rpx;\n  margin-bottom: 8rpx;\n  line-height: 1.5;\n  color: #666;\n}\n\n.reply-item:last-child {\n  margin-bottom: 0;\n}\n\n.reply-nickname {\n  color: #2979ff !important;\n  font-weight: 500;\n  cursor: pointer;\n  transition: opacity 0.2s ease;\n}\n\n.reply-nickname:active {\n  opacity: 0.7;\n}\n\n.reply-to {\n  color: #666 !important;\n  font-weight: 400;\n  cursor: pointer;\n  transition: color 0.2s ease;\n}\n\n.reply-to:active {\n  color: #2979ff !important;\n}\n\n.reply-content {\n  color: #666;\n}\n\n.view-more {\n  color: #2979ff;\n  font-size: 26rpx;\n  font-weight: 400;\n  margin-top: 8rpx;\n  padding: 4rpx 0;\n}\n\n.view-more:active {\n  opacity: 0.7;\n}\n\n/* 回复状态指示器 - 符合整体风格 */\n.reply-indicator {\n  background: #f8f9fa;\n  border-bottom: 1rpx solid #e4e7ed;\n  padding: 16rpx 32rpx;\n  opacity: 1;\n  transition: opacity 0.2s ease;\n}\n\n.reply-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.reply-text {\n  font-size: 26rpx;\n  color: #2979ff;\n  font-weight: 400;\n}\n\n.cancel-reply-btn {\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #e4e7ed;\n  border-radius: 50%;\n  font-size: 20rpx;\n  color: #666;\n  font-weight: normal;\n}\n\n.cancel-reply-btn:active {\n  background: #d3d4d6;\n  transform: scale(0.95);\n}\n\n/* 更多操作弹窗 - 简洁风格 */\n.action-popup {\n  padding: 32rpx 0 40rpx;\n  background: white;\n  border-radius: 20rpx 20rpx 0 0;\n}\n\n.popup-header {\n  padding: 0 32rpx 24rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  margin-bottom: 16rpx;\n}\n\n.popup-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.action-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 400;\n  transition: background-color 0.2s ease;\n}\n\n.action-item:active {\n  background: #f8f9fa;\n}\n\n.action-item.delete {\n  color: #ff4757;\n}\n\n.action-item.edit {\n  color: #2979ff;\n}\n\n.action-item.permission {\n  color: #67c23a;\n}\n\n.action-icon {\n  margin-right: 20rpx;\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0722cd1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0722cd1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753773794885\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}