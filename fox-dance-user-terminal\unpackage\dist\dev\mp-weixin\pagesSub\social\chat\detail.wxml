<view class="chat-container data-v-7a033bbb"><view class="header data-v-7a033bbb"><view class="header-content data-v-7a033bbb"><view class="header-left data-v-7a033bbb"><view class="avatar-container data-v-7a033bbb"><u-avatar class="header-avatar data-v-7a033bbb" vue-id="733686aa-1" src="{{otherUserAvatar}}" size="50" data-event-opts="{{[['^click',[['viewUserProfile']]]]}}" bind:click="__e" bind:__l="__l"></u-avatar><block wx:if="{{isOnline}}"><view class="online-indicator data-v-7a033bbb"></view></block></view><view data-event-opts="{{[['tap',[['viewUserProfile',['$event']]]]]}}" class="chat-info data-v-7a033bbb" bindtap="__e"><view class="name-container data-v-7a033bbb"><text class="chat-name data-v-7a033bbb">{{chatName}}</text><view class="connection-status data-v-7a033bbb"><view class="{{['status-dot','data-v-7a033bbb',(isConnected)?'connected':'',(!isConnected)?'disconnected':'']}}"></view><text class="status-text data-v-7a033bbb">{{isConnected?'实时聊天':'连接中...'}}</text></view></view></view></view><view class="header-actions data-v-7a033bbb"><u-icon style="margin-right:16rpx;" vue-id="733686aa-2" name="setting" size="20" color="#666" data-event-opts="{{[['^click',[['showDiagnostic']]]]}}" bind:click="__e" class="data-v-7a033bbb" bind:__l="__l"></u-icon><u-icon vue-id="733686aa-3" name="phone" size="24" color="#333" data-event-opts="{{[['^click',[['makeCall']]]]}}" bind:click="__e" class="data-v-7a033bbb" bind:__l="__l"></u-icon><u-icon vue-id="733686aa-4" name="more-dot-fill" size="24" color="#333" data-event-opts="{{[['^click',[['showMoreActions']]]]}}" bind:click="__e" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view></view></view><scroll-view class="message-list data-v-7a033bbb" scroll-y="{{true}}" scroll-top="{{scrollTop}}" data-event-opts="{{[['scrolltoupper',[['loadMoreMessages',['$event']]]]]}}" bindscrolltoupper="__e"><block wx:for="{{$root.l0}}" wx:for-item="message" wx:for-index="__i0__" wx:key="id"><view class="message-item data-v-7a033bbb"><block wx:if="{{message.$orig.showTime}}"><view class="time-divider data-v-7a033bbb"><text class="time-text data-v-7a033bbb">{{message.m0}}</text></view></block><view class="{{['message-wrapper','data-v-7a033bbb',(message.$orig.isMine)?'is-mine':'']}}"><block wx:if="{{!message.$orig.isMine}}"><u-avatar class="message-avatar data-v-7a033bbb" vue-id="{{'733686aa-5-'+__i0__}}" src="{{message.$orig.avatar}}" size="64" data-event-opts="{{[['^click',[['viewUserProfile']]]]}}" bind:click="__e" bind:__l="__l"></u-avatar><view class="message-content data-v-7a033bbb"><block wx:if="{{message.$orig.type==='text'}}"><view class="message-bubble text-bubble data-v-7a033bbb"><text class="message-text data-v-7a033bbb">{{message.$orig.content}}</text></view></block><block wx:else><block wx:if="{{message.$orig.type==='image'}}"><view data-event-opts="{{[['tap',[['previewImage',['$0'],[[['messageList','id',message.$orig.id,'content']]]]]]]}}" class="message-bubble image-bubble data-v-7a033bbb" bindtap="__e"><image class="message-image data-v-7a033bbb" src="{{message.$orig.content}}" mode="aspectFill"></image></view></block><block wx:else><block wx:if="{{message.$orig.type==='voice'}}"><view data-event-opts="{{[['tap',[['playVoice',['$0'],[[['messageList','id',message.$orig.id]]]]]]]}}" class="message-bubble voice-bubble data-v-7a033bbb" bindtap="__e"><u-icon vue-id="{{'733686aa-6-'+__i0__}}" name="volume-fill" size="16" color="#fff" class="data-v-7a033bbb" bind:__l="__l"></u-icon><text class="voice-duration data-v-7a033bbb">{{message.$orig.duration+"''"}}</text><block wx:if="{{message.$orig.isPlaying}}"><view class="voice-animation data-v-7a033bbb"><view class="wave data-v-7a033bbb"></view><view class="wave data-v-7a033bbb"></view><view class="wave data-v-7a033bbb"></view></view></block></view></block></block></block></view></block><block wx:else><view class="message-content data-v-7a033bbb"><block wx:if="{{message.$orig.type==='text'}}"><view class="message-bubble text-bubble mine data-v-7a033bbb"><text class="message-text data-v-7a033bbb">{{message.$orig.content}}</text></view></block><block wx:else><block wx:if="{{message.$orig.type==='image'}}"><view data-event-opts="{{[['tap',[['previewImage',['$0'],[[['messageList','id',message.$orig.id,'content']]]]]]]}}" class="message-bubble image-bubble data-v-7a033bbb" bindtap="__e"><image class="message-image data-v-7a033bbb" src="{{message.$orig.content}}" mode="aspectFill"></image></view></block><block wx:else><block wx:if="{{message.$orig.type==='voice'}}"><view data-event-opts="{{[['tap',[['playVoice',['$0'],[[['messageList','id',message.$orig.id]]]]]]]}}" class="message-bubble voice-bubble mine data-v-7a033bbb" bindtap="__e"><u-icon vue-id="{{'733686aa-7-'+__i0__}}" name="volume-fill" size="16" color="#fff" class="data-v-7a033bbb" bind:__l="__l"></u-icon><text class="voice-duration data-v-7a033bbb">{{message.$orig.duration+"''"}}</text><block wx:if="{{message.$orig.isPlaying}}"><view class="voice-animation data-v-7a033bbb"><view class="wave data-v-7a033bbb"></view><view class="wave data-v-7a033bbb"></view><view class="wave data-v-7a033bbb"></view></view></block></view></block></block></block><view class="message-status data-v-7a033bbb"><block wx:if="{{message.$orig.status==='sending'}}"><u-icon vue-id="{{'733686aa-8-'+__i0__}}" name="loading" size="12" color="#999" class="data-v-7a033bbb" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{message.$orig.status==='sent'}}"><u-icon vue-id="{{'733686aa-9-'+__i0__}}" name="checkmark" size="12" color="#999" class="data-v-7a033bbb" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{message.$orig.status==='read'}}"><u-icon vue-id="{{'733686aa-10-'+__i0__}}" name="checkmark-done" size="12" color="#2979ff" class="data-v-7a033bbb" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{message.$orig.status==='failed'}}"><u-icon vue-id="{{'733686aa-11-'+__i0__}}" name="close-circle" size="12" color="#ff4757" data-event-opts="{{[['^click',[['resendMessage',['$0'],[[['messageList','id',message.$orig.id]]]]]]]}}" bind:click="__e" class="data-v-7a033bbb" bind:__l="__l"></u-icon></block></block></block></block></view></view></block></view></view></block></scroll-view><view class="input-area data-v-7a033bbb"><block wx:if="{{showExtensions}}"><view class="extensions-panel data-v-7a033bbb"><view class="extension-grid data-v-7a033bbb"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="extension-item data-v-7a033bbb" bindtap="__e"><view class="extension-icon photo data-v-7a033bbb"><u-icon vue-id="733686aa-12" name="camera" color="#fff" size="24" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view><text class="extension-text data-v-7a033bbb">照片</text></view><view data-event-opts="{{[['tap',[['startVoiceRecord',['$event']]]]]}}" class="extension-item data-v-7a033bbb" bindtap="__e"><view class="extension-icon voice data-v-7a033bbb"><u-icon vue-id="733686aa-13" name="mic" color="#fff" size="24" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view><text class="extension-text data-v-7a033bbb">语音</text></view><view data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="extension-item data-v-7a033bbb" bindtap="__e"><view class="extension-icon location data-v-7a033bbb"><u-icon vue-id="733686aa-14" name="map-pin" color="#fff" size="24" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view><text class="extension-text data-v-7a033bbb">位置</text></view><view data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" class="extension-item data-v-7a033bbb" bindtap="__e"><view class="extension-icon file data-v-7a033bbb"><u-icon vue-id="733686aa-15" name="folder" color="#fff" size="24" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view><text class="extension-text data-v-7a033bbb">文件</text></view></view></view></block><block wx:if="{{showEmojis}}"><view class="emoji-panel data-v-7a033bbb"><scroll-view class="emoji-scroll data-v-7a033bbb" scroll-y="{{true}}"><view class="emoji-grid data-v-7a033bbb"><block wx:for="{{emojiList}}" wx:for-item="emoji" wx:for-index="__i1__" wx:key="*this"><text data-event-opts="{{[['tap',[['insertEmoji',['$0'],[[['emojiList','',__i1__]]]]]]]}}" class="emoji-item data-v-7a033bbb" bindtap="__e">{{emoji}}</text></block></view></scroll-view></view></block><view class="{{['input-bar','data-v-7a033bbb',(inputFocused)?'input-focused':'']}}"><block wx:if="{{!inputText&&!voiceMode}}"><view data-event-opts="{{[['tap',[['toggleVoiceMode',['$event']]]]]}}" class="voice-btn data-v-7a033bbb" bindtap="__e"><u-icon vue-id="733686aa-16" name="mic" size="22" color="#666" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view></block><block wx:if="{{!voiceMode}}"><view class="{{['input-wrapper','data-v-7a033bbb',(inputText)?'has-text':'']}}"><textarea class="input-textarea data-v-7a033bbb" placeholder="输入消息..." maxlength="{{500}}" auto-height="{{true}}" cursor-spacing="{{10}}" adjust-position="{{true}}" show-confirm-bar="{{false}}" disable-default-padding="{{true}}" data-event-opts="{{[['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['input',[['__set_model',['','inputText','$event',[]]],['onInputChange',['$event']]]],['linechange',[['onLineChange',['$event']]]],['confirm',[['onInputConfirm',['$event']]]]]}}" value="{{inputText}}" bindfocus="__e" bindblur="__e" bindinput="__e" bindlinechange="__e" bindconfirm="__e"></textarea><block wx:if="{{$root.g0}}"><view class="char-count data-v-7a033bbb"><text class="{{['data-v-7a033bbb',($root.g1>500)?'over-limit':'']}}">{{$root.g2+"/500"}}</text></view></block></view></block><block wx:else><view data-event-opts="{{[['touchstart',[['startRecord',['$event']]]],['touchend',[['stopRecord',['$event']]]],['touchcancel',[['cancelRecord',['$event']]]]]}}" class="{{['voice-record-btn','data-v-7a033bbb',(isRecording)?'recording':'',(!isRecording)?'record-ready':'']}}" bindtouchstart="__e" bindtouchend="__e" bindtouchcancel="__e"><view class="record-icon data-v-7a033bbb"><u-icon vue-id="733686aa-17" name="{{isRecording?'pause-circle-fill':'mic-fill'}}" color="{{isRecording?'#fff':'#666'}}" size="20" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view><text class="record-text data-v-7a033bbb">{{isRecording?'松开发送':'按住说话'}}</text><block wx:if="{{isRecording}}"><view class="record-wave data-v-7a033bbb"><block wx:for="{{3}}" wx:for-item="i" wx:for-index="__i2__" wx:key="*this"><view class="wave-dot data-v-7a033bbb"></view></block></view></block></view></block><view class="input-actions data-v-7a033bbb"><view data-event-opts="{{[['tap',[['toggleEmojis',['$event']]]]]}}" class="action-btn data-v-7a033bbb" bindtap="__e"><u-icon vue-id="733686aa-18" name="emoji" size="22" color="{{showEmojis?'#2979ff':'#666'}}" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view><block wx:if="{{!inputText&&!voiceMode}}"><view data-event-opts="{{[['tap',[['toggleExtensions',['$event']]]]]}}" class="action-btn data-v-7a033bbb" bindtap="__e"><u-icon vue-id="733686aa-19" name="plus" size="22" color="{{showExtensions?'#2979ff':'#666'}}" class="data-v-7a033bbb" bind:__l="__l"></u-icon></view></block><block wx:if="{{inputText||voiceMode}}"><view data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" class="{{['send-btn','data-v-7a033bbb',(canSendMessage)?'can-send':'',(isSending)?'sending':'']}}" bindtap="__e"><block wx:if="{{!isSending}}"><u-icon vue-id="733686aa-20" name="send" color="#fff" size="18" class="data-v-7a033bbb" bind:__l="__l"></u-icon></block><block wx:else><u-loading vue-id="733686aa-21" mode="circle" size="16" color="#fff" class="data-v-7a033bbb" bind:__l="__l"></u-loading></block></view></block></view></view></view></view>