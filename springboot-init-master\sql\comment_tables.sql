-- 评论表
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
    content_id VARCHAR(64) NOT NULL COMMENT '内容ID，如视频ID、文章ID等',
    user_id BIGINT NOT NULL COMMENT '评论用户ID',
    content TEXT NOT NULL COMMENT '评论内容',
    likes INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    reply_count INT NOT NULL DEFAULT 0 COMMENT '回复数量',
    nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    INDEX idx_content(content_id),
    INDEX idx_user(user_id),
    INDEX idx_created_at(created_at)
) COMMENT='评论表' COLLATE=utf8mb4_unicode_ci;
-- comments表添加store字段
ALTER TABLE comments ADD store_id BIGINT DEFAULT NULL COMMENT '店铺ID';
-- 在comments表中添加topic_id字段
ALTER TABLE comments ADD COLUMN topic_id BIGINT COMMENT '话题ID';
-- 在comments表中添加post_id字段
ALTER TABLE comments ADD COLUMN post_id BIGINT COMMENT '帖子ID';
-- 创建索引
ALTER TABLE comments ADD INDEX idx_topic_id(topic_id);

-- 评论回复表
CREATE TABLE IF NOT EXISTS comment_replies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '回复ID',
    comment_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '回复用户ID',
    content TEXT NOT NULL COMMENT '回复内容',
    reply_to_id BIGINT COMMENT '被回复的用户ID，如果直接回复评论则为NULL',
    likes INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    INDEX idx_comment(comment_id),
    INDEX idx_user(user_id),
    INDEX idx_reply_to(reply_to_id),
    INDEX idx_created_at(created_at)
) COMMENT='评论回复表' COLLATE=utf8mb4_unicode_ci;
-- 评论点赞记录表
CREATE TABLE IF NOT EXISTS comment_likes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
    comment_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    UNIQUE KEY uk_comment_user(comment_id, user_id),
    INDEX idx_user(user_id),
    INDEX idx_created_at(created_at)
) COMMENT='评论点赞记录表' COLLATE=utf8mb4_unicode_ci;

-- 回复点赞记录表
CREATE TABLE IF NOT EXISTS reply_likes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
    reply_id BIGINT NOT NULL COMMENT '回复ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    UNIQUE KEY uk_reply_user(reply_id, user_id),
    INDEX idx_user(user_id),
    INDEX idx_created_at(created_at)
) COMMENT='回复点赞记录表' COLLATE=utf8mb4_unicode_ci;

-- 查询数据
select * from ba_user where mobile='17846666041';
select * from ba_user where nickname='8.1长隆体验';
SELECT *  FROM topics WHERE is_delete = 0 AND title like '%123%';