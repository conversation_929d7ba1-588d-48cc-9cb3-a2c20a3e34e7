(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesSub/social/profile/index"],{

/***/ 687:
/*!*************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=4df458ff&scoped=true& */ 688);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 690);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true& */ 692);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 73);

var renderjs





/* normalize component */

var component = Object(_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "4df458ff",
  null,
  false,
  _index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesSub/social/profile/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 688:
/*!********************************************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?vue&type=template&id=4df458ff&scoped=true& ***!
  \********************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4df458ff&scoped=true& */ 689);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_4df458ff_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 689:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?vue&type=template&id=4df458ff&scoped=true& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-icon/u-icon */ "components/uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-icon/u-icon.vue */ 817))
    },
    uAvatar: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-avatar/u-avatar */ "components/uview-ui/components/u-avatar/u-avatar").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-avatar/u-avatar.vue */ 948))
    },
    uTabs: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-tabs/u-tabs */ "components/uview-ui/components/u-tabs/u-tabs").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-tabs/u-tabs.vue */ 927))
    },
    uEmpty: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-empty/u-empty */ "components/uview-ui/components/u-empty/u-empty").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-empty/u-empty.vue */ 970))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.tabs[_vm.currentTab] && _vm.tabs[_vm.currentTab].data.length > 0
  var l0 =
    g0 &&
    !(_vm.currentTab === 0 || _vm.currentTab === 1 || _vm.currentTab === 4) &&
    (_vm.currentTab === 2 || _vm.currentTab === 3)
      ? _vm.__map(_vm.tabs[_vm.currentTab].data, function (user, __i1__) {
          var $orig = _vm.__get_orig(user)
          var a0 = {
            id: user.id,
            nickname: user.nickname,
          }
          return {
            $orig: $orig,
            a0: a0,
          }
        })
      : null
  var m0 = !g0 ? _vm.getEmptyText() : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        m0: m0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 690:
/*!**************************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 691);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 691:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 83));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 85));
var _socialApi = __webpack_require__(/*! @/utils/socialApi.js */ 661);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var PostCard = function PostCard() {
  __webpack_require__.e(/*! require.ensure | pagesSub/social/components/PostCard */ "pagesSub/social/components/PostCard").then((function () {
    return resolve(__webpack_require__(/*! ../components/PostCard.vue */ 941));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var FollowButton = function FollowButton() {
  __webpack_require__.e(/*! require.ensure | pagesSub/social/components/FollowButton */ "pagesSub/social/components/FollowButton").then((function () {
    return resolve(__webpack_require__(/*! ../components/FollowButton.vue */ 955));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  name: "SocialProfile",
  components: {
    PostCard: PostCard,
    FollowButton: FollowButton
  },
  data: function data() {
    return {
      userInfo: {
        userId: "",
        nickname: "",
        avatar: "",
        bio: "",
        socialId: "",
        danceType: "",
        postCount: 0,
        privatePostCount: 0,
        // 私密作品数
        followingCount: 0,
        followersCount: 0,
        likeCount: 0,
        draftCount: 0
      },
      loading: true,
      currentTab: 0,
      isInitialized: false,
      tabs: [{
        name: "作品",
        data: [],
        loading: false
      }, {
        name: "私密作品",
        data: [],
        loading: false
      }, {
        name: "关注",
        data: [],
        loading: false
      }, {
        name: "粉丝",
        data: [],
        loading: false
      }, {
        name: "喜欢",
        data: [],
        loading: false
      }]
    };
  },
  onLoad: function onLoad() {
    var _this = this;
    console.log("Profile页面 onLoad");
    // 延迟加载，确保页面完全初始化
    this.$nextTick(function () {
      setTimeout(function () {
        _this.initializeData();
      }, 100);
    });
  },
  onShow: function onShow() {
    console.log("Profile页面 onShow");
    // 页面显示时刷新数据
    if (this.isInitialized) {
      this.loadUserInfo();
    }
  },
  methods: {
    // 初始化数据
    initializeData: function initializeData() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                console.log("初始化Profile页面数据...");
                _context.prev = 1;
                _context.next = 4;
                return _this2.loadUserInfo();
              case 4:
                _this2.isInitialized = true;
                _context.next = 10;
                break;
              case 7:
                _context.prev = 7;
                _context.t0 = _context["catch"](1);
                console.error("初始化数据失败:", _context.t0);
              case 10:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[1, 7]]);
      }))();
    },
    loadUserInfo: function loadUserInfo() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var currentUserId, result, userProfile;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                // 获取当前用户ID
                currentUserId = _this3.getCurrentUserId();
                if (currentUserId) {
                  _context2.next = 6;
                  break;
                }
                console.error("用户未登录，无法加载用户信息");
                uni.showToast({
                  title: "请先登录",
                  icon: "none"
                });
                return _context2.abrupt("return");
              case 6:
                console.log("开始加载用户信息 - userId:", currentUserId);
                _this3.loading = true;
                _context2.next = 10;
                return (0, _socialApi.getUserProfile)(currentUserId);
              case 10:
                result = _context2.sent;
                console.log("用户信息API返回:", result);
                if (!(result && result.code === 0 && result.data)) {
                  _context2.next = 21;
                  break;
                }
                userProfile = result.data;
                _this3.userInfo = {
                  userId: userProfile.id || userProfile.userId || currentUserId,
                  nickname: userProfile.nickname || "无名氏",
                  avatar: _this3.formatAvatarUrl(userProfile.avatar),
                  bio: userProfile.bio || userProfile.userProfile || "热爱舞蹈，享受生活",
                  socialId: userProfile.socialId,
                  danceType: userProfile.danceType || "街舞",
                  postCount: userProfile.postCount || 0,
                  privatePostCount: 0,
                  // 先设为0，后面会更新
                  followingCount: userProfile.followingCount || 0,
                  followersCount: userProfile.followersCount || userProfile.followerCount || 0,
                  likeCount: userProfile.likeReceivedCount || userProfile.likeCount || 0,
                  draftCount: userProfile.draftCount || 0
                };
                console.log("用户信息加载成功:", _this3.userInfo);

                // 加载私密作品数量
                _context2.next = 18;
                return _this3.loadPrivatePostCount(currentUserId);
              case 18:
                // 加载用户帖子数据
                _this3.loadTabData(_this3.currentTab);
                _context2.next = 23;
                break;
              case 21:
                console.error("用户信息API返回格式不正确:", result);
                uni.showToast({
                  title: "加载用户信息失败",
                  icon: "none"
                });
              case 23:
                _context2.next = 29;
                break;
              case 25:
                _context2.prev = 25;
                _context2.t0 = _context2["catch"](0);
                console.error("加载用户信息失败:", _context2.t0);
                uni.showToast({
                  title: "加载失败，请重试",
                  icon: "none"
                });
              case 29:
                _context2.prev = 29;
                _this3.loading = false;
                return _context2.finish(29);
              case 32:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 25, 29, 32]]);
      }))();
    },
    // 加载私密作品数量
    loadPrivatePostCount: function loadPrivatePostCount(userId) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var result;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return (0, _socialApi.getPostList)({
                  current: 1,
                  size: 1,
                  // 只需要获取数量，不需要具体数据
                  userId: userId,
                  isPublic: 0 // 只查询私密作品
                });
              case 3:
                result = _context3.sent;
                if (result && result.code === 0 && result.data) {
                  // 如果返回的是分页对象，获取total字段
                  if (result.data.total !== undefined) {
                    _this4.userInfo.privatePostCount = result.data.total;
                  } else if (Array.isArray(result.data)) {
                    // 如果返回的是数组，需要再次查询获取准确数量
                    // 这里暂时设为数组长度，实际应该有total字段
                    _this4.userInfo.privatePostCount = result.data.length;
                  }
                }
                console.log("私密作品数量:", _this4.userInfo.privatePostCount);
                _context3.next = 12;
                break;
              case 8:
                _context3.prev = 8;
                _context3.t0 = _context3["catch"](0);
                console.error("加载私密作品数量失败:", _context3.t0);
                _this4.userInfo.privatePostCount = 0;
              case 12:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 8]]);
      }))();
    },
    // 获取当前用户ID
    getCurrentUserId: function getCurrentUserId() {
      var userId = uni.getStorageSync("userid");
      return userId ? Number(userId) : null;
    },
    // 格式化头像URL
    formatAvatarUrl: function formatAvatarUrl(avatar) {
      if (avatar) {
        return "https://file.foxdance.com.cn" + avatar + "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85";
      }
      return "static/images/toux.png";
    },
    loadTabData: function loadTabData(tabIndex) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var data;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!_this5.tabs[tabIndex].loading) {
                  _context4.next = 2;
                  break;
                }
                return _context4.abrupt("return");
              case 2:
                _this5.$set(_this5.tabs[tabIndex], "loading", true);
                _context4.prev = 3;
                data = [];
                _context4.t0 = tabIndex;
                _context4.next = _context4.t0 === 0 ? 8 : _context4.t0 === 1 ? 12 : _context4.t0 === 2 ? 16 : _context4.t0 === 3 ? 20 : _context4.t0 === 4 ? 24 : 28;
                break;
              case 8:
                _context4.next = 10;
                return _this5.loadUserPosts();
              case 10:
                data = _context4.sent;
                return _context4.abrupt("break", 28);
              case 12:
                _context4.next = 14;
                return _this5.loadPrivatePosts();
              case 14:
                data = _context4.sent;
                return _context4.abrupt("break", 28);
              case 16:
                _context4.next = 18;
                return _this5.loadFollowingUsers();
              case 18:
                data = _context4.sent;
                return _context4.abrupt("break", 28);
              case 20:
                _context4.next = 22;
                return _this5.loadFollowersUsers();
              case 22:
                data = _context4.sent;
                return _context4.abrupt("break", 28);
              case 24:
                _context4.next = 26;
                return _this5.loadLikedPosts();
              case 26:
                data = _context4.sent;
                return _context4.abrupt("break", 28);
              case 28:
                _this5.$set(_this5.tabs[tabIndex], "data", data);
                _context4.next = 35;
                break;
              case 31:
                _context4.prev = 31;
                _context4.t1 = _context4["catch"](3);
                console.error("\u52A0\u8F7D\u6807\u7B7E\u9875".concat(tabIndex, "\u6570\u636E\u5931\u8D25:"), _context4.t1);
                _this5.$set(_this5.tabs[tabIndex], "data", []);
              case 35:
                _context4.prev = 35;
                _this5.$set(_this5.tabs[tabIndex], "loading", false);
                return _context4.finish(35);
              case 38:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[3, 31, 35, 38]]);
      }))();
    },
    // 加载用户发布的公开帖子
    loadUserPosts: function loadUserPosts() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var currentUserId, result, posts;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                currentUserId = _this6.getCurrentUserId();
                if (currentUserId) {
                  _context5.next = 5;
                  break;
                }
                console.error("用户未登录，无法加载帖子");
                return _context5.abrupt("return", []);
              case 5:
                _context5.next = 7;
                return (0, _socialApi.getPostList)({
                  current: 1,
                  size: 20,
                  userId: currentUserId,
                  isPublic: 1,
                  // 只加载公开帖子
                  sortField: "createTime",
                  sortOrder: "desc"
                });
              case 7:
                result = _context5.sent;
                console.log("用户帖子API返回:", result);
                if (!(result && result.code === 0 && result.data)) {
                  _context5.next = 14;
                  break;
                }
                posts = Array.isArray(result.data) ? result.data : result.data.records || [];
                return _context5.abrupt("return", posts.map(function (post) {
                  return {
                    id: post.id,
                    title: post.title || "",
                    coverImage: post.coverImage,
                    username: post.nickname || _this6.userInfo.nickname,
                    userAvatar: "https://file.foxdance.com.cn" + post.avatar + "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85",
                    content: post.content,
                    likeCount: post.likeCount || 0,
                    commentCount: post.commentCount || 0,
                    isLiked: post.isLiked || false,
                    isPublic: post.isPublic,
                    // 添加私密状态字段
                    status: post.status,
                    // 添加帖子状态字段
                    createTime: new Date(post.createTime)
                  };
                }));
              case 14:
                console.log("用户帖子API返回格式不正确:", result);
                return _context5.abrupt("return", []);
              case 16:
                _context5.next = 22;
                break;
              case 18:
                _context5.prev = 18;
                _context5.t0 = _context5["catch"](0);
                console.error("加载用户帖子失败:", _context5.t0);
                return _context5.abrupt("return", []);
              case 22:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 18]]);
      }))();
    },
    // 加载用户发布的私密帖子
    loadPrivatePosts: function loadPrivatePosts() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var currentUserId, result, posts;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.prev = 0;
                currentUserId = _this7.getCurrentUserId();
                if (currentUserId) {
                  _context6.next = 5;
                  break;
                }
                console.error("用户未登录，无法加载私密帖子");
                return _context6.abrupt("return", []);
              case 5:
                _context6.next = 7;
                return (0, _socialApi.getPostList)({
                  current: 1,
                  size: 20,
                  userId: currentUserId,
                  isPublic: 0,
                  // 只加载私密帖子
                  sortField: "createTime",
                  sortOrder: "desc"
                });
              case 7:
                result = _context6.sent;
                console.log("用户私密帖子API返回:", result);
                if (!(result && result.code === 0 && result.data)) {
                  _context6.next = 14;
                  break;
                }
                posts = Array.isArray(result.data) ? result.data : result.data.records || [];
                return _context6.abrupt("return", posts.map(function (post) {
                  return {
                    id: post.id,
                    title: post.title || "",
                    coverImage: post.coverImage,
                    username: post.nickname || _this7.userInfo.nickname,
                    userAvatar: "https://file.foxdance.com.cn" + post.avatar,
                    content: post.content,
                    likeCount: post.likeCount || 0,
                    commentCount: post.commentCount || 0,
                    isLiked: post.isLiked || false,
                    isPublic: post.isPublic,
                    // 私密帖子标识
                    status: post.status,
                    createTime: new Date(post.createTime)
                  };
                }));
              case 14:
                console.log("用户私密帖子API返回格式不正确:", result);
                return _context6.abrupt("return", []);
              case 16:
                _context6.next = 22;
                break;
              case 18:
                _context6.prev = 18;
                _context6.t0 = _context6["catch"](0);
                console.error("加载用户私密帖子失败:", _context6.t0);
                return _context6.abrupt("return", []);
              case 22:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[0, 18]]);
      }))();
    },
    // 加载用户点赞的帖子
    loadLikedPosts: function loadLikedPosts() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var currentUserId;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.prev = 0;
                currentUserId = _this8.getCurrentUserId();
                if (currentUserId) {
                  _context7.next = 5;
                  break;
                }
                console.error("用户未登录，无法加载点赞帖子");
                return _context7.abrupt("return", []);
              case 5:
                // TODO: 实现获取用户点赞帖子的API
                console.log("加载用户点赞的帖子 - userId:", currentUserId);
                // 暂时返回空数组，等待后端API实现
                return _context7.abrupt("return", []);
              case 9:
                _context7.prev = 9;
                _context7.t0 = _context7["catch"](0);
                console.error("加载点赞帖子失败:", _context7.t0);
                return _context7.abrupt("return", []);
              case 13:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[0, 9]]);
      }))();
    },
    // 加载关注的用户
    loadFollowingUsers: function loadFollowingUsers() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var currentUserId, result, users;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.prev = 0;
                currentUserId = _this9.getCurrentUserId();
                if (currentUserId) {
                  _context8.next = 5;
                  break;
                }
                console.error("用户未登录，无法加载关注列表");
                return _context8.abrupt("return", []);
              case 5:
                console.log("开始加载关注用户列表...");
                _context8.next = 8;
                return (0, _socialApi.getFollowingList)(currentUserId, {
                  current: 1,
                  size: 50
                });
              case 8:
                result = _context8.sent;
                console.log("关注用户API返回:", result);
                if (!(result && result.code === 0 && result.data)) {
                  _context8.next = 15;
                  break;
                }
                users = Array.isArray(result.data) ? result.data : result.data.records || [];
                return _context8.abrupt("return", users.map(function (user) {
                  return {
                    id: user.userId || user.id,
                    userId: user.userId || user.id,
                    // 确保有userId字段
                    nickname: user.nickname || "用户",
                    avatar: _this9.formatAvatarUrl(user.avatar),
                    bio: user.bio || "暂无简介",
                    danceType: user.danceType || "",
                    followersCount: user.followerCount || 0,
                    isFollowed: true,
                    // 关注列表中的用户都是已关注的
                    type: "user" // 标识这是用户类型数据
                  };
                }));
              case 15:
                console.error("关注用户API返回格式不正确:", result);
                return _context8.abrupt("return", []);
              case 17:
                _context8.next = 23;
                break;
              case 19:
                _context8.prev = 19;
                _context8.t0 = _context8["catch"](0);
                console.error("加载关注用户失败:", _context8.t0);
                return _context8.abrupt("return", []);
              case 23:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8, null, [[0, 19]]);
      }))();
    },
    // 加载粉丝用户
    loadFollowersUsers: function loadFollowersUsers() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var currentUserId, result, users;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.prev = 0;
                currentUserId = _this10.getCurrentUserId();
                if (currentUserId) {
                  _context9.next = 5;
                  break;
                }
                console.error("用户未登录，无法加载粉丝列表");
                return _context9.abrupt("return", []);
              case 5:
                console.log("开始加载粉丝用户列表...");
                _context9.next = 8;
                return (0, _socialApi.getFollowersList)(currentUserId, {
                  current: 1,
                  size: 50
                });
              case 8:
                result = _context9.sent;
                console.log("粉丝用户API返回:", result);
                if (!(result && result.code === 0 && result.data)) {
                  _context9.next = 15;
                  break;
                }
                users = Array.isArray(result.data) ? result.data : result.data.records || [];
                return _context9.abrupt("return", users.map(function (user) {
                  return {
                    id: user.userId || user.id,
                    userId: user.userId || user.id,
                    // 确保有userId字段
                    nickname: user.nickname || "用户",
                    avatar: _this10.formatAvatarUrl(user.avatar),
                    bio: user.bio || "暂无简介",
                    danceType: user.danceType || "",
                    followersCount: user.followerCount || 0,
                    isFollowed: user.isFollowed || false,
                    // 需要检查是否互相关注
                    type: "user" // 标识这是用户类型数据
                  };
                }));
              case 15:
                console.error("粉丝用户API返回格式不正确:", result);
                return _context9.abrupt("return", []);
              case 17:
                _context9.next = 23;
                break;
              case 19:
                _context9.prev = 19;
                _context9.t0 = _context9["catch"](0);
                console.error("加载粉丝用户失败:", _context9.t0);
                return _context9.abrupt("return", []);
              case 23:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[0, 19]]);
      }))();
    },
    switchTab: function switchTab(item) {
      var index = (0, _typeof2.default)(item) === "object" ? item.index : item;
      if (this.currentTab === index) return;
      this.currentTab = index;
      this.loadTabData(index);
    },
    scanCode: function scanCode() {
      uni.scanCode({
        success: function success(res) {
          console.log("扫码结果:", res);
        }
      });
    },
    goSettings: function goSettings() {
      uni.navigateTo({
        url: "/pagesSub/social/settings/index"
      });
    },
    editAvatar: function editAvatar() {
      var _this11 = this;
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: function success(res) {
          // 上传头像
          _this11.userInfo.avatar = res.tempFilePaths[0];
        }
      });
    },
    editProfile: function editProfile() {
      uni.navigateTo({
        url: "/pagesSub/social/profile/edit"
      });
    },
    goLikeList: function goLikeList() {
      uni.navigateTo({
        url: "/pagesSub/social/like/list"
      });
    },
    viewPost: function viewPost(post) {
      uni.navigateTo({
        url: "/pagesSub/social/post/detail?id=".concat(post.id)
      });
    },
    // PostCard组件需要的方法
    goPostDetail: function goPostDetail(post) {
      uni.navigateTo({
        url: "/pagesSub/social/post/detail?id=".concat(post.id)
      });
    },
    goUserProfile: function goUserProfile(data) {
      // 判断是帖子数据还是用户数据
      if (data.type === "user") {
        // 用户数据，直接跳转
        var userId = data.id || data.userId;
        if (userId) {
          uni.navigateTo({
            url: "/pagesSub/social/user/profile?userId=".concat(userId, "&name=").concat(data.nickname)
          });
        }
      } else {
        // 帖子数据，跳转到帖子作者页面
        // 如果是自己的帖子，不需要跳转
        if (data.username === this.userInfo.nickname) return;
        uni.navigateTo({
          url: "/pagesSub/social/user/profile?id=".concat(data.userId || data.id)
        });
      }
    },
    onPostLike: function onPostLike(post) {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var currentTabData, index;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                _context10.prev = 0;
                if (!post.isLiked) {
                  _context10.next = 9;
                  break;
                }
                _context10.next = 4;
                return (0, _socialApi.unlikePost)(post.id);
              case 4:
                post.isLiked = false;
                post.likeCount = Math.max(0, post.likeCount - 1);
                uni.showToast({
                  title: "取消点赞",
                  icon: "none",
                  duration: 1000
                });
                _context10.next = 14;
                break;
              case 9:
                _context10.next = 11;
                return (0, _socialApi.likePost)(post.id);
              case 11:
                post.isLiked = true;
                post.likeCount += 1;
                uni.showToast({
                  title: "点赞成功",
                  icon: "success",
                  duration: 1000
                });
              case 14:
                // 更新对应标签页中的帖子数据
                currentTabData = _this12.tabs[_this12.currentTab].data;
                index = currentTabData.findIndex(function (p) {
                  return p.id === post.id;
                });
                if (index !== -1) {
                  _this12.$set(currentTabData, index, _objectSpread({}, post));
                }
                _context10.next = 23;
                break;
              case 19:
                _context10.prev = 19;
                _context10.t0 = _context10["catch"](0);
                console.error("点赞操作失败:", _context10.t0);
                uni.showToast({
                  title: "操作失败",
                  icon: "none"
                });
              case 23:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[0, 19]]);
      }))();
    },
    // 强制刷新页面数据（供父组件调用）
    forceRefresh: function forceRefresh() {
      console.log("强制刷新个人资料页面数据...");
      // 重置数据状态
      this.isInitialized = false;
      this.loading = true;

      // 清空当前数据
      this.userInfo = {
        userId: "",
        nickname: "",
        avatar: "",
        bio: "",
        danceType: "",
        postCount: 0,
        followingCount: 0,
        followersCount: 0,
        likeCount: 0,
        draftCount: 0
      };

      // 清空tabs数据
      this.tabs.forEach(function (tab) {
        tab.data = [];
        tab.loading = false;
      });

      // 重新初始化数据
      this.initializeData();
    },
    // 获取空状态文本
    getEmptyText: function getEmptyText() {
      var tabNames = ["暂无作品", "暂无私密作品", "暂无关注", "暂无粉丝", "暂无喜欢"];
      return tabNames[this.currentTab] || "暂无内容";
    },
    // 用户关注成功事件
    onUserFollow: function onUserFollow(data) {
      console.log("用户关注成功:", data);
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, true);

      // 更新关注数统计
      this.userInfo.followingCount += 1;
    },
    // 用户取消关注成功事件
    onUserUnfollow: function onUserUnfollow(data) {
      console.log("用户取消关注成功:", data);
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, false);

      // 更新关注数统计
      if (this.userInfo.followingCount > 0) {
        this.userInfo.followingCount -= 1;
      }
    },
    // 关注状态变化事件
    onFollowChange: function onFollowChange(data) {
      console.log("关注状态变化:", data);
      // 更新本地数据
      this.updateUserFollowStatus(data.user.id, data.isFollowed);
    },
    // 更新用户关注状态的辅助方法
    updateUserFollowStatus: function updateUserFollowStatus(userId, isFollowed) {
      // 更新关注列表中的状态
      if (this.tabs[2] && this.tabs[2].data) {
        var followingUser = this.tabs[2].data.find(function (u) {
          return u.id === userId;
        });
        if (followingUser) {
          followingUser.isFollowed = isFollowed;
        }
      }

      // 更新粉丝列表中的状态
      if (this.tabs[3] && this.tabs[3].data) {
        var followerUser = this.tabs[3].data.find(function (u) {
          return u.id === userId;
        });
        if (followerUser) {
          followerUser.isFollowed = isFollowed;
        }
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 692:
/*!***********************************************************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true& ***!
  \***********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true& */ 693);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_4df458ff_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 693:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/profile/index.vue?vue&type=style&index=0&id=4df458ff&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ 723:
/*!**********************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/main.js?{"page":"pagesSub%2Fsocial%2Fprofile%2Findex"} ***!
  \**********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pagesSub/social/profile/index.vue */ 687));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ })

},[[723,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesSub/social/profile/index.js.map