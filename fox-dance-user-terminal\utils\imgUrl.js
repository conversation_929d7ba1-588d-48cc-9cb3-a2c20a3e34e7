export function imgUrlForWebp(img) {
  // 校验img存在
  if (img != null) {
    return (
      "https://file.foxdance.com.cn" +
      img +
      "?imageMogr2/format/webp/quality/85"
    );
  }
  return "/static/image/toux.png";
}

export function imgUrlForThumbnail(img) {
  // 校验img存在
  if (img != null) {
    return (
      "https://file.foxdance.com.cn" +
      img +
      "?imageMogr2/format/webp/quality/85?imageMogr2/thumbnail/300x300>/format/webp/quality/85"
    );
  }
  return "/static/image/toux.png";
}
