{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/weeksUserReport.vue?8eb2", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/weeksUserReport.vue?b5f7", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/weeksUserReport.vue?1185", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/weeksUserReport.vue?750a", "uni-app:///pages/mine/userReport/weeksUserReport.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/weeksUserReport.vue?9e2e", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pages/mine/userReport/weeksUserReport.vue?c83c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgbaseUrl", "imgbaseUrlOss", "safeAreaTop", "menuButtonInfoHeight", "loding", "isLogined", "swiperIndex", "configDate", "oneAni1", "oneAni2", "oneAni3", "oneAni4", "twoAni0", "twoAni1", "twoAni2", "twoAni3", "twoAni4", "thrAni1", "thrAni2", "thrAni3", "thrAni4", "thrAni5", "fouAni1", "fouAni2", "fouAni3", "fouAni4", "fiveAni1", "fiveAni2", "fiveAni3", "pmNum", "userReport", "onShow", "onLoad", "methods", "weekUserReportData", "uni", "title", "id", "console", "res", "that", "cshData", "setTimeout", "swiper<PERSON><PERSON>e", "swiperEnd", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoF5wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;MAEA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;;EACAC;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QAAAC;MAAA;QACAC;QACA;UACAH;UACA;AACA;AACA;AACA;AACA;;UAGA;YACA;YACA;YACA;cACAI;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;YACA;UACA;;UACAC;UACAA;UACAF;UACAE;UACAA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAD;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;MAEA;QACAH;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;MAEA;QACA;UACAA;UACAE;YACAF;UACA;UACAE;YACAF;UACA;UACAE;YACAF;UACA;UACAE;YACAF;UACA;QACA;MACA;;MAKA;MACA;QACAA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;;MAEA;MACA;QACAA;QACAE;UACAF;QACA;QACAE;UACAF;QACA;MACA;IAEA;IACA;IACAI;MACA;IAAA,CACA;IACAC;MACAV;QACAW;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACtRA;AAAA;AAAA;AAAA;AAAm5C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACAv6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/userReport/weeksUserReport.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/userReport/weeksUserReport.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./weeksUserReport.vue?vue&type=template&id=59cb20e1&\"\nvar renderjs\nimport script from \"./weeksUserReport.vue?vue&type=script&lang=js&\"\nexport * from \"./weeksUserReport.vue?vue&type=script&lang=js&\"\nimport style0 from \"./weeksUserReport.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/userReport/weeksUserReport.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weeksUserReport.vue?vue&type=template&id=59cb20e1&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-navbar/u-navbar\" */ \"@/components/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.swiperIndex = 1\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weeksUserReport.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weeksUserReport.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"userReport\" v-if=\"loding\">\r\n\t\t\r\n\t\t<u-navbar :back-icon-color=\"swiperIndex == 0 || swiperIndex == 1 ? '#fff' : '#333'\" back-icon-size=\"42\" title=\"用户报告\" background=\"none\" :border-bottom=\"false\" :title-color=\"swiperIndex == 0 || swiperIndex == 1 ? '#fff' : '#333'\" title-size=\"32\">\r\n\t\t</u-navbar>\r\n\t\t\r\n\t\t<view class=\"yueb weeks_yueb\">\r\n\t\t\t<swiper class=\"swiper\" :current=\"swiperIndex\" :vertical=\"true\" @change=\"swiperChange\" @animationfinish=\"swiperEnd\">\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item weeks_one\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon12.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<view class=\"weeks_one_a animate__animated animate__bounceInDown\" v-if=\"configDate.oneAni1\"><text>你的</text><text>FOX周报</text></view>\r\n\t\t\t\t\t\t<view class=\"weeks_one_b animate__animated animate__bounceInLeft\" v-if=\"configDate.oneAni2\"></view>\r\n\t\t\t\t\t\t<view class=\"weeks_one_c animate__animated animate__bounceInUp\" v-if=\"configDate.oneAni3\"><text>WORLD</text><text>THEATRE DAY</text></view>\r\n\t\t\t\t\t\t<view class=\"weeks_one_d animate__animated animate__zoomInDown\" v-if=\"configDate.oneAni4\" @click=\"swiperIndex = 1\">开启旅程</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item weeks_two\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon13.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t  \r\n\t\t\t\t\t\t<view class=\"weeks_two_n\">\r\n\t\t\t\t\t\t\t<view class=\"weeks_two_a animate__animated animate__rollIn\" v-if=\"configDate.twoAni0\">这一周，你在FOX的线上课累计观</view>\r\n\t\t\t\t\t\t\t<view class=\"weeks_two_a animate__animated animate__rollIn\" v-if=\"configDate.twoAni1\">看了<text>{{userReport.cloud_time*1}}</text>分钟，足足超过</view>\r\n\t\t\t\t\t\t\t<view class=\"weeks_two_a animate__animated animate__rollIn\" v-if=\"configDate.twoAni2\"><text>{{userReport.over_ratio*1}}%</text>的用户。</view>\r\n\t\t\t\t\t\t\t<view class=\"weeks_two_a animate__animated animate__rollIn\" v-if=\"configDate.twoAni3\">你不仅在舞蹈教室舞动自如，连家</view>\r\n\t\t\t\t\t\t\t<view class=\"weeks_two_a animate__animated animate__rollIn\" v-if=\"configDate.twoAni4\">里的客厅都成了你的练舞场!</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item v-if=\"userReport.invite_num*1 > 0\">\r\n\t\t\t\t\t<view class=\"swiper-item weeks_thr\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon14.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<view class=\"weeks_thr_a\">\r\n\t\t\t\t\t\t\t<view class=\"animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.thrAni1\">你太会分享了!</view>\r\n\t\t\t\t\t\t\t<view class=\"animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.thrAni2\">这一周你成功邀请了<text>{{userReport.invite_num*1}}</text>位 新朋友加入FOX</view>\r\n\t\t\t\t\t\t\t<view class=\"animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.thrAni3\">共收获了<text>{{userReport.get_points}}</text>积分</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"weeks_thr_b animate__animated animate__bounceInLeft\" v-if=\"configDate.thrAni4\"></view>\r\n\t\t\t\t\t\t<view class=\"weeks_thr_c animate__animated animate__fadeInUp\" v-if=\"configDate.thrAni5\">看来你不仅自己跳舞，还带着朋友们一起在舞蹈的世界里挥酒热情</view>\r\n\t\t\t\t\t\t  \r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item weeks_fou\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon15.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\r\n\t\t\t\t\t\t<view class=\"weeks_fou_a animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.fouAni1\">在FOX的刷课榜单上：</view>\r\n\t\t\t\t\t\t<view class=\"weeks_fou_b animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.fouAni2\">你本周位列第<text>{{pmNum}}</text>名</view>\r\n\t\t\t\t\t\t<view class=\"weeks_fou_c animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.fouAni3\"></view>\r\n\t\t\t\t\t\t<view class=\"weeks_fou_d animate__animated animate__lightSpeedInLeft\" v-if=\"configDate.fouAni4\">\r\n\t\t\t\t\t\t\t{{pmNum == 1 ? '你是步步生花”的舞神，每一步都绽放出惊艳的花朵，你的天赋与努力让你在排行榜上高居第一，无可替代!' : (pmNum > 1 && pmNum <= 10) ? '稳步向前。继续保持，下一定能夺冠！' : (pmNum > 10 && pmNum < 101) ? '稳步向前。继续保持，下一周冲进前10不是梦!' : (pmNum >= 101) ? '稳步向前。继续保持，下一周冲进前100不是梦!' : ''}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"swiper-item weeks_five\">\r\n\t\t\t\t\t\t<view :style=\"'height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%'\"></view>\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon16.png'\" mode=\"aspectFill\" class=\"years_one_bj\"></image>\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"weeks_five_a animate__animated animate__flipInX\" v-if=\"configDate.fiveAni1 && userReport.last_time && userReport.last_time.type == 0\">你最晚的一次线上练习是在 下午<text>{{userReport.last_time.last_time}}</text>和照的阳光下，我知道，你在辛勤的耕耘，等待胜利的果实</view>\r\n\t\t\t\t\t\t<view class=\"weeks_five_a animate__animated animate__flipInX\" v-if=\"configDate.fiveAni1 && userReport.last_time && userReport.last_time.type == 1\">你最晚的一次线上练习是在 晚上<text>{{userReport.last_time.last_time}}</text>看来深夜也是你的舞蹈时刻。这个时间点还在舞品,简直是灵魂夜猫子的最佳写照</view>\r\n\t\t\t\t\t\t<view class=\"weeks_five_a animate__animated animate__flipInX\" v-if=\"configDate.fiveAni1 && userReport.last_time && userReport.last_time.type == 2\">你最早的一次线上练习是在 上午<text>{{userReport.last_time.last_time}}</text>,伴着晨光，我知道，你在辛勤的耕耘，等待胜利的果实</view>\r\n\t\t\t\t\t\t<view class=\"weeks_five_a animate__animated animate__flipInX\" v-if=\"configDate.fiveAni1 && !userReport.last_time\">本周你还没有线上练习，躺在床上很舒服，但跳起来更有成就感，希望看到下周活力焕发的你!</view>\r\n\t\t\t\t\t\t<view class=\"weeks_five_b animate__animated animate__fadeInLeft\" v-if=\"configDate.fiveAni2\"></view>\r\n\t\t\t\t\t\t<view class=\"weeks_five_a animate__animated animate__flipInX\" v-if=\"configDate.fiveAni3\">Persistence is a necessary quality for the achievement of heroes.</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"userd1 animate__animated animate__bounce\">搜索</view>\r\n\t\t\r\n\t\t<view class=\"userd1 animate__animated animate__fadeInLeftBig\">搜索</view> -->\r\n\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tweekReportApi,\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\timgbaseUrlOss:'',\r\n\t\t\tsafeAreaTop:wx.getWindowInfo().safeArea.top,\r\n\t\t\tmenuButtonInfoHeight:uni.getMenuButtonBoundingClientRect().height,\r\n\t\t\tloding:false,\r\n\t\t\tisLogined:true,\r\n\t\t\tswiperIndex:0,\r\n\t\t\tconfigDate:{\r\n\t\t\t\toneAni1:false,\r\n\t\t\t\toneAni2:false,\r\n\t\t\t\toneAni3:false,\r\n\t\t\t\toneAni4:false,\r\n\t\t\t\t\r\n\t\t\t\ttwoAni0:false,\r\n\t\t\t\ttwoAni1:false,\r\n\t\t\t\ttwoAni2:false,\r\n\t\t\t\ttwoAni3:false,\r\n\t\t\t\ttwoAni4:false,\r\n\t\t\t\t\r\n\t\t\t\tthrAni1:false,\r\n\t\t\t\tthrAni2:false,\r\n\t\t\t\tthrAni3:false,\r\n\t\t\t\tthrAni4:false,\r\n\t\t\t\tthrAni5:false,\r\n\t\t\t\t\r\n\t\t\t\tfouAni1:false,\r\n\t\t\t\tfouAni2:false,\r\n\t\t\t\tfouAni3:false,\r\n\t\t\t\tfouAni4:false,\r\n\t\t\t\t\r\n\t\t\t\tfiveAni1:false,\r\n\t\t\t\tfiveAni2:false,\r\n\t\t\t\tfiveAni3:false,\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tpmNum:0,\r\n\t\t\tuserReport:{}\r\n\t\t}\r\n\t},\r\n\tonShow: function(){\r\n\t   this.imgbaseUrl = this.$baseUrl;\r\n\t   this.imgbaseUrlOss = this.$baseUrlOss;\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.weekUserReportData(option.id);//周报\r\n\t},\r\n\tmethods: {\r\n\t\t//周报\r\n\t\tweekUserReportData(id){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tweekReportApi({id:id}).then(res => {\r\n\t\t\t\tconsole.log('周报1',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t/*// res.data.invite_num = 2\r\n\t\t\t\t\tres.data.last_time =  {\r\n\t\t\t\t\t\t\"last_time\": \"10:22\",\r\n\t\t\t\t\t\t\"create_time\": 1734142950\r\n\t\t\t\t\t}*/\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(res.data.last_time){\r\n\t\t\t\t\t\tvar date = new Date(Number(res.data.last_time.create_time + '000'));\r\n\t\t\t\t\t\tvar hours = date.getHours();\r\n\t\t\t\t\t\tif (hours >= 12 && hours < 18) {\r\n\t\t\t\t\t\t\tres.data.last_time.type = 0\r\n\t\t\t\t\t\t\t// console.log(\"属于中午12:00~下午5:59这个时间段---\");\r\n\t\t\t\t\t\t} else if ((hours >= 18 && hours <= 23) || (hours >= 0 && hours < 3)) {\r\n\t\t\t\t\t\t\tres.data.last_time.type = 1\r\n\t\t\t\t\t\t\t// console.log(\"属于晚上6:00~凌晨2:59这个时间段\");\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tres.data.last_time.type = 2\r\n\t\t\t\t\t\t\t// console.log(\"属于凌晨3:00~中午11:59这个时间段\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.pmNum = res.data.rank*1;\r\n\t\t\t\t\tthat.userReport = res.data;\r\n\t\t\t\t\tconsole.log('周报2',that.userReport);\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.cshData()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//初始化\r\n\t\tcshData(){\r\n\t\t\tvar that = this;\r\n\t\t\tif(this.swiperIndex == 0){\r\n\t\t\t\tthat.configDate.oneAni1 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.oneAni2 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.oneAni3 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.oneAni4 = true;\r\n\t\t\t\t},3000);\r\n\t\t\t}\r\n\t\t},\r\n\t\t//监听swiper\r\n\t\tswiperChange(e){\r\n\t\t\tvar that = this;\r\n\t\t\t// console.log(e,'监听swiper')\r\n\t\t\tthis.swiperIndex = e.detail.current\r\n\t\t\t\r\n\t\t\tif(this.swiperIndex == 1){\r\n\t\t\t\tthat.configDate.twoAni0 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni1 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni2 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni3 = true;\r\n\t\t\t\t},3000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.twoAni4 = true;\r\n\t\t\t\t},4000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(this.userReport.invite_num*1 > 0){\r\n\t\t\t\tif(this.swiperIndex == 2){\r\n\t\t\t\t\tthat.configDate.thrAni1 = true;\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthat.configDate.thrAni2 = true;\r\n\t\t\t\t\t},1000);\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthat.configDate.thrAni3 = true;\r\n\t\t\t\t\t},2000);\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthat.configDate.thrAni4 = true;\r\n\t\t\t\t\t},3000);\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tthat.configDate.thrAni5 = true;\r\n\t\t\t\t\t},4000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t// if(this.swiperIndex == 3){\r\n\t\t\tif(this.swiperIndex == (this.userReport.invite_num*1 > 0 ? 3 : 2)){\r\n\t\t\t\tthat.configDate.fouAni1 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni2 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni3 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni4 = true;\r\n\t\t\t\t},3000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni5 = true;\r\n\t\t\t\t},4000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fouAni6 = true;\r\n\t\t\t\t},5000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// if(this.swiperIndex == 4){\r\n\t\t\tif(this.swiperIndex == (this.userReport.invite_num*1 > 0 ? 4 : 3)){\r\n\t\t\t\tthat.configDate.fiveAni1 = true;\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fiveAni2 = true;\r\n\t\t\t\t},1000);\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.configDate.fiveAni3 = true;\r\n\t\t\t\t},2000);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\t//动画结束时会触发\r\n\t\tswiperEnd(e){\r\n\t\t\t// console.log(e,'动画结束时会触发')\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\r\n.userReport{-overflow: hidden;}\r\npage{padding-bottom: 0;background:#fff;}\r\n.userd1{\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tbackground:red;\r\n\tmargin: auto;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weeksUserReport.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./weeksUserReport.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753760725008\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}